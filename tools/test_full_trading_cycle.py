#!/usr/bin/env python3
"""
完整交易流程模拟测试工具
Full Trading Cycle Simulation Test Tool

模拟完整的交易生命周期：信号生成 → 开仓 → 钉钉通知 → 平仓 → 钉钉通知
测试BTCUSDC符号的正常交易流程
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.symbol_manager import symbol_manager
from quant.binance_client import binance_client
from quant.strategies.auto_trader import auto_trader
from quant.notification_manager import notification_manager
from quant.utils.logger import get_logger
from quant.services.market_analysis_service import MarketAnalysisService

logger = get_logger(__name__)


class FullTradingCycleTest:
    """完整交易流程测试器"""
    
    def __init__(self):
        self.test_results = {
            "signal_generation": False,
            "order_placement": False,
            "open_notification": False,
            "position_tracking": False,
            "close_signal": False,
            "close_order": False,
            "close_notification": False,
            "database_records": False
        }
        self.test_trade_id = None
        
    def print_section(self, title: str):
        """打印测试节标题"""
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
        
    def print_result(self, test_name: str, status: bool, details: str = ""):
        """打印测试结果"""
        icon = "✅" if status else "❌"
        print(f"{icon} {test_name}: {'PASS' if status else 'FAIL'}")
        if details:
            print(f"   {details}")
        return status
    
    async def test_system_readiness(self) -> bool:
        """测试系统准备状态"""
        self.print_section("系统准备状态检查")
        
        try:
            # 检查符号管理器
            current_symbol = symbol_manager.get_current_symbol()
            if not self.print_result("符号管理器状态", current_symbol == "BTCUSDC", f"当前符号: {current_symbol}"):
                return False
                
            # 检查AutoTrader状态
            auto_trader_ready = auto_trader.enabled and not auto_trader.emergency_stop
            if not self.print_result("自动交易器状态", auto_trader_ready, f"启用: {auto_trader.enabled}, 紧急停止: {auto_trader.emergency_stop}"):
                return False
                
            # 检查数据库连接
            try:
                with db.get_session() as session:
                    from sqlalchemy import text
                    result = session.execute(text("SELECT COUNT(*) FROM trade_history")).fetchone()
                    self.print_result("数据库连接", True, f"当前交易记录: {result[0]}条")
            except Exception as e:
                self.print_result("数据库连接", False, f"连接失败: {e}")
                return False
                
            print(f"\n🎯 系统准备完毕，开始完整交易流程测试...")
            return True
            
        except Exception as e:
            self.print_result("系统准备检查", False, f"检查失败: {e}")
            return False
    
    async def create_mock_tradable_signal(self) -> dict:
        """创建模拟的可交易信号"""
        self.print_section("创建模拟可交易信号")
        
        # 获取当前价格用于创建真实的信号
        try:
            current_price = await binance_client.get_current_price("BTCUSDC")
            print(f"📊 当前BTCUSDC价格: ${current_price:,.2f}")
        except Exception as e:
            print(f"⚠️ 获取价格失败，使用模拟价格: {e}")
            current_price = 60000.0
        
        # 创建高置信度的LONG信号
        mock_signal = {
            'signal_timestamp': datetime.now().isoformat() + 'Z',
            'symbol': 'BTCUSDC',  # 使用新的符号
            'direction': 'LONG',
            'entry_price': current_price,
            'confidence_score': 0.85,  # 高置信度确保可交易
            'market_state': 'bullish_test',
            'trigger_pattern': 'simulation_test_pattern',
            'confirmed_indicators': ['rsi_bullish', 'macd_golden_cross', 'volume_surge'],
            'suggested_bet': 100.0,  # 适中的下注金额
            'trading_suspended': False,
            'analysis_only': False,
            'decision_details': {
                'indicators': ['rsi', 'macd', 'volume'],
                'strength': 'strong',
                'risk_level': 'medium',
                'market_condition': 'favorable'
            },
            'test_mode': True  # 标记为测试信号
        }
        
        print(f"🧪 模拟信号创建成功:")
        print(f"   符号: {mock_signal['symbol']}")
        print(f"   方向: {mock_signal['direction']}")
        print(f"   入场价: ${mock_signal['entry_price']:,.2f}")
        print(f"   置信度: {mock_signal['confidence_score']:.3f}")
        print(f"   建议下注: ${mock_signal['suggested_bet']}")
        
        self.test_results["signal_generation"] = True
        return mock_signal
    
    async def test_order_placement(self, signal: dict) -> bool:
        """测试订单执行"""
        self.print_section("测试开仓订单执行")
        
        try:
            # 通过AutoTrader处理信号
            result = await auto_trader.handle_new_signal(signal)
            
            if result.success:
                self.test_trade_id = result.trade_id
                self.test_results["order_placement"] = True
                return self.print_result("开仓订单执行", True, f"交易ID: {self.test_trade_id}")
            else:
                return self.print_result("开仓订单执行", False, f"执行失败: {result.message}")
                
        except Exception as e:
            return self.print_result("开仓订单执行", False, f"异常: {e}")
    
    async def test_notification_system(self, trade_id: int, notification_type: str) -> bool:
        """测试钉钉通知系统"""
        self.print_section(f"测试{notification_type}钉钉通知")
        
        try:
            # 获取交易记录
            with db.get_session() as session:
                from quant.database_manager import TradeHistory
                trade = session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()
                
                if not trade:
                    return self.print_result(f"{notification_type}通知", False, "未找到交易记录")
            
            # 提取需要的数据，避免session绑定问题
            trade_data = {
                'symbol': trade.symbol,
                'direction': trade.direction,
                'entry_price': trade.entry_price,
                'exit_price': trade.exit_price,
                'suggested_bet': trade.suggested_bet,
                'pnl': trade.pnl
            }
            
            # 构造通知消息
            if notification_type == "开仓":
                message = f"""
🚀 **自动开仓执行** (测试)
📈 交易对: {trade_data['symbol']}
📊 方向: {trade_data['direction']}
💰 入场价: ${trade_data['entry_price']:,.2f}
🎯 下注金额: ${trade_data['suggested_bet']}
⏰ 时间: {datetime.now().strftime('%H:%M:%S')}
🔖 交易ID: {trade_id}
"""
            else:  # 平仓
                message = f"""
🏁 **自动平仓执行** (测试)
📈 交易对: {trade_data['symbol']}
📊 方向: {trade_data['direction']}
💰 平仓价: ${trade_data['exit_price'] or 0:,.2f}
💸 盈亏: ${trade_data['pnl'] or 0:.2f}
⏰ 时间: {datetime.now().strftime('%H:%M:%S')}
🔖 交易ID: {trade_id}
"""
            
            # 发送测试通知
            success = await notification_manager.send_trading_alert(
                title=f"交易系统{notification_type}通知(测试)",
                content=message.strip(),
                level="INFO"
            )
            
            if notification_type == "开仓":
                self.test_results["open_notification"] = success
            else:
                self.test_results["close_notification"] = success
                
            return self.print_result(f"{notification_type}钉钉通知", success, 
                                   "通知发送成功" if success else "通知发送失败")
            
        except Exception as e:
            return self.print_result(f"{notification_type}钉钉通知", False, f"异常: {e}")
    
    async def simulate_position_close(self, trade_id: int) -> bool:
        """模拟平仓操作"""
        self.print_section("模拟平仓操作")
        
        try:
            # 获取交易记录
            with db.get_session() as session:
                from quant.database_manager import TradeHistory
                from sqlalchemy import text
                
                # 获取交易信息
                trade = session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()
                if not trade:
                    return self.print_result("获取交易记录", False, "交易记录不存在")
                
                # 模拟获取当前价格并计算盈亏
                try:
                    current_price = await binance_client.get_current_price(trade.symbol)
                    print(f"📊 当前价格: ${current_price:,.2f}")
                except Exception as e:
                    print(f"⚠️ 使用模拟价格: {e}")
                    # 模拟盈利场景 - 价格上涨2%
                    current_price = trade.entry_price * 1.02
                
                # 计算盈亏
                if trade.direction == "LONG":
                    pnl = (current_price - trade.entry_price) / trade.entry_price * trade.suggested_bet
                else:
                    pnl = (trade.entry_price - current_price) / trade.entry_price * trade.suggested_bet
                
                # 更新交易记录为已平仓
                update_query = text("""
                    UPDATE trade_history 
                    SET exit_price = :exit_price,
                        exit_timestamp = :exit_timestamp,
                        pnl = :pnl,
                        status = 'WIN'
                    WHERE id = :trade_id
                """)
                
                session.execute(update_query, {
                    'exit_price': current_price,
                    'exit_timestamp': datetime.now(),
                    'pnl': pnl,
                    'trade_id': trade_id
                })
                session.commit()
                
                self.test_results["close_signal"] = True
                self.test_results["close_order"] = True
                
                return self.print_result("模拟平仓", True, 
                                       f"平仓价: ${current_price:,.2f}, 盈亏: ${pnl:.2f}")
                
        except Exception as e:
            return self.print_result("模拟平仓", False, f"异常: {e}")
    
    async def verify_database_records(self, trade_id: int) -> bool:
        """验证数据库记录完整性"""
        self.print_section("验证数据库记录")
        
        try:
            with db.get_session() as session:
                from quant.database_manager import TradeHistory
                
                trade = session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()
                
                if not trade:
                    return self.print_result("数据库记录", False, "交易记录不存在")
                
                print(f"📋 交易记录验证:")
                print(f"   交易ID: {trade.id}")
                print(f"   符号: {trade.symbol}")
                print(f"   方向: {trade.direction}")
                print(f"   入场价: ${trade.entry_price:,.2f}")
                print(f"   出场价: ${trade.exit_price:,.2f}" if trade.exit_price else "   出场价: 未设置")
                print(f"   盈亏: ${trade.pnl:.2f}" if trade.pnl else "   盈亏: 未计算")
                print(f"   状态: {trade.status}")
                print(f"   信号时间: {trade.signal_timestamp}")
                print(f"   平仓时间: {trade.exit_timestamp}" if trade.exit_timestamp else "   平仓时间: 未设置")
                
                # 检查关键字段
                checks = [
                    ("符号正确", trade.symbol == "BTCUSDC"),
                    ("有入场价", trade.entry_price is not None and trade.entry_price > 0),
                    ("有出场价", trade.exit_price is not None and trade.exit_price > 0),
                    ("有盈亏计算", trade.pnl is not None),
                    ("状态已更新", trade.status in ["WIN", "LOSS"]),
                ]
                
                all_passed = True
                for check_name, check_result in checks:
                    if not self.print_result(f"  {check_name}", check_result):
                        all_passed = False
                
                self.test_results["database_records"] = all_passed
                return all_passed
                
        except Exception as e:
            return self.print_result("数据库记录验证", False, f"异常: {e}")
    
    async def run_full_test(self) -> bool:
        """运行完整的交易流程测试"""
        print("🔧 完整交易流程模拟测试工具")
        print("=" * 60)
        print("测试流程: 信号生成 → 开仓 → 钉钉通知 → 平仓 → 钉钉通知")
        
        try:
            # 1. 系统准备检查
            if not await self.test_system_readiness():
                return False
            
            # 2. 创建模拟信号
            signal = await self.create_mock_tradable_signal()
            
            # 3. 测试开仓
            if not await self.test_order_placement(signal):
                return False
            
            # 4. 测试开仓通知
            await self.test_notification_system(self.test_trade_id, "开仓")
            
            # 等待一下，模拟交易持续时间
            print(f"\n⏱️ 等待3秒模拟交易持续时间...")
            await asyncio.sleep(3)
            
            # 5. 模拟平仓
            if not await self.simulate_position_close(self.test_trade_id):
                return False
            
            # 6. 测试平仓通知
            await self.test_notification_system(self.test_trade_id, "平仓")
            
            # 7. 验证数据库记录
            await self.verify_database_records(self.test_trade_id)
            
            # 生成测试报告
            await self.generate_test_report()
            
            return True
            
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def generate_test_report(self):
        """生成测试报告"""
        self.print_section("测试报告")
        
        passed_tests = sum(1 for result in self.test_results.values() if result)
        total_tests = len(self.test_results)
        
        print(f"📊 测试结果统计:")
        for test_name, result in self.test_results.items():
            icon = "✅" if result else "❌"
            print(f"   {icon} {test_name}")
        
        print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！BTCUSDC交易流程完全正常。")
        else:
            print("⚠️ 部分测试失败，需要进一步检查。")
        
        if self.test_trade_id:
            print(f"🔖 测试交易ID: {self.test_trade_id}")
            print("💡 建议: 可在数据库中查看完整的交易记录")


async def main():
    """主函数"""
    tester = FullTradingCycleTest()
    success = await tester.run_full_test()
    return 0 if success else 1


if __name__ == "__main__":
    exit(asyncio.run(main()))