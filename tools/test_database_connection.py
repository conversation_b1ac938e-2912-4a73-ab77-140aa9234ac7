#!/usr/bin/env python3
"""
数据库连接测试工具
Test Database Connection Tool

测试系统运行时的实际数据库连接路径和状态
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_database_paths():
    """测试数据库路径和连接状态"""
    print("🔍 数据库连接状态测试")
    print("=" * 60)
    
    try:
        # 测试全局数据库实例
        from quant.database_manager import db
        
        print(f"📊 全局数据库实例信息:")
        print(f"   数据库路径: {db.db_path}")
        print(f"   完整路径: {db.db_path.resolve()}")
        print(f"   文件存在: {db.db_path.exists()}")
        if db.db_path.exists():
            print(f"   文件大小: {db.db_path.stat().st_size} bytes")
        
        # 测试数据库连接
        try:
            with db.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table'")).fetchall()
                tables = [r[0] for r in result]
                print(f"   可用表: {tables}")
                
                if 'trade_history' in tables:
                    count_result = session.execute(text("SELECT COUNT(*) FROM trade_history")).fetchone()
                    print(f"   trade_history记录数: {count_result[0]}")
                    
                    # 测试ORM查询
                    from quant.database_manager import TradeHistory
                    trades = session.query(TradeHistory).limit(1).all()
                    print(f"   ORM查询成功: 找到{len(trades)}条记录")
                else:
                    print("   ❌ trade_history表不存在")
                    
        except Exception as e:
            print(f"   ❌ 数据库连接失败: {e}")
            return False
            
        # 测试pending trades查询
        try:
            pending_trades = db.get_pending_trades()
            print(f"   待结算交易: {len(pending_trades)}条")
        except Exception as e:
            print(f"   ❌ get_pending_trades失败: {e}")
            return False
            
        print("\n✅ 数据库连接测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_engine_path():
    """检查SQLAlchemy引擎的实际路径"""
    try:
        from quant.database_manager import db
        from sqlalchemy import text
        
        print(f"\n🔧 SQLAlchemy引擎信息:")
        print(f"   引擎URL: {db.engine.url}")
        print(f"   数据库方言: {db.engine.dialect.name}")
        
        # 获取实际连接的数据库路径
        with db.engine.connect() as conn:
            result = conn.execute(text("PRAGMA database_list")).fetchall()
            for row in result:
                print(f"   数据库连接: {row[1]} -> {row[2]}")
                
    except Exception as e:
        print(f"❌ 引擎信息检查失败: {e}")

if __name__ == "__main__":
    test_database_paths()
    check_database_engine_path()