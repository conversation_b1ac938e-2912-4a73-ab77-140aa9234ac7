#!/usr/bin/env python3
"""
数据库路径验证工具
Database Path Validation Tool

验证所有数据库路径引用是否正确指向data/目录
"""

import os
import re
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))


def find_database_references():
    """查找所有数据库路径引用"""
    project_root = Path(__file__).parent.parent
    
    # 要搜索的文件类型
    search_patterns = ['*.py', '*.sh']
    
    # 要搜索的数据库文件名
    db_patterns = [
        r'trading_system\.db',
        r'"trading_system\.db"',
        r"'trading_system\.db'",
    ]
    
    results = []
    
    # 遍历所有相关文件
    for pattern in search_patterns:
        for file_path in project_root.rglob(pattern):
            # 跳过某些目录
            if any(skip in str(file_path) for skip in ['__pycache__', '.git', 'venv', 'node_modules']):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 查找数据库引用
                for line_num, line in enumerate(content.split('\n'), 1):
                    for db_pattern in db_patterns:
                        if re.search(db_pattern, line):
                            # 检查是否已经是正确的路径
                            if 'data/trading_system.db' not in line:
                                results.append({
                                    'file': str(file_path.relative_to(project_root)),
                                    'line': line_num,
                                    'content': line.strip(),
                                    'issue': 'Incorrect database path'
                                })
            except Exception as e:
                print(f"Warning: Could not read {file_path}: {e}")
    
    return results


def check_database_files():
    """检查数据库文件状态"""
    project_root = Path(__file__).parent.parent
    
    results = {
        'correct_location': {
            'path': project_root / 'data' / 'trading_system.db',
            'exists': False,
            'size': 0
        },
        'incorrect_location': {
            'path': project_root / 'trading_system.db',
            'exists': False,
            'size': 0
        }
    }
    
    # 检查正确位置的数据库
    correct_path = results['correct_location']['path']
    if correct_path.exists():
        results['correct_location']['exists'] = True
        results['correct_location']['size'] = correct_path.stat().st_size
    
    # 检查错误位置的数据库
    incorrect_path = results['incorrect_location']['path']
    if incorrect_path.exists():
        results['incorrect_location']['exists'] = True
        results['incorrect_location']['size'] = incorrect_path.stat().st_size
    
    return results


def main():
    """主函数"""
    print("🔍 数据库路径验证工具")
    print("=" * 50)
    
    # 检查数据库文件状态
    print("\n📁 数据库文件状态检查:")
    print("-" * 30)
    
    file_status = check_database_files()
    
    correct_db = file_status['correct_location']
    incorrect_db = file_status['incorrect_location']
    
    if correct_db['exists']:
        print(f"✅ 正确位置: data/trading_system.db")
        print(f"   文件大小: {correct_db['size']:,} 字节")
    else:
        print(f"❌ 正确位置: data/trading_system.db (文件不存在)")
    
    if incorrect_db['exists']:
        print(f"⚠️  错误位置: trading_system.db")
        print(f"   文件大小: {incorrect_db['size']:,} 字节")
        if incorrect_db['size'] == 0:
            print(f"   建议: 删除此空文件")
    else:
        print(f"✅ 错误位置: trading_system.db (已清理)")
    
    # 检查代码中的路径引用
    print(f"\n🔍 代码路径引用检查:")
    print("-" * 30)
    
    path_issues = find_database_references()
    
    if not path_issues:
        print("✅ 所有数据库路径引用都正确!")
    else:
        print(f"❌ 发现 {len(path_issues)} 个路径引用问题:")
        print()
        
        for issue in path_issues:
            print(f"文件: {issue['file']}")
            print(f"行号: {issue['line']}")
            print(f"内容: {issue['content']}")
            print(f"问题: {issue['issue']}")
            print("-" * 30)
    
    # 总结
    print(f"\n📊 验证总结:")
    print("-" * 30)
    
    if (correct_db['exists'] and correct_db['size'] > 0 and 
        not incorrect_db['exists'] and len(path_issues) == 0):
        print("🎉 所有数据库路径配置都正确!")
        return True
    else:
        issues = []
        if not correct_db['exists'] or correct_db['size'] == 0:
            issues.append("正确位置的数据库文件问题")
        if incorrect_db['exists']:
            issues.append("存在错误位置的数据库文件")
        if path_issues:
            issues.append(f"{len(path_issues)}个代码路径引用问题")
        
        print(f"⚠️  发现问题: {', '.join(issues)}")
        
        if incorrect_db['exists'] and incorrect_db['size'] == 0:
            print(f"\n💡 建议操作:")
            print(f"   rm trading_system.db  # 删除根目录的空数据库文件")
        
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)