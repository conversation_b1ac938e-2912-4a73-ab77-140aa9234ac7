#!/usr/bin/env python3
"""
一键应用配置更改脚本

直接在config.json中修改symbol后，运行此脚本即可应用更改。
After modifying symbol in config.json, run this script to apply changes.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Import the reloader
from tools.reload_config_symbol import ConfigSymbolReloader


async def main():
    """一键应用配置更改 / One-click apply config changes."""
    print("🚀 一键应用配置更改 / One-Click Config Apply")
    print("=" * 60)
    
    config_path = "config/config.json"
    
    # Check if custom config path is provided
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
    
    reloader = ConfigSymbolReloader(config_path)
    success = await reloader.reload_and_apply()
    
    if success:
        print(f"\n✅ 配置更改应用成功! / Config changes applied successfully!")
    else:
        print(f"\n❌ 配置更改应用失败 / Failed to apply config changes")
        print(f"💡 请检查config.json中的symbol配置 / Please check symbol config in config.json")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(asyncio.run(main()))