#!/usr/bin/env python3
"""
交易问题诊断工具
Trading Issue Diagnosis Tool

诊断11:39信号无法自动下单的问题，并检查品种切换一致性
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
from sqlalchemy import text

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.symbol_manager import symbol_manager
from quant.binance_client import binance_client
from quant.config_manager import config
from quant.strategies.auto_trader import auto_trader
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class TradingIssueDiagnostics:
    """交易问题诊断器"""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
    
    def print_section(self, title: str):
        """打印章节标题"""
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    def print_result(self, test_name: str, status: str, details: str = ""):
        """打印测试结果"""
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   {details}")
        
        if status != "PASS":
            self.issues_found.append((test_name, status, details))
    
    async def diagnose_database_connectivity(self):
        """诊断数据库连接问题"""
        self.print_section("数据库连接诊断")
        
        try:
            # 测试1：基本连接
            with db.get_session() as session:
                result = session.execute(text("SELECT 1")).fetchone()
                if result[0] == 1:
                    self.print_result("数据库基本连接", "PASS", "连接正常")
                else:
                    self.print_result("数据库基本连接", "FAIL", "连接异常")
                    return False
            
            # 测试2：表存在性
            with db.get_session() as session:
                result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='trade_history'")).fetchone()
                if result:
                    self.print_result("trade_history表存在性", "PASS", "表存在")
                else:
                    self.print_result("trade_history表存在性", "FAIL", "表不存在")
                    return False
            
            # 测试3：表数据
            with db.get_session() as session:
                result = session.execute(text("SELECT COUNT(*) FROM trade_history")).fetchone()
                count = result[0]
                self.print_result("表数据检查", "PASS", f"共有{count}条记录")
            
            # 测试4：SQLAlchemy ORM查询
            try:
                trades = db.get_recent_trades(limit=1)
                self.print_result("SQLAlchemy ORM查询", "PASS", f"成功获取{len(trades)}条记录")
            except Exception as e:
                self.print_result("SQLAlchemy ORM查询", "FAIL", f"ORM查询失败: {str(e)}")
                return False
            
            return True
            
        except Exception as e:
            self.print_result("数据库连接诊断", "FAIL", f"连接失败: {e}")
            return False
    
    async def diagnose_symbol_consistency(self):
        """诊断符号一致性问题"""
        self.print_section("符号一致性诊断")
        
        # 检查配置文件
        config_symbol = config.get("AUTO_TRADER", {}).get("symbol", "BTCUSDT")
        
        # 检查symbol_manager
        manager_symbol = symbol_manager.get_current_symbol()
        
        # 检查binance_client
        client_symbol = binance_client.get_current_symbol()
        
        print(f"📊 当前符号配置状态:")
        print(f"   配置文件 (config.json): {config_symbol}")
        print(f"   Symbol Manager: {manager_symbol}")
        print(f"   Binance Client: {client_symbol}")
        
        # 检查一致性
        all_symbols = [config_symbol, manager_symbol, client_symbol]
        if len(set(all_symbols)) == 1:
            self.print_result("符号一致性检查", "PASS", f"所有组件都使用 {config_symbol}")
        else:
            self.print_result("符号一致性检查", "FAIL", 
                             f"配置不一致: config={config_symbol}, manager={manager_symbol}, client={client_symbol}")
            
            # 尝试修复
            correct_symbol = config_symbol  # 以配置文件为准
            symbol_manager.set_current_symbol(correct_symbol)
            binance_client.set_current_symbol(correct_symbol)
            
            self.fixes_applied.append(f"同步所有组件到符号: {correct_symbol}")
            self.print_result("符号一致性修复", "PASS", f"已同步到 {correct_symbol}")
        
        return True
    
    async def diagnose_signal_generation(self):
        """诊断信号生成问题"""
        self.print_section("信号生成诊断")
        
        try:
            # 检查最近的信号 - 使用原始SQL查询避免ORM问题
            with db.get_session() as session:
                result = session.execute(text("""
                    SELECT * FROM trade_history 
                    ORDER BY signal_timestamp DESC 
                    LIMIT 5
                """)).fetchall()
                
                if result:
                    latest_trade = result[0]
                    # 处理时间戳 - 索引1是signal_timestamp字段
                    latest_time = datetime.fromisoformat(latest_trade[1].replace('Z', '+00:00')) if 'T' in latest_trade[1] else datetime.strptime(latest_trade[1], '%Y-%m-%d %H:%M:%S')
                    time_diff = datetime.now(latest_time.tzinfo) - latest_time if latest_time.tzinfo else datetime.utcnow() - latest_time
                
                    print(f"📈 最近信号信息:")
                    print(f"   最新信号时间: {latest_time}")
                    print(f"   距离现在: {time_diff}")
                    print(f"   信号符号: {latest_trade[2]}")
                    print(f"   信号方向: {latest_trade[3]}")
                    print(f"   置信度: {latest_trade[5]}")
                    print(f"   状态: {latest_trade[10]}")
                
                    if time_diff.total_seconds() < 3600:  # 1小时内
                        self.print_result("最近信号生成", "PASS", f"{time_diff.total_seconds()/60:.1f}分钟前生成信号")
                    else:
                        self.print_result("最近信号生成", "WARN", f"{time_diff.total_seconds()/3600:.1f}小时前生成最后信号")
                    
                    # 检查符号一致性
                    current_symbol = symbol_manager.get_current_symbol()
                    signal_symbol = latest_trade[2]
                    if signal_symbol == current_symbol:
                        self.print_result("信号符号一致性", "PASS", f"信号符号与当前配置一致: {current_symbol}")
                    else:
                        self.print_result("信号符号一致性", "FAIL", 
                                         f"信号符号({signal_symbol})与当前配置({current_symbol})不一致")
                else:
                    self.print_result("最近信号生成", "FAIL", "没有找到任何信号记录")
            
            return True
            
        except Exception as e:
            self.print_result("信号生成诊断", "FAIL", f"检查失败: {e}")
            return False
    
    async def diagnose_auto_trading(self):
        """诊断自动交易问题"""
        self.print_section("自动交易诊断")
        
        try:
            # 检查AutoTrader配置
            print(f"🤖 AutoTrader配置:")
            print(f"   启用状态: {auto_trader.enabled}")
            print(f"   紧急停止: {auto_trader.emergency_stop}")
            print(f"   当前符号: {auto_trader.symbol}")
            print(f"   最小订单: ${auto_trader.min_order_usdt}")
            print(f"   最大订单: ${auto_trader.max_order_usdt}")
            
            if not auto_trader.enabled:
                self.print_result("AutoTrader启用检查", "FAIL", "AutoTrader已禁用")
                return False
            elif auto_trader.emergency_stop:
                self.print_result("AutoTrader紧急停止检查", "FAIL", "AutoTrader处于紧急停止状态")
                return False
            else:
                self.print_result("AutoTrader状态检查", "PASS", "AutoTrader正常启用")
            
            # 检查符号一致性
            current_symbol = symbol_manager.get_current_symbol()
            if auto_trader.symbol == current_symbol:
                self.print_result("AutoTrader符号一致性", "PASS", f"符号一致: {current_symbol}")
            else:
                self.print_result("AutoTrader符号一致性", "FAIL", 
                                 f"AutoTrader符号({auto_trader.symbol})与当前配置({current_symbol})不一致")
                
                # 自动修复
                auto_trader.symbol = current_symbol
                self.fixes_applied.append(f"更新AutoTrader符号到: {current_symbol}")
                self.print_result("AutoTrader符号修复", "PASS", f"已更新到 {current_symbol}")
            
            return True
            
        except Exception as e:
            self.print_result("自动交易诊断", "FAIL", f"检查失败: {e}")
            return False
    
    async def diagnose_11_39_signal_issue(self):
        """专门诊断11:39信号问题"""
        self.print_section("11:39信号问题专项诊断")
        
        try:
            # 查找11:39附近的信号
            target_time = datetime(2025, 8, 17, 3, 39, 0)  # UTC时间
            start_time = target_time - timedelta(minutes=2)
            end_time = target_time + timedelta(minutes=2)
            
            with db.get_session() as session:
                query = text("""
                    SELECT * FROM trade_history 
                    WHERE signal_timestamp BETWEEN :start_time AND :end_time
                    ORDER BY signal_timestamp DESC
                """)
                
                result = session.execute(query, {
                    'start_time': start_time,
                    'end_time': end_time
                }).fetchall()
                
                if result:
                    print(f"🔍 找到11:39附近的信号 ({len(result)}条):")
                    for row in result:
                        print(f"   时间: {row[1]}, 符号: {row[2]}, 方向: {row[3]}, 状态: {row[10]}")
                    
                    # 检查第一条记录的详细信息
                    signal_record = result[0]
                    signal_symbol = signal_record[2]
                    signal_status = signal_record[10]
                    
                    print(f"\n📋 信号详细信息:")
                    print(f"   符号: {signal_symbol}")
                    print(f"   状态: {signal_status}")
                    print(f"   置信度: {signal_record[5]}")
                    print(f"   建议下注: ${signal_record[9]}")
                    
                    if signal_status == 'PENDING':
                        self.print_result("11:39信号状态", "FAIL", "信号仍处于PENDING状态，未被执行")
                        return False
                    else:
                        self.print_result("11:39信号状态", "PASS", f"信号状态: {signal_status}")
                
                else:
                    self.print_result("11:39信号查找", "FAIL", "未找到11:39附近的信号记录")
                    return False
            
            return True
            
        except Exception as e:
            self.print_result("11:39信号诊断", "FAIL", f"检查失败: {e}")
            return False
    
    async def test_mock_signal_execution(self):
        """测试模拟信号执行"""
        self.print_section("模拟信号执行测试")
        
        try:
            # 创建模拟信号
            current_symbol = symbol_manager.get_current_symbol()
            mock_signal = {
                'signal_timestamp': datetime.now().isoformat() + 'Z',
                'symbol': current_symbol,
                'direction': 'LONG',
                'entry_price': 60000.0,
                'confidence_score': 0.75,
                'market_state': 'bullish',
                'trigger_pattern': 'test_signal',
                'confirmed_indicators': ['rsi', 'macd'],
                'suggested_bet': 50.0,
                'trading_suspended': False,
                'analysis_only': False,
                'decision_details': {
                    'indicators': ['rsi', 'macd'],
                    'strength': 'medium',
                    'risk_level': 'low'
                }
            }
            
            print(f"🧪 测试信号信息:")
            print(f"   符号: {mock_signal['symbol']}")
            print(f"   方向: {mock_signal['direction']}")
            print(f"   建议下注: ${mock_signal['suggested_bet']}")
            
            # 测试AutoTrader处理
            result = await auto_trader.handle_new_signal(mock_signal)
            
            if result.success:
                self.print_result("模拟信号执行测试", "PASS", f"成功处理模拟信号: {result.message}")
                if result.trade_id:
                    print(f"   生成交易ID: {result.trade_id}")
            else:
                self.print_result("模拟信号执行测试", "FAIL", f"模拟信号处理失败: {result.message}")
                return False
            
            return True
            
        except Exception as e:
            self.print_result("模拟信号执行测试", "FAIL", f"测试失败: {e}")
            return False
    
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔧 交易问题全面诊断工具")
        print("=" * 60)
        print("正在诊断11:39信号无法自动下单的问题...")
        
        # 依次运行所有诊断
        db_ok = await self.diagnose_database_connectivity()
        if not db_ok:
            print("\n❌ 数据库连接问题，停止进一步诊断")
            return False
        
        symbol_ok = await self.diagnose_symbol_consistency()
        signal_ok = await self.diagnose_signal_generation()
        trading_ok = await self.diagnose_auto_trading()
        signal_1139_ok = await self.diagnose_11_39_signal_issue()
        test_ok = await self.test_mock_signal_execution()
        
        # 生成诊断报告
        self.print_section("诊断报告")
        
        if self.issues_found:
            print("❌ 发现的问题:")
            for i, (test_name, status, details) in enumerate(self.issues_found, 1):
                print(f"{i}. {test_name}: {status}")
                if details:
                    print(f"   {details}")
        
        if self.fixes_applied:
            print("\n✅ 应用的修复:")
            for i, fix in enumerate(self.fixes_applied, 1):
                print(f"{i}. {fix}")
        
        # 总结
        total_issues = len(self.issues_found)
        total_fixes = len(self.fixes_applied)
        
        print(f"\n📊 诊断总结:")
        print(f"   发现问题: {total_issues}个")
        print(f"   应用修复: {total_fixes}个")
        
        if total_issues == 0:
            print("🎉 所有检查通过！交易系统状态正常。")
            return True
        elif total_fixes >= total_issues:
            print("✅ 大部分问题已修复，建议重启系统后重新测试。")
            return True
        else:
            print("⚠️ 仍有未解决的问题需要手动处理。")
            return False


async def main():
    """主函数"""
    diagnostics = TradingIssueDiagnostics()
    success = await diagnostics.run_full_diagnosis()
    
    if success:
        print(f"\n💡 建议:")
        print(f"1. 如果应用了修复，请重启交易系统")
        print(f"2. 监控下一个信号的自动交易执行")
        print(f"3. 检查钉钉通知中的符号信息一致性")
    else:
        print(f"\n🚨 需要进一步处理:")
        print(f"1. 检查系统日志中的详细错误信息")
        print(f"2. 验证数据库文件完整性")
        print(f"3. 确认配置文件设置正确")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(asyncio.run(main()))