#!/usr/bin/env python3
"""
Advanced Log Analysis Tool

Provides utilities for analyzing structured logs with:
- Correlation ID tracking
- Operation summaries  
- Retry analysis
- Error pattern detection
- Performance metrics
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import re

from quant.utils.structured_logger import LogAggregator


class LogAnalyzer:
    """Advanced log analyzer for structured logs."""
    
    def __init__(self, log_file: Path):
        self.log_file = log_file
        self.logs: List[Dict[str, Any]] = []
        self.load_logs()
    
    def load_logs(self):
        """Load and parse JSON logs from file."""
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            log_entry = json.loads(line)
                            self.logs.append(log_entry)
                        except json.JSONDecodeError:
                            # Skip malformed JSON lines
                            continue
            print(f"Loaded {len(self.logs)} log entries from {self.log_file}")
        except FileNotFoundError:
            print(f"Log file not found: {self.log_file}")
            self.logs = []
    
    def analyze_operations(self, hours: int = 1) -> Dict[str, Any]:
        """Analyze operations within the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        recent_logs = [
            log for log in self.logs
            if self._parse_timestamp(log.get("timestamp", "")) >= cutoff_time
        ]
        
        operations = defaultdict(list)
        
        for log in recent_logs:
            if "context" in log and "correlation_id" in log["context"]:
                correlation_id = log["context"]["correlation_id"]
                operations[correlation_id].append(log)
        
        # Analyze each operation
        operation_summaries = {}
        for correlation_id, op_logs in operations.items():
            summary = LogAggregator.get_operation_summary(op_logs, correlation_id)
            operation_summaries[correlation_id] = summary
        
        # Generate statistics
        completed_ops = [s for s in operation_summaries.values() if s.get("completed")]
        failed_ops = [s for s in operation_summaries.values() if s.get("errors", 0) > 0]
        
        return {
            "time_range_hours": hours,
            "total_operations": len(operation_summaries),
            "completed_operations": len(completed_ops),
            "failed_operations": len(failed_ops),
            "success_rate": (len(completed_ops) / len(operation_summaries) * 100) if operation_summaries else 0,
            "average_duration": sum(s.get("duration_seconds", 0) for s in completed_ops) / len(completed_ops) if completed_ops else 0,
            "operation_types": Counter(s.get("operation_type") for s in operation_summaries.values()),
            "details": operation_summaries
        }
    
    def analyze_retries(self, hours: int = 1) -> Dict[str, Any]:
        """Analyze retry patterns within the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        retry_logs = [
            log for log in self.logs
            if (self._parse_timestamp(log.get("timestamp", "")) >= cutoff_time and
                log.get("event_type") == "retry_attempt")
        ]
        
        retry_operations = defaultdict(list)
        
        for log in retry_logs:
            correlation_id = log.get("correlation_id", "unknown")
            retry_operations[correlation_id].append(log)
        
        # Analyze retry patterns
        retry_stats = {
            "total_retry_sequences": len(retry_operations),
            "total_retry_attempts": len(retry_logs),
            "retry_sequences": {}
        }
        
        for correlation_id, attempts in retry_operations.items():
            attempts.sort(key=lambda x: x.get("timestamp", ""))
            
            first_attempt = attempts[0]
            last_attempt = attempts[-1]
            
            retry_context = first_attempt.get("retry_context", {})
            operation_name = retry_context.get("operation", "unknown")
            
            sequence_info = {
                "operation": operation_name,
                "total_attempts": len(attempts),
                "max_attempts": retry_context.get("max_attempts", 0),
                "completed_all_attempts": len(attempts) >= retry_context.get("max_attempts", 0),
                "errors": [log.get("error") for log in attempts if log.get("error")],
                "start_time": first_attempt.get("timestamp"),
                "end_time": last_attempt.get("timestamp")
            }
            
            retry_stats["retry_sequences"][correlation_id] = sequence_info
        
        # Calculate aggregated statistics
        if retry_operations:
            avg_attempts = sum(len(attempts) for attempts in retry_operations.values()) / len(retry_operations)
            retry_stats["average_attempts_per_sequence"] = avg_attempts
            
            # Find most common error patterns
            all_errors = []
            for attempts in retry_operations.values():
                all_errors.extend([log.get("error", "") for log in attempts if log.get("error")])
            
            retry_stats["common_error_patterns"] = Counter(all_errors).most_common(10)
        
        return retry_stats
    
    def analyze_errors(self, hours: int = 1) -> Dict[str, Any]:
        """Analyze error patterns within the last N hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        error_logs = [
            log for log in self.logs
            if (self._parse_timestamp(log.get("timestamp", "")) >= cutoff_time and
                log.get("level") in ["ERROR", "CRITICAL"])
        ]
        
        # Group errors by module/logger
        errors_by_module = defaultdict(list)
        for log in error_logs:
            module = log.get("logger", "unknown")
            errors_by_module[module].append(log)
        
        # Analyze error patterns
        error_patterns = Counter()
        for log in error_logs:
            message = log.get("message", "")
            # Extract error patterns (first 100 chars)
            pattern = message[:100]
            error_patterns[pattern] += 1
        
        return {
            "time_range_hours": hours,
            "total_errors": len(error_logs),
            "errors_by_module": {module: len(logs) for module, logs in errors_by_module.items()},
            "error_patterns": error_patterns.most_common(10),
            "recent_errors": error_logs[-10:] if error_logs else []  # Last 10 errors
        }
    
    def analyze_performance(self, operation_type: Optional[str] = None, hours: int = 1) -> Dict[str, Any]:
        """Analyze performance metrics for operations."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        operation_logs = [
            log for log in self.logs
            if (self._parse_timestamp(log.get("timestamp", "")) >= cutoff_time and
                log.get("event_type") == "operation_end")
        ]
        
        if operation_type:
            operation_logs = [
                log for log in operation_logs
                if log.get("context", {}).get("operation_type") == operation_type
            ]
        
        durations = [log.get("duration_seconds", 0) for log in operation_logs if log.get("duration_seconds")]
        
        if not durations:
            return {"message": "No performance data found"}
        
        durations.sort()
        count = len(durations)
        
        return {
            "operation_type": operation_type or "all",
            "time_range_hours": hours,
            "total_operations": count,
            "average_duration": sum(durations) / count,
            "median_duration": durations[count // 2],
            "min_duration": min(durations),
            "max_duration": max(durations),
            "p95_duration": durations[int(count * 0.95)] if count > 0 else 0,
            "p99_duration": durations[int(count * 0.99)] if count > 0 else 0
        }
    
    def find_correlation_chain(self, correlation_id: str) -> List[Dict[str, Any]]:
        """Find all logs related to a specific correlation ID."""
        return LogAggregator.filter_by_correlation_id(self.logs, correlation_id)
    
    def detect_anomalies(self, hours: int = 1) -> Dict[str, Any]:
        """Detect potential anomalies in log patterns."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        recent_logs = [
            log for log in self.logs
            if self._parse_timestamp(log.get("timestamp", "")) >= cutoff_time
        ]
        
        anomalies = {
            "high_error_rate": {},
            "slow_operations": [],
            "frequent_retries": [],
            "unusual_patterns": []
        }
        
        # Check error rates by module
        errors_by_module = defaultdict(int)
        total_by_module = defaultdict(int)
        
        for log in recent_logs:
            module = log.get("logger", "unknown")
            total_by_module[module] += 1
            
            if log.get("level") in ["ERROR", "CRITICAL"]:
                errors_by_module[module] += 1
        
        # Flag modules with high error rates (>10%)
        for module in total_by_module:
            error_rate = errors_by_module[module] / total_by_module[module]
            if error_rate > 0.1:  # 10% threshold
                anomalies["high_error_rate"][module] = {
                    "error_rate": error_rate * 100,
                    "errors": errors_by_module[module],
                    "total": total_by_module[module]
                }
        
        # Check for slow operations (>30 seconds)
        for log in recent_logs:
            if log.get("event_type") == "operation_end" and log.get("duration_seconds", 0) > 30:
                anomalies["slow_operations"].append({
                    "correlation_id": log.get("context", {}).get("correlation_id"),
                    "operation_type": log.get("context", {}).get("operation_type"),
                    "duration": log.get("duration_seconds"),
                    "timestamp": log.get("timestamp")
                })
        
        # Check for excessive retries (>5 attempts)
        retry_counts = defaultdict(int)
        for log in recent_logs:
            if log.get("event_type") == "retry_attempt":
                correlation_id = log.get("correlation_id", "unknown")
                retry_counts[correlation_id] += 1
        
        for correlation_id, count in retry_counts.items():
            if count > 5:
                anomalies["frequent_retries"].append({
                    "correlation_id": correlation_id,
                    "retry_count": count
                })
        
        return anomalies
    
    def generate_summary_report(self, hours: int = 1) -> str:
        """Generate a comprehensive summary report."""
        operations = self.analyze_operations(hours)
        retries = self.analyze_retries(hours)
        errors = self.analyze_errors(hours)
        performance = self.analyze_performance(hours=hours)
        anomalies = self.detect_anomalies(hours)
        
        report = f"""
# Log Analysis Report ({hours}h)

## Operation Summary
- Total Operations: {operations['total_operations']}
- Success Rate: {operations['success_rate']:.1f}%
- Average Duration: {operations['average_duration']:.2f}s
- Failed Operations: {operations['failed_operations']}

## Retry Analysis
- Total Retry Sequences: {retries['total_retry_sequences']}
- Total Retry Attempts: {retries['total_retry_attempts']}
- Average Attempts per Sequence: {retries.get('average_attempts_per_sequence', 0):.1f}

## Error Analysis
- Total Errors: {errors['total_errors']}
- Errors by Module: {dict(errors['errors_by_module'])}

## Performance Metrics
- Operations Analyzed: {performance.get('total_operations', 0)}
- Average Duration: {performance.get('average_duration', 0):.2f}s
- 95th Percentile: {performance.get('p95_duration', 0):.2f}s

## Anomalies Detected
- High Error Rate Modules: {len(anomalies['high_error_rate'])}
- Slow Operations: {len(anomalies['slow_operations'])}
- Frequent Retries: {len(anomalies['frequent_retries'])}

## Recommendations
"""
        
        # Add recommendations based on analysis
        if operations['success_rate'] < 90:
            report += "- ⚠️  Success rate below 90%, investigate failed operations\n"
        
        if retries.get('average_attempts_per_sequence', 0) > 2:
            report += "- ⚠️  High retry rate detected, check system reliability\n"
        
        if anomalies['high_error_rate']:
            report += f"- ⚠️  High error rates in modules: {list(anomalies['high_error_rate'].keys())}\n"
        
        if anomalies['slow_operations']:
            report += "- ⚠️  Slow operations detected, consider performance optimization\n"
        
        if not any([operations['success_rate'] < 90, retries.get('average_attempts_per_sequence', 0) > 2, 
                   anomalies['high_error_rate'], anomalies['slow_operations']]):
            report += "- ✅ System appears to be operating normally\n"
        
        return report
    
    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse timestamp string to datetime object."""
        try:
            # Handle ISO format timestamps
            if 'T' in timestamp_str:
                return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).replace(tzinfo=None)
            else:
                return datetime.fromisoformat(timestamp_str)
        except (ValueError, TypeError):
            return datetime.min


def main():
    """Command line interface for log analysis."""
    parser = argparse.ArgumentParser(description="Advanced Log Analysis Tool")
    parser.add_argument("log_file", help="Path to the log file")
    parser.add_argument("--hours", type=int, default=1, help="Hours to analyze (default: 1)")
    parser.add_argument("--operation", help="Specific operation type to analyze")
    parser.add_argument("--correlation-id", help="Find logs for specific correlation ID")
    parser.add_argument("--report", action="store_true", help="Generate summary report")
    parser.add_argument("--retries", action="store_true", help="Analyze retry patterns")
    parser.add_argument("--errors", action="store_true", help="Analyze error patterns")
    parser.add_argument("--performance", action="store_true", help="Analyze performance")
    parser.add_argument("--anomalies", action="store_true", help="Detect anomalies")
    
    args = parser.parse_args()
    
    analyzer = LogAnalyzer(Path(args.log_file))
    
    if args.correlation_id:
        logs = analyzer.find_correlation_chain(args.correlation_id)
        print(f"Found {len(logs)} logs for correlation ID: {args.correlation_id}")
        for log in logs:
            print(json.dumps(log, indent=2, ensure_ascii=False))
    
    elif args.report:
        report = analyzer.generate_summary_report(args.hours)
        print(report)
    
    elif args.retries:
        retries = analyzer.analyze_retries(args.hours)
        print(json.dumps(retries, indent=2, ensure_ascii=False))
    
    elif args.errors:
        errors = analyzer.analyze_errors(args.hours)
        print(json.dumps(errors, indent=2, ensure_ascii=False))
    
    elif args.performance:
        performance = analyzer.analyze_performance(args.operation, args.hours)
        print(json.dumps(performance, indent=2, ensure_ascii=False))
    
    elif args.anomalies:
        anomalies = analyzer.detect_anomalies(args.hours)
        print(json.dumps(anomalies, indent=2, ensure_ascii=False))
    
    else:
        operations = analyzer.analyze_operations(args.hours)
        print(json.dumps(operations, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()