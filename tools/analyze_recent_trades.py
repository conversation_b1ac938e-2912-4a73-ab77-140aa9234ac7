#!/usr/bin/env python3
"""
分析最近的交易记录，查找异常平仓问题
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path
import json

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


def analyze_recent_trades():
    """分析最近的交易记录"""
    logger.info("=== 最近24小时的交易记录分析 ===")
    
    try:
        # 查询最近24小时的交易
        yesterday = datetime.utcnow() - timedelta(hours=24)
        
        with db.get_session() as session:
            from quant.database_manager import TradeHistory

            trades = (
                session.query(TradeHistory)
                .filter(TradeHistory.created_at >= yesterday)
                .order_by(TradeHistory.id.desc())
                .limit(20)
                .all()
            )

            # 转换为字典以避免session问题
            trade_dicts = []
            for trade in trades:
                trade_dict = {
                    'id': trade.id,
                    'signal_timestamp': trade.signal_timestamp,
                    'created_at': trade.created_at,
                    'entry_price': trade.entry_price,
                    'exit_price': trade.exit_price,
                    'exit_timestamp': trade.exit_timestamp,
                    'status': trade.status,
                    'direction': trade.direction,
                    'suggested_bet': trade.suggested_bet,
                    'decision_details': trade.decision_details
                }
                trade_dicts.append(trade_dict)

        print(f'找到 {len(trade_dicts)} 条最近的交易记录:')

        suspicious_trades = []

        for trade in trade_dicts:
            print(f'\n交易ID: {trade["id"]}')
            print(f'  信号时间: {trade["signal_timestamp"]}')
            print(f'  创建时间: {trade["created_at"]}')
            print(f'  开仓价格: ${trade["entry_price"]:,.2f}')
            print(f'  平仓价格: ${trade["exit_price"] or 0:,.2f}')
            print(f'  平仓时间: {trade["exit_timestamp"]}')
            print(f'  状态: {trade["status"]}')
            print(f'  方向: {trade["direction"]}')
            print(f'  金额: ${trade["suggested_bet"]:,.2f}')
            
            # 分析持仓时间
            if trade["signal_timestamp"] and trade["exit_timestamp"]:
                try:
                    if isinstance(trade["signal_timestamp"], str):
                        signal_time = datetime.fromisoformat(trade["signal_timestamp"])
                    else:
                        signal_time = trade["signal_timestamp"]

                    if isinstance(trade["exit_timestamp"], str):
                        exit_time = datetime.fromisoformat(trade["exit_timestamp"])
                    else:
                        exit_time = trade["exit_timestamp"]

                    hold_duration = (exit_time - signal_time).total_seconds() / 60
                    print(f'  持仓时长: {hold_duration:.2f} 分钟')

                    # 标记可疑的短时间持仓
                    if hold_duration < 1.0:  # 少于1分钟
                        suspicious_trades.append({
                            'id': trade["id"],
                            'hold_duration': hold_duration,
                            'signal_time': trade["signal_timestamp"],
                            'exit_time': trade["exit_timestamp"],
                            'entry_price': trade["entry_price"],
                            'exit_price': trade["exit_price"]
                        })
                        print(f'  🚨 可疑：持仓时间过短！')
                except Exception as e:
                    print(f'  ❌ 时间解析错误: {e}')
            
            # 解析决策详情
            if trade["decision_details"]:
                try:
                    if isinstance(trade["decision_details"], str):
                        details = json.loads(trade["decision_details"])
                    else:
                        details = trade["decision_details"]

                    if 'exit_reason' in details:
                        print(f'  平仓原因: {details["exit_reason"]}')
                    if 'trigger_mode' in details:
                        print(f'  触发模式: {details["trigger_mode"]}')
                except Exception as e:
                    print(f'  决策详情解析错误: {e}')
        
        # 分析可疑交易
        if suspicious_trades:
            print(f'\n🚨 发现 {len(suspicious_trades)} 个可疑的短时间持仓交易:')
            for sus in suspicious_trades:
                print(f'  交易{sus["id"]}: 持仓 {sus["hold_duration"]:.2f} 分钟')
                print(f'    开仓: {sus["signal_time"]}')
                print(f'    平仓: {sus["exit_time"]}')
                print(f'    价格: ${sus["entry_price"]:,.2f} -> ${sus["exit_price"] or 0:,.2f}')
            
            return suspicious_trades
        else:
            print('\n✅ 未发现明显的短时间持仓交易')
            return []
    
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return []


def analyze_kline_timing():
    """分析K线时间计算逻辑"""
    logger.info("=== K线时间计算逻辑分析 ===")
    
    try:
        from quant.strategies.simple_exit_manager import simple_exit_manager
        
        # 测试不同时间点的K线计算
        test_times = [
            datetime(2025, 8, 13, 13, 5, 0),   # 13:05 -> 应该在13:30结束
            datetime(2025, 8, 13, 13, 25, 0),  # 13:25 -> 应该在13:30结束
            datetime(2025, 8, 13, 13, 35, 0),  # 13:35 -> 应该在14:00结束
            datetime(2025, 8, 13, 13, 55, 0),  # 13:55 -> 应该在14:00结束
            datetime(2025, 8, 13, 14, 5, 0),   # 14:05 -> 应该在14:30结束
        ]
        
        print("K线结束时间计算测试:")
        for test_time in test_times:
            # 模拟计算K线结束时间
            minutes_in_hour = test_time.minute
            
            if minutes_in_hour < 30:
                kline_end = test_time.replace(minute=30, second=0, microsecond=0)
            else:
                kline_end = test_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            
            # 计算计划平仓时间（K线结束前2分钟）
            planned_exit = kline_end - timedelta(minutes=simple_exit_manager.exit_before_kline_end_minutes)
            
            # 计算持仓时间
            hold_time = (planned_exit - test_time).total_seconds() / 60
            
            print(f"  信号时间: {test_time.strftime('%H:%M:%S')}")
            print(f"  K线结束: {kline_end.strftime('%H:%M:%S')}")
            print(f"  计划平仓: {planned_exit.strftime('%H:%M:%S')}")
            print(f"  预期持仓: {hold_time:.1f} 分钟")
            
            # 检查是否满足最小持仓时间
            if hold_time < simple_exit_manager.min_hold_minutes:
                print(f"  ⚠️  持仓时间 {hold_time:.1f} 分钟 < 最小持仓时间 {simple_exit_manager.min_hold_minutes} 分钟")
            else:
                print(f"  ✅ 持仓时间满足要求")
            print()
    
    except Exception as e:
        logger.error(f"❌ K线时间分析失败: {e}")
        import traceback
        traceback.print_exc()


def check_min_hold_protection():
    """检查最小持仓时间保护机制"""
    logger.info("=== 最小持仓时间保护机制检查 ===")
    
    try:
        from quant.strategies.simple_exit_manager import simple_exit_manager
        
        print(f"当前配置:")
        print(f"  最小持仓时间: {simple_exit_manager.min_hold_minutes} 分钟")
        print(f"  K线结束前平仓: {simple_exit_manager.exit_before_kline_end_minutes} 分钟")
        print(f"  K线周期: {simple_exit_manager.kline_period_minutes} 分钟")
        
        # 检查配置的合理性
        max_possible_hold = simple_exit_manager.kline_period_minutes - simple_exit_manager.exit_before_kline_end_minutes
        
        print(f"\n配置分析:")
        print(f"  最大可能持仓时间: {max_possible_hold} 分钟")
        print(f"  最小持仓时间要求: {simple_exit_manager.min_hold_minutes} 分钟")
        
        if simple_exit_manager.min_hold_minutes > max_possible_hold:
            print(f"  ❌ 配置冲突：最小持仓时间大于最大可能持仓时间")
        else:
            print(f"  ✅ 配置合理")
        
        # 检查代码中的保护逻辑
        print(f"\n代码逻辑检查:")
        print(f"  SimpleExitManager._calculate_exit_time 方法应该包含最小持仓时间检查")
        
        # 模拟一个接近K线结束的信号
        current_time = datetime.utcnow()
        minutes_in_hour = current_time.minute
        
        # 找到当前K线结束时间
        if minutes_in_hour < 30:
            kline_end = current_time.replace(minute=30, second=0, microsecond=0)
        else:
            kline_end = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        
        # 计算如果现在开仓，持仓时间是多少
        planned_exit = kline_end - timedelta(minutes=simple_exit_manager.exit_before_kline_end_minutes)
        time_to_exit = (planned_exit - current_time).total_seconds() / 60
        
        print(f"\n当前时间测试:")
        print(f"  当前时间: {current_time.strftime('%H:%M:%S')}")
        print(f"  下次K线结束: {kline_end.strftime('%H:%M:%S')}")
        print(f"  计划平仓时间: {planned_exit.strftime('%H:%M:%S')}")
        print(f"  如果现在开仓，持仓时间: {time_to_exit:.1f} 分钟")
        
        if time_to_exit < simple_exit_manager.min_hold_minutes:
            print(f"  ⚠️  当前开仓会违反最小持仓时间保护")
        else:
            print(f"  ✅ 当前开仓满足最小持仓时间要求")
    
    except Exception as e:
        logger.error(f"❌ 最小持仓时间检查失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    suspicious = analyze_recent_trades()
    print("\n" + "="*60 + "\n")
    analyze_kline_timing()
    print("\n" + "="*60 + "\n")
    check_min_hold_protection()
    
    if suspicious:
        print(f"\n🚨 发现 {len(suspicious)} 个需要进一步调查的交易")
    else:
        print(f"\n✅ 未发现明显的异常交易")
