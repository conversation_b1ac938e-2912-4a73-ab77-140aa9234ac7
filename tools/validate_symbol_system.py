#!/usr/bin/env python3
"""
Symbol System Validation Script

Validates the complete multi-symbol trading system implementation.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.config_manager import ConfigManager
from quant.symbol_manager import SymbolManager, symbol_manager
from quant.binance_client import binance_client
from quant.simple_analysis_engine import analysis_engine
from quant.real_time_data_manager import real_time_data_manager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class SymbolSystemValidator:
    """Validates the multi-symbol trading system."""
    
    def __init__(self, config_path: str = "config/config.json"):
        self.config_manager = ConfigManager(config_path)
        self.symbol_manager = symbol_manager
        self.validation_results = []
    
    def print_banner(self, title: str):
        """Print a formatted banner."""
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}")
    
    def print_test(self, test_name: str, status: str, details: str = ""):
        """Print test result."""
        status_icon = "✅" if status == "PASS" else "❌"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   {details}")
        self.validation_results.append((test_name, status, details))
    
    def test_configuration_loading(self):
        """Test configuration loading and validation."""
        self.print_banner("Configuration Loading Tests")
        
        try:
            # Test 1: Load main configuration
            config_data = self.config_manager.load_config()
            if "TRADING_SYMBOLS" in config_data:
                self.print_test("Configuration Loading", "PASS", 
                               f"Found {len(config_data['TRADING_SYMBOLS'])} trading symbols")
            else:
                self.print_test("Configuration Loading", "FAIL", 
                               "TRADING_SYMBOLS section not found")
                return False
            
            # Test 2: Validate symbol configurations
            trading_symbols = config_data["TRADING_SYMBOLS"]
            required_fields = ["enabled", "min_order_usdt", "max_order_usdt", "max_position_minutes"]
            
            for symbol, config in trading_symbols.items():
                missing_fields = [field for field in required_fields if field not in config]
                if missing_fields:
                    self.print_test(f"Symbol Configuration ({symbol})", "FAIL",
                                   f"Missing fields: {missing_fields}")
                else:
                    self.print_test(f"Symbol Configuration ({symbol})", "PASS",
                                   f"All required fields present")
            
            return True
            
        except Exception as e:
            self.print_test("Configuration Loading", "FAIL", str(e))
            return False
    
    def test_symbol_manager(self):
        """Test symbol manager functionality."""
        self.print_banner("Symbol Manager Tests")
        
        try:
            # Test 1: Current symbol retrieval
            current_symbol = self.symbol_manager.get_current_symbol()
            if current_symbol:
                self.print_test("Current Symbol Retrieval", "PASS", 
                               f"Current symbol: {current_symbol}")
            else:
                self.print_test("Current Symbol Retrieval", "FAIL", 
                               "No current symbol found")
                return False
            
            # Test 2: Symbol switching
            original_symbol = current_symbol
            test_symbol = "BTCUSDC" if original_symbol == "BTCUSDT" else "BTCUSDT"
            
            try:
                self.symbol_manager.set_current_symbol(test_symbol)
                new_symbol = self.symbol_manager.get_current_symbol()
                
                if new_symbol == test_symbol:
                    self.print_test("Symbol Switching", "PASS",
                                   f"Switched from {original_symbol} to {new_symbol}")
                else:
                    self.print_test("Symbol Switching", "FAIL",
                                   f"Expected {test_symbol}, got {new_symbol}")
                
                # Restore original symbol
                self.symbol_manager.set_current_symbol(original_symbol)
                
            except Exception as e:
                self.print_test("Symbol Switching", "FAIL", str(e))
            
            return True
            
        except Exception as e:
            self.print_test("Symbol Manager Tests", "FAIL", str(e))
            return False
    
    async def test_binance_client_integration(self):
        """Test Binance client integration with symbol manager."""
        self.print_banner("Binance Client Integration Tests")
        
        try:
            # Test 1: Initialize client
            await binance_client.initialize()
            
            # Test 2: Current symbol retrieval
            client_symbol = binance_client.get_current_symbol()
            manager_symbol = self.symbol_manager.get_current_symbol()
            
            if client_symbol == manager_symbol:
                self.print_test("Client-Manager Symbol Sync", "PASS",
                               f"Both using symbol: {client_symbol}")
            else:
                self.print_test("Client-Manager Symbol Sync", "FAIL",
                               f"Client: {client_symbol}, Manager: {manager_symbol}")
            
            # Test 3: Symbol switching propagation
            test_symbol = "BTCUSDC"
            binance_client.set_current_symbol(test_symbol)
            updated_symbol = binance_client.get_current_symbol()
            
            if updated_symbol == test_symbol:
                self.print_test("Client Symbol Update", "PASS",
                               f"Client symbol updated to: {updated_symbol}")
            else:
                self.print_test("Client Symbol Update", "FAIL",
                               f"Expected {test_symbol}, got {updated_symbol}")
            
            # Test 4: Price fetching with symbol parameter
            try:
                price = await binance_client.get_current_price("BTCUSDT")
                if price > 0:
                    self.print_test("Symbol-Specific Price Fetch", "PASS",
                                   f"BTCUSDT price: ${price:,.2f}")
                else:
                    self.print_test("Symbol-Specific Price Fetch", "FAIL",
                                   f"Invalid price: {price}")
            except Exception as e:
                self.print_test("Symbol-Specific Price Fetch", "FAIL", str(e))
            
            return True
            
        except Exception as e:
            self.print_test("Binance Client Integration", "FAIL", str(e))
            return False
    
    async def test_symbol_validation(self):
        """Test symbol validation functionality."""
        self.print_banner("Symbol Validation Tests")
        
        try:
            # Test 1: Valid symbol validation
            valid_symbols = ["BTCUSDT", "BTCUSDC", "ETHUSDT"]
            
            for symbol in valid_symbols:
                try:
                    is_valid = await self.symbol_manager.validate_symbol_switch_async(symbol)
                    if is_valid:
                        self.print_test(f"Symbol Validation ({symbol})", "PASS",
                                       f"{symbol} is valid for trading")
                    else:
                        self.print_test(f"Symbol Validation ({symbol})", "FAIL",
                                       f"{symbol} validation failed")
                except Exception as e:
                    self.print_test(f"Symbol Validation ({symbol})", "FAIL", str(e))
            
            # Test 2: Invalid symbol validation
            invalid_symbol = "INVALID123"
            try:
                is_valid = await self.symbol_manager.validate_symbol_switch_async(invalid_symbol)
                if not is_valid:
                    self.print_test("Invalid Symbol Rejection", "PASS",
                                   f"Correctly rejected {invalid_symbol}")
                else:
                    self.print_test("Invalid Symbol Rejection", "FAIL",
                                   f"Incorrectly accepted {invalid_symbol}")
            except Exception as e:
                self.print_test("Invalid Symbol Rejection", "PASS",
                               f"Correctly threw exception: {str(e)[:50]}...")
            
            return True
            
        except Exception as e:
            self.print_test("Symbol Validation Tests", "FAIL", str(e))
            return False
    
    async def test_analysis_engine_integration(self):
        """Test analysis engine integration with symbol manager."""
        self.print_banner("Analysis Engine Integration Tests")
        
        try:
            # Test 1: Check if analysis engine uses symbol manager
            current_symbol = self.symbol_manager.get_current_symbol()
            
            # This is a basic test - we can't easily mock the full analysis
            # but we can check if the engine is initialized properly
            if hasattr(analysis_engine, 'symbol_manager'):
                self.print_test("Analysis Engine Symbol Manager", "PASS",
                               "Analysis engine has symbol manager integration")
            else:
                self.print_test("Analysis Engine Symbol Manager", "PARTIAL",
                               "Analysis engine integration not directly verifiable")
            
            # Test 2: Check if engine can handle symbol parameter
            try:
                # This is a lightweight test
                if current_symbol:
                    self.print_test("Analysis Engine Symbol Aware", "PASS",
                                   f"Engine can work with symbol: {current_symbol}")
            except Exception as e:
                self.print_test("Analysis Engine Symbol Aware", "FAIL", str(e))
            
            return True
            
        except Exception as e:
            self.print_test("Analysis Engine Integration", "FAIL", str(e))
            return False
    
    async def test_complete_workflow(self):
        """Test complete symbol switching workflow."""
        self.print_banner("Complete Workflow Tests")
        
        try:
            original_symbol = self.symbol_manager.get_current_symbol()
            test_symbol = "BTCUSDC" if original_symbol == "BTCUSDT" else "BTCUSDT"
            
            # Test 1: End-to-end symbol switch
            try:
                # Switch symbol in manager
                self.symbol_manager.set_current_symbol(test_symbol)
                
                # Update client
                binance_client.set_current_symbol(test_symbol)
                
                # Verify consistency
                manager_symbol = self.symbol_manager.get_current_symbol()
                client_symbol = binance_client.get_current_symbol()
                
                if manager_symbol == client_symbol == test_symbol:
                    self.print_test("End-to-End Symbol Switch", "PASS",
                                   f"All components switched to: {test_symbol}")
                else:
                    self.print_test("End-to-End Symbol Switch", "FAIL",
                                   f"Inconsistent state: Manager={manager_symbol}, Client={client_symbol}")
                
                # Restore original symbol
                self.symbol_manager.set_current_symbol(original_symbol)
                binance_client.set_current_symbol(original_symbol)
                
            except Exception as e:
                self.print_test("End-to-End Symbol Switch", "FAIL", str(e))
            
            return True
            
        except Exception as e:
            self.print_test("Complete Workflow Tests", "FAIL", str(e))
            return False
    
    async def run_all_validations(self):
        """Run all validation tests."""
        print("🔍 Multi-Symbol Trading System Validation")
        print("=" * 60)
        
        # Run all test suites
        config_ok = self.test_configuration_loading()
        if not config_ok:
            print("\n❌ Configuration tests failed. Cannot proceed with other tests.")
            return False
        
        manager_ok = self.test_symbol_manager()
        client_ok = await self.test_binance_client_integration()
        validation_ok = await self.test_symbol_validation()
        analysis_ok = await self.test_analysis_engine_integration()
        workflow_ok = await self.test_complete_workflow()
        
        # Summary
        self.print_banner("Validation Summary")
        
        passed = sum(1 for _, status, _ in self.validation_results if status == "PASS")
        partial = sum(1 for _, status, _ in self.validation_results if status == "PARTIAL")
        failed = sum(1 for _, status, _ in self.validation_results if status == "FAIL")
        total = len(self.validation_results)
        
        print(f"Total Tests: {total}")
        print(f"✅ Passed: {passed}")
        print(f"⚠️  Partial: {partial}")
        print(f"❌ Failed: {failed}")
        
        if failed == 0:
            print(f"\n🎉 All validation tests passed! Multi-symbol system is ready.")
            return True
        elif failed <= 2 and passed >= total * 0.8:
            print(f"\n⚠️  System mostly functional with minor issues.")
            return True
        else:
            print(f"\n❌ System has significant issues that need to be addressed.")
            return False


async def main():
    """Main validation entry point."""
    config_path = "config/config.json"
    
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            print("Usage: python validate_symbol_system.py [config_path]")
            print("  config_path: Path to configuration file (default: config/config.json)")
            return
        config_path = sys.argv[1]
    
    validator = SymbolSystemValidator(config_path)
    success = await validator.run_all_validations()
    
    if success:
        print(f"\n✅ Validation completed successfully!")
        print(f"💡 Next steps:")
        print(f"   1. Run the symbol switcher tool to test interactive switching")
        print(f"   2. Test with production trading system")
        print(f"   3. Monitor system logs for any issues")
    else:
        print(f"\n❌ Validation failed!")
        print(f"💡 Please address the failed tests before proceeding.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(asyncio.run(main()))