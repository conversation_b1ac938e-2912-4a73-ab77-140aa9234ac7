#!/usr/bin/env python3
"""
Test Symbol Switch Script

Demonstrates the symbol switching functionality programmatically.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.symbol_manager import symbol_manager
from quant.binance_client import binance_client
from quant.utils.logger import get_logger
from tools.symbol_switcher import SymbolSwitcher

logger = get_logger(__name__)


async def test_symbol_switch():
    """Test the complete symbol switching workflow."""
    print("🔄 Testing Symbol Switching System")
    print("=" * 50)
    
    try:
        # Initialize switcher
        switcher = SymbolSwitcher()
        
        # Show current configuration
        print("\n📊 Current Configuration:")
        switcher.print_current_config()
        
        # Test switching from BTCUSDT to BTCUSDC
        original_symbol = symbol_manager.get_current_symbol()
        target_symbol = "BTCUSDC" if original_symbol == "BTCUSDT" else "BTCUSDT"
        
        print(f"\n🔄 Testing switch from {original_symbol} to {target_symbol}")
        
        # Perform the switch
        success = await switcher.switch_symbol(target_symbol)
        
        if success:
            print(f"✅ Successfully switched to {target_symbol}")
            
            # Verify the switch
            print("\n🔍 Verification:")
            current = symbol_manager.get_current_symbol()
            print(f"   Symbol Manager: {current}")
            print(f"   Binance Client: {binance_client.get_current_symbol()}")
            
            # Show updated configuration
            print("\n📊 Updated Configuration:")
            switcher.print_current_config()
            
            # Test a sample price fetch with new symbol
            try:
                price = await binance_client.get_current_price()
                print(f"\n💰 Current {current} price: ${price:,.2f}")
            except Exception as e:
                print(f"\n⚠️  Price fetch test: {e}")
            
            print(f"\n✅ Symbol switching test completed successfully!")
            
        else:
            print(f"❌ Failed to switch to {target_symbol}")
        
        return success
        
    except Exception as e:
        print(f"❌ Error in symbol switching test: {e}")
        return False


async def test_multiple_symbols():
    """Test switching between multiple symbols."""
    print("\n🔄 Testing Multiple Symbol Switches")
    print("=" * 50)
    
    symbols = ["BTCUSDT", "BTCUSDC"]
    switcher = SymbolSwitcher()
    
    for symbol in symbols:
        print(f"\n📈 Testing switch to {symbol}")
        try:
            success = await switcher.switch_symbol(symbol)
            if success:
                current = symbol_manager.get_current_symbol()
                price = await binance_client.get_current_price()
                print(f"✅ Active: {current}, Price: ${price:,.2f}")
            else:
                print(f"❌ Failed to switch to {symbol}")
        except Exception as e:
            print(f"❌ Error switching to {symbol}: {e}")
    
    print(f"\n✅ Multiple symbol test completed!")


async def main():
    """Main test runner."""
    print("🧪 Symbol Switching System Test Suite")
    print("=" * 60)
    
    # Test 1: Basic symbol switching
    test1_success = await test_symbol_switch()
    
    # Test 2: Multiple symbol switches
    await test_multiple_symbols()
    
    print(f"\n📊 Test Summary:")
    print(f"✅ Basic Symbol Switch: {'PASSED' if test1_success else 'FAILED'}")
    print(f"✅ Multiple Symbol Test: COMPLETED")
    
    print(f"\n🎉 All tests completed!")
    print(f"💡 The multi-symbol trading system is ready for use!")
    
    return 0 if test1_success else 1


if __name__ == "__main__":
    exit(asyncio.run(main()))