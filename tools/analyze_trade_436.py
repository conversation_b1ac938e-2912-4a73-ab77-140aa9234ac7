#!/usr/bin/env python3
"""
深入分析交易436的问题
"""

import sys
from datetime import datetime
from pathlib import Path
import json

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


def analyze_trade_436():
    """深入分析交易436"""
    logger.info("=== 深入分析交易436 ===")
    
    try:
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            
            # 查询交易436
            trade = session.query(TradeHistory).filter(TradeHistory.id == 436).first()
            
            if trade:
                print(f'交易ID: {trade.id}')
                print(f'信号时间: {trade.signal_timestamp}')
                print(f'创建时间: {trade.created_at}')
                print(f'开仓价格: ${trade.entry_price:,.2f}')
                print(f'平仓价格: ${trade.exit_price or 0:,.2f}')
                print(f'平仓时间: {trade.exit_timestamp}')
                print(f'状态: {trade.status}')
                print(f'方向: {trade.direction}')
                print(f'金额: ${trade.suggested_bet:,.2f}')
                
                # 计算持仓时间
                if trade.signal_timestamp and trade.exit_timestamp:
                    if isinstance(trade.signal_timestamp, str):
                        signal_time = datetime.fromisoformat(trade.signal_timestamp)
                    else:
                        signal_time = trade.signal_timestamp
                    
                    if isinstance(trade.exit_timestamp, str):
                        exit_time = datetime.fromisoformat(trade.exit_timestamp)
                    else:
                        exit_time = trade.exit_timestamp
                    
                    hold_duration = (exit_time - signal_time).total_seconds()
                    
                    print(f'持仓时长: {hold_duration:.3f} 秒 ({hold_duration/60:.3f} 分钟)')
                    
                    if hold_duration < 60:  # 少于1分钟
                        print('🚨 确认：持仓时间过短！')
                        
                        # 分析时间戳的精度
                        print(f'\n时间戳分析:')
                        print(f'  信号时间: {signal_time}')
                        print(f'  平仓时间: {exit_time}')
                        print(f'  时间差: {hold_duration:.6f} 秒')
                        print(f'  毫秒差: {hold_duration * 1000:.1f} 毫秒')
                
                # 分析决策详情
                if trade.decision_details:
                    try:
                        if isinstance(trade.decision_details, str):
                            details = json.loads(trade.decision_details)
                        else:
                            details = trade.decision_details
                        
                        print(f'\n决策详情:')
                        for key, value in details.items():
                            print(f'  {key}: {value}')
                            
                        # 特别关注平仓相关信息
                        if 'exit_reason' in details:
                            print(f'\n🔍 平仓原因分析: {details["exit_reason"]}')
                        
                        if 'settlement_method' in details:
                            print(f'🔍 结算方法: {details["settlement_method"]}')
                            
                    except Exception as e:
                        print(f'决策详情解析错误: {e}')
                        print(f'原始数据: {trade.decision_details}')
                
                # 检查是否是测试交易
                if trade.entry_price == 50000.0:
                    print(f'\n🧪 这是一个测试交易（价格 = $50,000）')
                    print(f'   测试交易应该有2分钟的最小持仓时间保护')
                    print(f'   但实际持仓时间只有 {hold_duration:.3f} 秒')
                    print(f'   这表明保护机制被绕过了！')
                
                return trade
            else:
                print('❌ 未找到交易436')
                return None
    
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def find_similar_issues():
    """查找类似的问题交易"""
    logger.info("=== 查找类似的问题交易 ===")
    
    try:
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            
            # 查找所有测试交易（entry_price = 50000.0）
            test_trades = (
                session.query(TradeHistory)
                .filter(TradeHistory.entry_price == 50000.0)
                .order_by(TradeHistory.id.desc())
                .limit(10)
                .all()
            )
            
            print(f'找到 {len(test_trades)} 个测试交易:')
            
            problem_trades = []
            
            for trade in test_trades:
                print(f'\n交易ID: {trade.id}')
                print(f'  状态: {trade.status}')
                print(f'  信号时间: {trade.signal_timestamp}')
                print(f'  平仓时间: {trade.exit_timestamp}')
                
                if trade.signal_timestamp and trade.exit_timestamp:
                    if isinstance(trade.signal_timestamp, str):
                        signal_time = datetime.fromisoformat(trade.signal_timestamp)
                    else:
                        signal_time = trade.signal_timestamp
                    
                    if isinstance(trade.exit_timestamp, str):
                        exit_time = datetime.fromisoformat(trade.exit_timestamp)
                    else:
                        exit_time = trade.exit_timestamp
                    
                    hold_duration = (exit_time - signal_time).total_seconds() / 60
                    print(f'  持仓时长: {hold_duration:.3f} 分钟')
                    
                    if hold_duration < 2.0:  # 测试交易应该至少持仓2分钟
                        print(f'  🚨 问题：持仓时间 < 2分钟（测试交易保护失效）')
                        problem_trades.append({
                            'id': trade.id,
                            'hold_duration': hold_duration,
                            'status': trade.status
                        })
                    else:
                        print(f'  ✅ 正常：持仓时间满足要求')
                elif trade.status == "PENDING":
                    print(f'  ⏳ 仍在持仓中')
                else:
                    print(f'  ❓ 时间信息不完整')
            
            if problem_trades:
                print(f'\n🚨 发现 {len(problem_trades)} 个问题交易:')
                for prob in problem_trades:
                    print(f'  交易{prob["id"]}: 持仓{prob["hold_duration"]:.3f}分钟, 状态{prob["status"]}')
            else:
                print(f'\n✅ 除了当前问题外，其他测试交易都正常')
            
            return problem_trades
    
    except Exception as e:
        logger.error(f"❌ 查找失败: {e}")
        import traceback
        traceback.print_exc()
        return []


if __name__ == "__main__":
    trade_436 = analyze_trade_436()
    print("\n" + "="*60 + "\n")
    problem_trades = find_similar_issues()
    
    if trade_436 and trade_436.entry_price == 50000.0:
        print(f"\n🎯 结论：交易436是测试交易，但保护机制失效")
        print(f"   需要检查为什么测试交易的2分钟保护被绕过了")
    
    if problem_trades:
        print(f"\n📊 总共发现 {len(problem_trades)} 个类似问题")
