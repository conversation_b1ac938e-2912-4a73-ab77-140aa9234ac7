#!/usr/bin/env python3
"""
Symbol Switcher Tool

Interactive tool for switching trading symbols with validation and safety checks.
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.config_manager import ConfigManager
from quant.symbol_manager import SymbolManager, symbol_manager
from quant.binance_client import binance_client
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class SymbolSwitcher:
    """Interactive tool for switching trading symbols."""
    
    def __init__(self, config_path: str = "config/config.json"):
        self.config_manager = ConfigManager(config_path)
        self.symbol_manager = symbol_manager
        self.config_path = config_path
    
    def print_current_config(self):
        """Print current symbol configuration."""
        current_symbol = self.symbol_manager.get_current_symbol()
        trading_symbols = self.config_manager.get("TRADING_SYMBOLS", {})
        
        print(f"\n📊 Current Trading Configuration")
        print(f"{'='*50}")
        print(f"Current Symbol: {current_symbol}")
        
        if current_symbol in trading_symbols:
            symbol_config = trading_symbols[current_symbol]
            print(f"Status: {'✅ Enabled' if symbol_config.get('enabled', False) else '❌ Disabled'}")
            print(f"Min Order: ${symbol_config.get('min_order_usdt', 'N/A')}")
            print(f"Max Order: ${symbol_config.get('max_order_usdt', 'N/A')}")
            print(f"Max Position Minutes: {symbol_config.get('max_position_minutes', 'N/A')}")
            print(f"Adverse Stop: {symbol_config.get('adverse_pct_stop', 'N/A')}%")
        
        print(f"\n📈 Available Symbols:")
        for symbol, config in trading_symbols.items():
            status = "✅ Enabled" if config.get('enabled', False) else "❌ Disabled"
            current_marker = " (CURRENT)" if symbol == current_symbol else ""
            print(f"  {symbol}: {status}{current_marker}")
            if config.get('comment'):
                print(f"    Comment: {config['comment']}")
    
    async def validate_symbol(self, symbol: str) -> bool:
        """Validate symbol with exchange."""
        print(f"\n🔍 Validating symbol {symbol}...")
        
        try:
            # Initialize binance client if needed
            if not binance_client.client:
                await binance_client.initialize()
            
            # Validate with symbol manager
            is_valid = await self.symbol_manager.validate_symbol_switch_async(symbol)
            
            if is_valid:
                # Get symbol info
                symbol_info = await self.symbol_manager.get_symbol_info(symbol)
                if symbol_info:
                    print(f"✅ Symbol validation passed:")
                    print(f"   Base Asset: {symbol_info.base_asset}")
                    print(f"   Quote Asset: {symbol_info.quote_asset}")
                    print(f"   Status: {symbol_info.status}")
                    print(f"   Min Quantity: {symbol_info.min_qty}")
                    print(f"   Min Notional: ${symbol_info.min_notional}")
                    print(f"   Price Precision: {symbol_info.price_precision}")
                    print(f"   Quantity Precision: {symbol_info.quantity_precision}")
                    return True
                else:
                    print(f"❌ Could not get symbol info for {symbol}")
                    return False
            else:
                print(f"❌ Symbol validation failed for {symbol}")
                return False
                
        except Exception as e:
            print(f"❌ Error validating symbol {symbol}: {e}")
            return False
    
    def update_config(self, new_symbol: str) -> bool:
        """Update configuration file with new symbol."""
        try:
            # Load current config
            config_data = self.config_manager.load_config()
            
            # Update AUTO_TRADER symbol
            if "AUTO_TRADER" not in config_data:
                config_data["AUTO_TRADER"] = {}
            
            old_symbol = config_data["AUTO_TRADER"].get("symbol", "BTCUSDT")
            config_data["AUTO_TRADER"]["symbol"] = new_symbol
            
            # Update symbol-specific configuration if available
            trading_symbols = config_data.get("TRADING_SYMBOLS", {})
            if new_symbol in trading_symbols:
                symbol_config = trading_symbols[new_symbol]
                # Copy symbol-specific settings to AUTO_TRADER
                config_data["AUTO_TRADER"]["min_order_usdt"] = symbol_config.get("min_order_usdt", 10.0)
                config_data["AUTO_TRADER"]["max_order_usdt"] = symbol_config.get("max_order_usdt", 1000.0)
                config_data["AUTO_TRADER"]["max_position_minutes"] = symbol_config.get("max_position_minutes", 30)
                
                # Update EXTENSION_RULES
                if "EXTENSION_RULES" not in config_data["AUTO_TRADER"]:
                    config_data["AUTO_TRADER"]["EXTENSION_RULES"] = {}
                
                config_data["AUTO_TRADER"]["EXTENSION_RULES"]["adverse_pct_stop"] = symbol_config.get("adverse_pct_stop", 0.008)
                config_data["AUTO_TRADER"]["EXTENSION_RULES"]["high_vol_threshold_pct"] = symbol_config.get("high_vol_threshold_pct", 0.015)
            
            # Save updated configuration
            with open(self.config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            print(f"✅ Configuration updated:")
            print(f"   Changed symbol from {old_symbol} to {new_symbol}")
            print(f"   Updated file: {self.config_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error updating configuration: {e}")
            return False
    
    async def switch_symbol(self, new_symbol: str) -> bool:
        """Switch to new trading symbol with full validation."""
        current_symbol = self.symbol_manager.get_current_symbol()
        
        if new_symbol == current_symbol:
            print(f"ℹ️  Already using symbol {new_symbol}")
            return True
        
        print(f"\n🔄 Switching from {current_symbol} to {new_symbol}")
        
        # Step 1: Validate new symbol
        if not await self.validate_symbol(new_symbol):
            return False
        
        # Step 2: Check if symbol is enabled in config
        trading_symbols = self.config_manager.get("TRADING_SYMBOLS", {})
        if new_symbol in trading_symbols and not trading_symbols[new_symbol].get("enabled", True):
            print(f"⚠️  Symbol {new_symbol} is disabled in configuration")
            enable = input("Enable this symbol? (y/n): ").lower().strip()
            if enable != 'y':
                print("❌ Symbol switch cancelled")
                return False
        
        # Step 3: Update configuration
        if not self.update_config(new_symbol):
            return False
        
        # Step 4: Update symbol manager
        self.symbol_manager.set_current_symbol(new_symbol)
        binance_client.set_current_symbol(new_symbol)
        
        print(f"✅ Successfully switched to {new_symbol}")
        print(f"⚠️  Please restart the trading system for changes to take effect")
        
        return True
    
    def add_symbol_config(self, symbol: str):
        """Add new symbol configuration."""
        try:
            # Get recommended template
            template = self.symbol_manager.get_symbol_config_template(symbol)
            
            print(f"\n📝 Adding configuration for {symbol}")
            print("Recommended settings:")
            for key, value in template.items():
                if key != "EXTENSION_RULES":
                    print(f"  {key}: {value}")
            
            # Load current config
            config_data = self.config_manager.load_config()
            
            if "TRADING_SYMBOLS" not in config_data:
                config_data["TRADING_SYMBOLS"] = {}
            
            config_data["TRADING_SYMBOLS"][symbol] = {
                "enabled": True,
                "min_order_usdt": template.get("min_order_usdt", 10.0),
                "max_order_usdt": template.get("max_order_usdt", 1000.0),
                "max_position_minutes": template.get("max_position_minutes", 30),
                "adverse_pct_stop": template.get("EXTENSION_RULES", {}).get("adverse_pct_stop", 0.008),
                "high_vol_threshold_pct": template.get("EXTENSION_RULES", {}).get("high_vol_threshold_pct", 0.015),
                "comment": f"Added by symbol switcher"
            }
            
            # Save configuration
            with open(self.config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            print(f"✅ Added configuration for {symbol}")
            return True
            
        except Exception as e:
            print(f"❌ Error adding symbol configuration: {e}")
            return False
    
    async def run_interactive(self):
        """Run interactive symbol switcher."""
        print("🔄 Symbol Switcher Tool")
        print("=" * 40)
        
        while True:
            try:
                self.print_current_config()
                
                print(f"\nOptions:")
                print("1. Switch to different symbol")
                print("2. Add new symbol configuration")
                print("3. Validate current symbol")
                print("4. Refresh configuration")
                print("5. Exit")
                
                choice = input("\nSelect option (1-5): ").strip()
                
                if choice == "1":
                    symbol = input("Enter symbol to switch to (e.g., BTCUSDC): ").strip().upper()
                    if symbol:
                        await self.switch_symbol(symbol)
                    
                elif choice == "2":
                    symbol = input("Enter new symbol to add (e.g., ETHUSDT): ").strip().upper()
                    if symbol:
                        if await self.validate_symbol(symbol):
                            self.add_symbol_config(symbol)
                        else:
                            print(f"❌ Symbol {symbol} validation failed")
                    
                elif choice == "3":
                    current_symbol = self.symbol_manager.get_current_symbol()
                    await self.validate_symbol(current_symbol)
                    
                elif choice == "4":
                    self.config_manager = ConfigManager(self.config_path)
                    print("✅ Configuration refreshed")
                    
                elif choice == "5":
                    print("👋 Goodbye!")
                    break
                    
                else:
                    print("❌ Invalid option")
                    
                input("\nPress Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                input("\nPress Enter to continue...")


async def main():
    """Main entry point."""
    config_path = "config/config.json"
    
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            print("Usage: python symbol_switcher.py [config_path]")
            print("  config_path: Path to configuration file (default: config/config.json)")
            return
        config_path = sys.argv[1]
    
    switcher = SymbolSwitcher(config_path)
    await switcher.run_interactive()


if __name__ == "__main__":
    asyncio.run(main())