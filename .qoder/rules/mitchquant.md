---
trigger: always_on
alwaysApply: true
---
1.请你用尽量简单的方法或代码文件解决问题，越简洁越好。
2.如没必要，或用户没要求，不要随意创建新的文件!请牢记
3.每次运行终端命令的时候，要先查看当前pwd目录情况，以免文件创建在错误的地方。
4.文档中输出启动python命令时需要使用python3命令
**代码组织和架构规范：**
1. **文件放置规则**：
   - 所有策略类和业务逻辑类文件必须放置在 `quant/strategies/` 目录下
   - 禁止在项目根目录直接创建策略相关的 `.py` 文件
   - 测试文件应放在 `tests/` 目录下
   - 文档文件应放在 `docs/` 目录下

2. **功能集成原则**：
   - 在开发新功能前，必须先分析现有代码架构和相似功能模块
   - 优先扩展现有类和模块，而不是创建新的独立文件
   - 如果功能与现有模块相似，应该：
     - 继承现有基类或接口
     - 复用现有的工具函数和配置系统
     - 遵循现有的命名约定和代码风格
   - 避免创建功能重复的文件，防止代码冗余和维护困难

3. **代码复用要求**：
   - 使用现有的钉钉通知系统
   - 复用现有的数据库连接和配置管理机制
   - 集成到现有的定时任务框架中
   - 遵循项目既定的日志记录和错误处理模式

4. **架构一致性**：
   - 新功能应与现有系统保持接口一致性
   - 使用相同的依赖注入和配置管理方式
   - 保持与现有模块的松耦合设计