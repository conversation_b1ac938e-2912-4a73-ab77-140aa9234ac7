#!/usr/bin/env python3
"""
安全的OKX交易系统启动脚本
确保从.env文件正确加载API密钥，不在代码中硬编码
"""

import os
import sys
import asyncio
from datetime import datetime

# 首先加载环境变量
from dotenv import load_dotenv
load_dotenv()

sys.path.insert(0, '.')

def log(message):
    """统一的日志输出"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {message}")

def verify_env_variables():
    """验证环境变量是否正确配置"""
    log("🔒 验证环境变量配置...")
    
    # 检查OKX API密钥
    okx_key = os.getenv('OKX_ACCESS_KEY')
    okx_secret = os.getenv('OKX_SECRET_KEY')
    okx_passphrase = os.getenv('OKX_PASSPHRASE')
    
    print(f"  OKX_ACCESS_KEY: {'✅ 已设置' if okx_key else '❌ 未设置'}")
    print(f"  OKX_SECRET_KEY: {'✅ 已设置' if okx_secret else '❌ 未设置'}")
    print(f"  OKX_PASSPHRASE: {'✅ 已设置' if okx_passphrase else '❌ 未设置'}")
    
    if not (okx_key and okx_secret and okx_passphrase):
        log("❌ OKX API密钥未完整配置，请检查.env文件")
        log("   .env文件应包含:")
        log("   OKX_ACCESS_KEY=your_access_key")
        log("   OKX_SECRET_KEY=your_secret_key") 
        log("   OKX_PASSPHRASE=your_passphrase")
        return False
    
    log("✅ 所有环境变量配置正确")
    return True

def verify_system_config():
    """验证系统配置"""
    log("⚙️ 验证系统配置...")
    
    try:
        from quant.config_manager import config
        from quant.exchange_client import exchange_client
        
        # 检查配置文件
        exchanges = config.get('EXCHANGES', {})
        auto_trader = config.get('AUTO_TRADER', {})
        
        default_exchange = exchanges.get('default')
        trader_exchange = auto_trader.get('exchange')
        
        log(f"  默认交易所: {default_exchange}")
        log(f"  自动交易交易所: {trader_exchange}")
        
        # 检查exchange_client
        current_exchange = exchange_client.get_exchange_name()
        log(f"  当前交易所客户端: {current_exchange}")
        
        if current_exchange != 'okx':
            log("❌ 系统配置错误：未使用OKX作为交易所")
            return False
            
        log("✅ 系统配置验证通过")
        return True
        
    except Exception as e:
        log(f"❌ 系统配置验证失败: {e}")
        return False

async def test_okx_connection():
    """测试OKX连接"""
    log("📡 测试OKX API连接...")
    
    try:
        from quant.exchange_client import exchange_client
        
        # 测试数据获取
        klines = await exchange_client.get_klines(symbol='BTCUSDT', interval='1m', limit=1)
        if klines and len(klines) > 0:
            price = float(klines[-1][4])
            log(f"✅ OKX API连接成功，当前BTC价格: ${price:,.2f}")
            return True
        else:
            log("❌ OKX API连接失败：无法获取数据")
            return False
            
    except Exception as e:
        log(f"❌ OKX API连接失败: {e}")
        return False

async def start_trading_system():
    """启动交易系统"""
    log("🚀 启动OKX交易系统...")
    
    try:
        # 导入并启动主系统
        from quant.main import TradingSystem
        
        trading_system = TradingSystem()
        await trading_system.initialize()
        
        log("✅ 交易系统初始化成功")
        log("🔄 启动定时任务调度器...")
        
        trading_system.setup_scheduler()
        trading_system.scheduler.start()
        trading_system.running = True
        
        log("🎉 OKX交易系统启动成功!")
        log("系统正在使用安全的.env配置运行...")
        log("按 Ctrl+C 停止系统")
        
        # 保持系统运行
        while trading_system.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        log("👋 收到停止信号，正在关闭系统...")
        trading_system.running = False
        trading_system.scheduler.shutdown()
        log("✅ 系统已安全关闭")
        
    except Exception as e:
        log(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("=" * 60)
    print("🔐 安全的OKX交易系统启动程序")
    print("=" * 60)
    
    # 步骤1：验证环境变量
    if not verify_env_variables():
        return False
    
    # 步骤2：验证系统配置
    if not verify_system_config():
        return False
    
    # 步骤3：测试OKX连接
    if not await test_okx_connection():
        return False
    
    # 步骤4：启动交易系统
    await start_trading_system()
    
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except Exception as e:
        log(f"❌ 启动过程出错: {e}")
        exit(1)