#!/bin/bash
# OKX交易系统安全启动脚本
# 基于OKX API集成规范，确保API密钥安全和系统配置正确

set -e  # 任何命令失败时退出

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_ROOT"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# 检查.env文件
check_env_file() {
    if [ ! -f ".env" ]; then
        error ".env文件不存在，请创建.env文件并配置OKX API密钥"
    fi
    
    # 检查.env文件权限
    local perm=$(stat -f "%p" .env 2>/dev/null | tail -c 4)
    if [[ "$perm" != "0600" ]]; then
        warn ".env文件权限不安全 ($perm)，建议设置为600"
        read -p "是否自动修复权限? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            chmod 600 .env
            log ".env文件权限已修复为600"
        fi
    fi
    
    log "✅ .env文件检查通过"
}

# 加载环境变量
load_env_vars() {
    if [ -f ".env" ]; then
        set -a  # 自动导出变量
        source .env
        set +a
        log "✅ 环境变量已加载"
    else
        error ".env文件不存在"
    fi
}

# 验证OKX API密钥
verify_okx_credentials() {
    local missing_vars=()
    
    if [ -z "$OKX_ACCESS_KEY" ]; then
        missing_vars+=("OKX_ACCESS_KEY")
    fi
    
    if [ -z "$OKX_SECRET_KEY" ]; then
        missing_vars+=("OKX_SECRET_KEY")
    fi
    
    if [ -z "$OKX_PASSPHRASE" ]; then
        missing_vars+=("OKX_PASSPHRASE")
    fi
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        error "缺少OKX API环境变量: ${missing_vars[*]}"
    fi
    
    log "✅ OKX API密钥环境变量验证通过"
}

# 检查虚拟环境
check_virtual_env() {
    if [ -f "trading_system_venv/bin/activate" ]; then
        source trading_system_venv/bin/activate
        log "✅ 虚拟环境已激活: $(which python)"
    else
        warn "虚拟环境不存在，使用系统Python: $(which python)"
    fi
}

# 检查Python依赖
check_dependencies() {
    info "检查Python依赖..."
    
    python -c "
import sys
required_modules = ['pandas', 'numpy', 'scipy', 'quant', 'dotenv']
missing_modules = []

for module in required_modules:
    try:
        __import__(module)
    except ImportError:
        missing_modules.append(module)

if missing_modules:
    print(f'❌ 缺少依赖: {missing_modules}')
    print('请运行: pip install -r requirements.txt')
    sys.exit(1)
else:
    print('✅ 所有依赖正常')
" || error "依赖检查失败"
    
    log "✅ Python依赖检查通过"
}

# 验证配置文件
verify_config() {
    local config_file=${1:-"config/config.json"}
    
    # 如果是相对路径，转换为绝对路径
    if [[ "$config_file" != /* ]]; then
        config_file="$PROJECT_ROOT/$config_file"
    fi
    
    if [ ! -f "$config_file" ]; then
        error "配置文件不存在: $config_file"
    fi
    
    # 验证JSON格式
    python -c "import json; json.load(open('$config_file'))" || error "配置文件JSON格式错误"
    
    log "✅ 配置文件验证通过: $config_file"
    echo "$config_file"
}

# 运行系统验证
run_system_verification() {
    info "运行系统完整性验证..."
    
    if [ -f "verify_okx_system.py" ]; then
        python verify_okx_system.py || error "系统验证失败，请检查配置"
        log "✅ 系统验证通过"
    else
        warn "系统验证脚本不存在，跳过验证"
    fi
}

# 启动主程序
start_main_program() {
    local config_file="$1"
    
    # 设置环境变量
    export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
    export PYTHONUNBUFFERED=1
    
    # 选择主程序文件
    local main_file=""
    if [ -f "start_okx_trading_system.py" ]; then
        main_file="start_okx_trading_system.py"
        log "使用安全启动脚本: $main_file"
    elif [ -f "main.py" ]; then
        main_file="main.py"
        log "使用主程序: $main_file"
    elif [ -f "main_refactored.py" ]; then
        main_file="main_refactored.py"
        log "使用重构主程序: $main_file"
    else
        error "找不到主程序文件"
    fi
    
    # 显示启动信息
    echo
    echo "==========================================="
    echo "      🚀 OKX交易系统安全启动"
    echo "==========================================="
    echo "项目路径: $PROJECT_ROOT"
    echo "配置文件: $config_file"
    echo "主程序: $main_file"
    echo "Python: $(which python)"
    echo "环境: ${ENVIRONMENT:-development}"
    echo "==========================================="
    echo
    echo "🔒 安全提示: API密钥已从.env文件安全加载"
    echo "📊 系统将使用OKX作为主要数据源"
    echo "⚡ 按 Ctrl+C 可优雅停止系统"
    echo
    
    # 启动系统
    if [[ "$main_file" == "start_okx_trading_system.py" ]]; then
        # 使用安全启动脚本，不需要传递配置文件参数
        python "$main_file"
    else
        # 传统启动方式
        python "$main_file" "$config_file"
    fi
}

# 主函数
main() {
    local config_file=${1:-"config/config.json"}
    
    echo "==========================================="
    echo "      🔐 OKX交易系统启动检查"
    echo "==========================================="
    
    # 1. 检查.env文件
    check_env_file
    
    # 2. 加载环境变量
    load_env_vars
    
    # 3. 验证OKX API密钥
    verify_okx_credentials
    
    # 4. 检查虚拟环境
    check_virtual_env
    
    # 5. 检查Python依赖
    check_dependencies
    
    # 6. 验证配置文件
    config_file=$(verify_config "$config_file")
    
    # 7. 运行系统验证
    run_system_verification
    
    log "🎉 所有检查通过，启动交易系统..."
    
    # 8. 启动主程序
    start_main_program "$config_file"
}

# 错误处理
trap 'error "启动过程被中断"' INT TERM

# 执行主函数
main "$@"
