{"test_summary": {"timestamp": "2025-08-17T02:18:04.441748", "total_duration": 0.4092118740081787, "total_workflows": 5, "successful_workflows": 4, "workflow_success_rate": 80.0, "total_steps": 28, "completed_steps": 21, "step_completion_rate": 75.0, "overall_status": "PASS"}, "workflow_results": [{"workflow_name": "complete_trading_lifecycle", "success": false, "duration": 9.107589721679688e-05, "steps_completed": 1, "steps_total": 8, "success_rate": 12.5, "error_message": "<quant.services.market_analysis_service.MarketAnalysisService object at 0x123219160> does not have the attribute '_generate_trading_signals'"}, {"workflow_name": "signal_generation_workflow", "success": true, "duration": 0.35581493377685547, "steps_completed": 6, "steps_total": 6, "success_rate": 100.0, "error_message": null}, {"workflow_name": "risk_management_workflow", "success": true, "duration": 0.2546236515045166, "steps_completed": 5, "steps_total": 5, "success_rate": 100.0, "error_message": null}, {"workflow_name": "data_consistency_workflow", "success": true, "duration": 0.30474424362182617, "steps_completed": 4, "steps_total": 4, "success_rate": 100.0, "error_message": null}, {"workflow_name": "fault_tolerance_workflow", "success": true, "duration": 0.40601539611816406, "steps_completed": 5, "steps_total": 5, "success_rate": 100.0, "error_message": null}], "recommendations": ["修复 complete_trading_lifecycle 工作流: <quant.services.market_analysis_service.MarketAnalysisService object at 0x123219160> does not have the attribute '_generate_trading_signals'"]}