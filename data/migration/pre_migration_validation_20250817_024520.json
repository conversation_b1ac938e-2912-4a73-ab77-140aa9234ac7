{"validation_time": "2025-08-17T02:45:20.825030", "summary": {"total_tests": 34, "passed_tests": 31, "warnings": 0, "critical_failures": 3, "ready_for_migration": false}, "categories": {"Configuration": {"total": 8, "passed": 6, "failed": 2, "critical_failed": 2}, "Dependencies": {"total": 8, "passed": 8, "failed": 0, "critical_failed": 0}, "File Permissions": {"total": 5, "passed": 5, "failed": 0, "critical_failed": 0}, "Database": {"total": 5, "passed": 5, "failed": 0, "critical_failed": 0}, "External Services": {"total": 2, "passed": 1, "failed": 1, "critical_failed": 1}, "New Architecture": {"total": 3, "passed": 3, "failed": 0, "critical_failed": 0}, "System Resources": {"total": 3, "passed": 3, "failed": 0, "critical_failed": 0}}, "detailed_results": [{"category": "Configuration", "test_name": "Config File config.json", "passed": true, "message": "File exists", "details": {"file": "config.json", "exists": true}, "critical": true, "timestamp": "2025-08-17T02:45:18.455600"}, {"category": "Configuration", "test_name": "Config File config.development.json", "passed": true, "message": "File exists", "details": {"file": "config.development.json", "exists": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.455957"}, {"category": "Configuration", "test_name": "BINANCE.API_KEY", "passed": false, "message": "Missing or empty", "details": {"section": "BINANCE", "key": "API_KEY", "has_value": false}, "critical": true, "timestamp": "2025-08-17T02:45:18.456204"}, {"category": "Configuration", "test_name": "BINANCE.SECRET_KEY", "passed": false, "message": "Missing or empty", "details": {"section": "BINANCE", "key": "SECRET_KEY", "has_value": false}, "critical": true, "timestamp": "2025-08-17T02:45:18.456234"}, {"category": "Configuration", "test_name": "BINANCE.BASE_URL", "passed": true, "message": "Configured", "details": {"section": "BINANCE", "key": "BASE_URL", "has_value": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.456253"}, {"category": "Configuration", "test_name": "DATABASE.PATH", "passed": true, "message": "Configured", "details": {"section": "DATABASE", "key": "PATH", "has_value": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.456269"}, {"category": "Configuration", "test_name": "RISK_MANAGEMENT.MAX_DAILY_LOSS", "passed": true, "message": "Configured", "details": {"section": "RISK_MANAGEMENT", "key": "MAX_DAILY_LOSS", "has_value": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.456286"}, {"category": "Configuration", "test_name": "RISK_MANAGEMENT.POSITION_SIZE_PERCENT", "passed": true, "message": "Configured", "details": {"section": "RISK_MANAGEMENT", "key": "POSITION_SIZE_PERCENT", "has_value": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.456299"}, {"category": "Dependencies", "test_name": "Requirements File", "passed": true, "message": "requirements.txt exists", "details": {"file": "requirements.txt"}, "critical": false, "timestamp": "2025-08-17T02:45:18.456349"}, {"category": "Dependencies", "test_name": "Package aiohttp", "passed": true, "message": "Package aiohttp listed in requirements", "details": {"package": "aiohttp"}, "critical": false, "timestamp": "2025-08-17T02:45:18.456542"}, {"category": "Dependencies", "test_name": "Package websockets", "passed": true, "message": "Package websockets listed in requirements", "details": {"package": "websockets"}, "critical": false, "timestamp": "2025-08-17T02:45:18.456574"}, {"category": "Dependencies", "test_name": "Package numpy", "passed": true, "message": "Package numpy listed in requirements", "details": {"package": "numpy"}, "critical": false, "timestamp": "2025-08-17T02:45:18.456591"}, {"category": "Dependencies", "test_name": "Package pandas", "passed": true, "message": "Package pandas listed in requirements", "details": {"package": "pandas"}, "critical": false, "timestamp": "2025-08-17T02:45:18.456604"}, {"category": "Dependencies", "test_name": "Package python-dotenv", "passed": true, "message": "Package python-dotenv listed in requirements", "details": {"package": "python-dotenv"}, "critical": false, "timestamp": "2025-08-17T02:45:18.456617"}, {"category": "Dependencies", "test_name": "Package apscheduler", "passed": true, "message": "Package apscheduler listed in requirements", "details": {"package": "apscheduler"}, "critical": false, "timestamp": "2025-08-17T02:45:18.456630"}, {"category": "Dependencies", "test_name": "Critical Packages", "passed": true, "message": "All critical packages importable", "details": {}, "critical": false, "timestamp": "2025-08-17T02:45:18.456644"}, {"category": "File Permissions", "test_name": "File main.py", "passed": true, "message": "Permissions OK: read=True, write=True", "details": {"file": "main.py", "readable": true, "writable": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.456777"}, {"category": "File Permissions", "test_name": "File main_refactored.py", "passed": true, "message": "Permissions OK: read=True, write=True", "details": {"file": "main_refactored.py", "readable": true, "writable": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.456860"}, {"category": "File Permissions", "test_name": "File config.json", "passed": true, "message": "Permissions OK: read=True, write=True", "details": {"file": "config.json", "readable": true, "writable": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.456962"}, {"category": "File Permissions", "test_name": "File trading_system.db", "passed": true, "message": "Permissions OK: read=True, write=True", "details": {"file": "trading_system.db", "readable": true, "writable": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.457410"}, {"category": "File Permissions", "test_name": "Logs Directory", "passed": true, "message": "Logs directory writable: True", "details": {"directory": "logs", "writable": true}, "critical": false, "timestamp": "2025-08-17T02:45:18.457507"}, {"category": "Database", "test_name": "Database Status", "passed": true, "message": "Database status: healthy", "details": {"status": "healthy", "response_time_ms": 1.81, "connection_retries": 0, "pool_size": 10, "pool_overflow": -9, "recovery_enabled": true, "last_health_check": "2025-08-17T02:45:18.457869"}, "critical": true, "timestamp": "2025-08-17T02:45:18.459693"}, {"category": "Database", "test_name": "Table trade_signals", "passed": true, "message": "Table exists with 0 records", "details": {"table": "trade_signals", "count": 0}, "critical": false, "timestamp": "2025-08-17T02:45:18.460439"}, {"category": "Database", "test_name": "Table trade_results", "passed": true, "message": "Table exists with 0 records", "details": {"table": "trade_results", "count": 0}, "critical": false, "timestamp": "2025-08-17T02:45:18.461463"}, {"category": "Database", "test_name": "Table system_logs", "passed": true, "message": "Table exists with 0 records", "details": {"table": "system_logs", "count": 0}, "critical": false, "timestamp": "2025-08-17T02:45:18.462287"}, {"category": "Database", "test_name": "Recent Trades", "passed": true, "message": "Found 0 recent trades", "details": {"recent_trades_count": 0}, "critical": false, "timestamp": "2025-08-17T02:45:18.469189"}, {"category": "External Services", "test_name": "Binance API", "passed": false, "message": "Binance API connection failed: Binance API credentials not found. Please set BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY environment variables or ensure they exist in the config file.", "details": {"error": "Binance API credentials not found. Please set BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY environment variables or ensure they exist in the config file."}, "critical": true, "timestamp": "2025-08-17T02:45:18.469284"}, {"category": "External Services", "test_name": "Network Connectivity", "passed": true, "message": "Network connection to Binance successful", "details": {"status_code": 200}, "critical": false, "timestamp": "2025-08-17T02:45:19.380689"}, {"category": "New Architecture", "test_name": "Orchestrator Creation", "passed": true, "message": "TradingSystemOrchestrator created successfully", "details": {}, "critical": false, "timestamp": "2025-08-17T02:45:19.822155"}, {"category": "New Architecture", "test_name": "Service Container", "passed": true, "message": "Service container initialized with 5 services", "details": {"total_services": 5, "enabled_services": 5, "instantiated_services": 0, "running_services": 0, "initialization_order": ["market_analysis", "settlement", "risk_management", "health_monitoring", "system_metrics"], "service_configs": {"market_analysis": {"enabled": true, "service_class": "MarketAnalysisService", "dependencies": []}, "settlement": {"enabled": true, "service_class": "SettlementService", "dependencies": []}, "risk_management": {"enabled": true, "service_class": "RiskManagementService", "dependencies": []}, "health_monitoring": {"enabled": true, "service_class": "HealthMonitoringService", "dependencies": ["risk_management"]}, "system_metrics": {"enabled": true, "service_class": "SystemMetricsService", "dependencies": []}}}, "critical": false, "timestamp": "2025-08-17T02:45:19.822192"}, {"category": "New Architecture", "test_name": "Service Health", "passed": true, "message": "Services health: 0/0 services ready", "details": {}, "critical": false, "timestamp": "2025-08-17T02:45:19.822215"}, {"category": "System Resources", "test_name": "CPU Usage", "passed": true, "message": "CPU usage: 24.0%", "details": {"cpu_percent": 24.0}, "critical": false, "timestamp": "2025-08-17T02:45:20.824323"}, {"category": "System Resources", "test_name": "Memory Usage", "passed": true, "message": "Memory usage: 81.9%", "details": {"memory_percent": 81.9, "available_gb": 2.8949127197265625}, "critical": false, "timestamp": "2025-08-17T02:45:20.824542"}, {"category": "System Resources", "test_name": "Disk Space", "passed": true, "message": "Disk usage: 24.6%", "details": {"disk_percent": 24.6, "free_gb": 666.0768280029297}, "critical": false, "timestamp": "2025-08-17T02:45:20.824761"}]}