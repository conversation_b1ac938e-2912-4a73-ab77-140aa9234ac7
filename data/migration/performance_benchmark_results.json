{"total_benchmark_time": 0.2835719585418701, "metrics_collected": 5, "performance_analysis": {"regression_detected": false, "performance_improvements": [], "performance_regressions": [], "overall_assessment": "performance_maintained", "detailed_analysis": {}}, "performance_report": "============================================================\\n📊 重构系统性能分析报告\\n============================================================\\n\\n✅ 总体评估: 性能基本保持\\n✅ 重构没有造成性能回归\\n\\n📈 详细性能数据:\\n\\n💡 推荐行动:\\n\\n   ✅ 可以部署重构系统\\n   📝 建议监控生产环境性能指标", "raw_metrics": [{"operation": "system_initialization", "execution_time": 0.006392832845449448, "memory_used_mb": 0.09375, "memory_peak_mb": 0.06545448303222656, "cpu_percent": 0.0}, {"operation": "service_lifecycle", "execution_time": 0.001712209079414606, "memory_used_mb": 0.015625, "memory_peak_mb": 0.03414154052734375, "cpu_percent": 0.0}, {"operation": "configuration_loading", "execution_time": 0.004928667098283768, "memory_used_mb": 0.015625, "memory_peak_mb": 0.039600372314453125, "cpu_percent": 0.0}, {"operation": "service_access", "execution_time": 4.212465137243271e-05, "memory_used_mb": 0.0, "memory_peak_mb": 0.0032091140747070312, "cpu_percent": 0.0}, {"operation": "concurrent_operations", "execution_time": 0.1078446670435369, "memory_used_mb": 0.15625, "memory_peak_mb": 0.14208984375, "cpu_percent": 0.0}]}