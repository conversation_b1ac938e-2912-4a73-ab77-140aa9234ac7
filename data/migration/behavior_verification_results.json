{"overall_status": "SUCCESS", "success_rate": 85.71428571428571, "total_time": 0.0074770450592041016, "detailed_results": {"initialization": {"original_init_time": null, "refactored_init_time": 0.0015506744384765625, "initialization_steps_match": true, "services_configured": 5, "error_handling_works": true}, "service_lifecycle": {"services_start_successfully": false, "services_stop_gracefully": true, "service_health_monitoring": true, "dependency_order_correct": true}, "configuration": {"config_loading_works": true, "environment_variables_loaded": true, "service_configuration_valid": true, "fallback_handling_works": true}, "error_handling": {"service_isolation_works": true, "graceful_degradation_works": true, "error_recovery_works": true, "logging_error_capture": false}}}