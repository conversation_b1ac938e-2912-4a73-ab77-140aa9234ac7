{"validation_time": "2025-08-17T02:42:45.418403", "summary": {"total_tests": 34, "passed_tests": 19, "warnings": 5, "critical_failures": 10, "ready_for_migration": false}, "categories": {"Configuration": {"total": 8, "passed": 2, "failed": 6, "critical_failed": 2}, "Dependencies": {"total": 8, "passed": 4, "failed": 4, "critical_failed": 4}, "File Permissions": {"total": 5, "passed": 5, "failed": 0, "critical_failed": 0}, "Database": {"total": 5, "passed": 1, "failed": 4, "critical_failed": 3}, "External Services": {"total": 2, "passed": 1, "failed": 1, "critical_failed": 1}, "New Architecture": {"total": 3, "passed": 3, "failed": 0, "critical_failed": 0}, "System Resources": {"total": 3, "passed": 3, "failed": 0, "critical_failed": 0}}, "detailed_results": [{"category": "Configuration", "test_name": "Config File config.json", "passed": true, "message": "File exists", "details": {"file": "config.json", "exists": true}, "critical": true, "timestamp": "2025-08-17T02:42:42.713431"}, {"category": "Configuration", "test_name": "Config File config.development.json", "passed": true, "message": "File exists", "details": {"file": "config.development.json", "exists": true}, "critical": false, "timestamp": "2025-08-17T02:42:42.713560"}, {"category": "Configuration", "test_name": "BINANCE.API_KEY", "passed": false, "message": "Missing or empty", "details": {"section": "BINANCE", "key": "API_KEY", "has_value": false}, "critical": true, "timestamp": "2025-08-17T02:42:42.713767"}, {"category": "Configuration", "test_name": "BINANCE.SECRET_KEY", "passed": false, "message": "Missing or empty", "details": {"section": "BINANCE", "key": "SECRET_KEY", "has_value": false}, "critical": true, "timestamp": "2025-08-17T02:42:42.713794"}, {"category": "Configuration", "test_name": "BINANCE.BASE_URL", "passed": false, "message": "Missing or empty", "details": {"section": "BINANCE", "key": "BASE_URL", "has_value": false}, "critical": false, "timestamp": "2025-08-17T02:42:42.713811"}, {"category": "Configuration", "test_name": "DATABASE.PATH", "passed": false, "message": "Missing or empty", "details": {"section": "DATABASE", "key": "PATH", "has_value": false}, "critical": false, "timestamp": "2025-08-17T02:42:42.713826"}, {"category": "Configuration", "test_name": "RISK_MANAGEMENT.MAX_DAILY_LOSS", "passed": false, "message": "Missing or empty", "details": {"section": "RISK_MANAGEMENT", "key": "MAX_DAILY_LOSS", "has_value": false}, "critical": false, "timestamp": "2025-08-17T02:42:42.713842"}, {"category": "Configuration", "test_name": "RISK_MANAGEMENT.POSITION_SIZE_PERCENT", "passed": false, "message": "Missing or empty", "details": {"section": "RISK_MANAGEMENT", "key": "POSITION_SIZE_PERCENT", "has_value": false}, "critical": false, "timestamp": "2025-08-17T02:42:42.713855"}, {"category": "Dependencies", "test_name": "Requirements File", "passed": true, "message": "requirements.txt exists", "details": {"file": "requirements.txt"}, "critical": false, "timestamp": "2025-08-17T02:42:42.713964"}, {"category": "Dependencies", "test_name": "Package aiohttp", "passed": false, "message": "Package aiohttp missing from requirements", "details": {"package": "aiohttp"}, "critical": true, "timestamp": "2025-08-17T02:42:42.714191"}, {"category": "Dependencies", "test_name": "Package websockets", "passed": false, "message": "Package websockets missing from requirements", "details": {"package": "websockets"}, "critical": true, "timestamp": "2025-08-17T02:42:42.714238"}, {"category": "Dependencies", "test_name": "Package numpy", "passed": false, "message": "Package numpy missing from requirements", "details": {"package": "numpy"}, "critical": true, "timestamp": "2025-08-17T02:42:42.714260"}, {"category": "Dependencies", "test_name": "Package pandas", "passed": true, "message": "Package pandas listed in requirements", "details": {"package": "pandas"}, "critical": false, "timestamp": "2025-08-17T02:42:42.714275"}, {"category": "Dependencies", "test_name": "Package python-dotenv", "passed": true, "message": "Package python-dotenv listed in requirements", "details": {"package": "python-dotenv"}, "critical": false, "timestamp": "2025-08-17T02:42:42.714289"}, {"category": "Dependencies", "test_name": "Package apscheduler", "passed": false, "message": "Package apscheduler missing from requirements", "details": {"package": "apscheduler"}, "critical": true, "timestamp": "2025-08-17T02:42:42.714303"}, {"category": "Dependencies", "test_name": "Critical Packages", "passed": true, "message": "All critical packages importable", "details": {}, "critical": false, "timestamp": "2025-08-17T02:42:42.714319"}, {"category": "File Permissions", "test_name": "File main.py", "passed": true, "message": "Permissions OK: read=True, write=True", "details": {"file": "main.py", "readable": true, "writable": true}, "critical": false, "timestamp": "2025-08-17T02:42:42.714455"}, {"category": "File Permissions", "test_name": "File main_refactored.py", "passed": true, "message": "Permissions OK: read=True, write=True", "details": {"file": "main_refactored.py", "readable": true, "writable": true}, "critical": false, "timestamp": "2025-08-17T02:42:42.714578"}, {"category": "File Permissions", "test_name": "File config.json", "passed": true, "message": "Permissions OK: read=True, write=True", "details": {"file": "config.json", "readable": true, "writable": true}, "critical": false, "timestamp": "2025-08-17T02:42:42.714667"}, {"category": "File Permissions", "test_name": "File trading_system.db", "passed": true, "message": "Permissions OK: read=True, write=True", "details": {"file": "trading_system.db", "readable": true, "writable": true}, "critical": false, "timestamp": "2025-08-17T02:42:42.714773"}, {"category": "File Permissions", "test_name": "Logs Directory", "passed": true, "message": "Logs directory writable: True", "details": {"directory": "logs", "writable": true}, "critical": false, "timestamp": "2025-08-17T02:42:42.714850"}, {"category": "Database", "test_name": "Database Status", "passed": true, "message": "Database status: healthy", "details": {"status": "healthy", "response_time_ms": 3.08, "connection_retries": 0, "pool_size": 10, "pool_overflow": -9, "recovery_enabled": true, "last_health_check": "2025-08-17T02:42:42.715293"}, "critical": true, "timestamp": "2025-08-17T02:42:42.718395"}, {"category": "Database", "test_name": "Table trade_signals", "passed": false, "message": "Table check failed: 'DatabaseManager' object has no attribute 'execute_query'", "details": {"table": "trade_signals", "error": "'DatabaseManager' object has no attribute 'execute_query'"}, "critical": true, "timestamp": "2025-08-17T02:42:42.718435"}, {"category": "Database", "test_name": "Table trade_results", "passed": false, "message": "Table check failed: 'DatabaseManager' object has no attribute 'execute_query'", "details": {"table": "trade_results", "error": "'DatabaseManager' object has no attribute 'execute_query'"}, "critical": true, "timestamp": "2025-08-17T02:42:42.718458"}, {"category": "Database", "test_name": "Table system_logs", "passed": false, "message": "Table check failed: 'DatabaseManager' object has no attribute 'execute_query'", "details": {"table": "system_logs", "error": "'DatabaseManager' object has no attribute 'execute_query'"}, "critical": true, "timestamp": "2025-08-17T02:42:42.718474"}, {"category": "Database", "test_name": "Recent Trades", "passed": false, "message": "Failed to fetch recent trades: 'DatabaseManager' object has no attribute 'get_recent_trades'", "details": {"error": "'DatabaseManager' object has no attribute 'get_recent_trades'"}, "critical": false, "timestamp": "2025-08-17T02:42:42.718489"}, {"category": "External Services", "test_name": "Binance API", "passed": false, "message": "Binance API connection failed: Binance API credentials not found. Please set BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY environment variables or ensure they exist in the config file.", "details": {"error": "Binance API credentials not found. Please set BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY environment variables or ensure they exist in the config file."}, "critical": true, "timestamp": "2025-08-17T02:42:42.718555"}, {"category": "External Services", "test_name": "Network Connectivity", "passed": true, "message": "Network connection to Binance successful", "details": {"status_code": 200}, "critical": false, "timestamp": "2025-08-17T02:42:43.738661"}, {"category": "New Architecture", "test_name": "Orchestrator Creation", "passed": true, "message": "TradingSystemOrchestrator created successfully", "details": {}, "critical": false, "timestamp": "2025-08-17T02:42:44.411570"}, {"category": "New Architecture", "test_name": "Service Container", "passed": true, "message": "Service container initialized with 5 services", "details": {"total_services": 5, "enabled_services": 5, "instantiated_services": 0, "running_services": 0, "initialization_order": ["market_analysis", "settlement", "risk_management", "health_monitoring", "system_metrics"], "service_configs": {"market_analysis": {"enabled": true, "service_class": "MarketAnalysisService", "dependencies": []}, "settlement": {"enabled": true, "service_class": "SettlementService", "dependencies": []}, "risk_management": {"enabled": true, "service_class": "RiskManagementService", "dependencies": []}, "health_monitoring": {"enabled": true, "service_class": "HealthMonitoringService", "dependencies": ["risk_management"]}, "system_metrics": {"enabled": true, "service_class": "SystemMetricsService", "dependencies": []}}}, "critical": false, "timestamp": "2025-08-17T02:42:44.411608"}, {"category": "New Architecture", "test_name": "Service Health", "passed": true, "message": "Services health: 0/0 services ready", "details": {}, "critical": false, "timestamp": "2025-08-17T02:42:44.411629"}, {"category": "System Resources", "test_name": "CPU Usage", "passed": true, "message": "CPU usage: 24.3%", "details": {"cpu_percent": 24.3}, "critical": false, "timestamp": "2025-08-17T02:42:45.416825"}, {"category": "System Resources", "test_name": "Memory Usage", "passed": true, "message": "Memory usage: 81.6%", "details": {"memory_percent": 81.6, "available_gb": 2.94580078125}, "critical": false, "timestamp": "2025-08-17T02:42:45.417060"}, {"category": "System Resources", "test_name": "Disk Space", "passed": true, "message": "Disk usage: 24.6%", "details": {"disk_percent": 24.6, "free_gb": 666.0769462585449}, "critical": false, "timestamp": "2025-08-17T02:42:45.417411"}]}