{"LOG": {"level": "info", "path": "./logs", "name": "error.log", "console": true, "backup_count": 7, "clear": false, "rotation_mode": "size", "max_bytes": 20971520, "when": "D", "interval": 1, "utc": true, "async": true, "queue_size": 10000, "retention_days": 14}, "BINANCE": {"API_KEY": "configured_via_env_vars", "SECRET_KEY": "configured_via_env_vars", "BASE_URL": "https://api.binance.com", "comment": "API credentials loaded from environment variables BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY"}, "DATABASE": {"PATH": "data/trading_system.db", "TIMESERIES_PATH": "data/timeseries.db", "CONNECTION_TIMEOUT": 30}, "PLATFORMS": {"binance": {"comment": "API credentials loaded from environment variables BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY"}, "okx": {"comment": "API credentials loaded from environment variables OKX_ACCESS_KEY, OKX_SECRET_KEY, and OKX_PASSPHRASE"}}, "PROXY": null, "DINGTALK": "https://oapi.dingtalk.com/robot/send?access_token=test_webhook_disabled_for_development", "CONFIDENCE_SCORING": {"weights": {"trend": 0.35, "momentum": 0.3, "volatility": 0.15, "volume": 0.12, "market_regime": 0.08}, "thresholds": {"minimum": 0.7, "strong": 0.85, "maximum": 0.95}, "indicator_weights": {"rsi": 0.85, "macd": 0.95, "bollinger_bands": 0.8, "volume_profile": 0.65, "adx": 0.9, "stochastic": 0.75, "williams_r": 0.65, "cci": 0.75, "mfi": 0.7}, "market_regime_adjustments": {"bullish": 1.15, "bearish": 1.05, "sideways": 0.85, "volatile": 0.75}}, "SIMPLE_BET_CONTROL": {"enabled": true, "fixed_bet_amount": 150.0, "ignore_confidence_adjustment": false, "min_confidence_for_full_bet": 0.4, "comment": "简化下注金额控制：enabled=true时直接使用fixed_bet_amount作为下注金额"}, "RISK_MANAGEMENT": {"enabled": true, "allow_signals_when_suspended": true, "MAX_DAILY_LOSS": 1000.0, "POSITION_SIZE_PERCENT": 1.5, "max_daily_loss_percent": 10.0, "max_consecutive_losses": 3, "max_position_size_usdt": 1000.0, "min_position_size_usdt": 10.0, "base_position_size_usdt": 150.0, "volatility_threshold": 0.15, "confidence_threshold": 0.45, "max_affordable_ratio": 1.0, "account_state": {"balance_usdt": 10000.0}}, "SYSTEM_MONITORING": {"enabled": true, "metrics_interval": 60, "alert_thresholds": {"cpu_usage": 0.9, "memory_usage": 0.9, "disk_usage": 0.85, "api_latency": 5.0, "websocket_disconnections": 5}, "health_check_interval": 300, "focus": "trading_critical"}, "PERFORMANCE_OPTIMIZATION": {"cache_ttl": 300, "max_concurrent_operations": 5, "query_timeout": 30, "enable_query_optimization": true, "archive_old_data": true, "archive_days_threshold": 90}, "SCHEDULING": {"analysis_minutes": [9, 39], "timezone": "Asia/Shanghai"}, "RISK_FILTERS": {"low_conf_block_threshold": 0.35, "low_conf_reduce_threshold": 0.65, "severe_trend_misalignment": 0.1, "low_vol_threshold": 0.005}, "ENTRY_WINDOW": {"enabled": true, "dynamic_thresholds": {"base_net_break_points": 30.0, "base_max_pullback_pts": 50.0, "base_min_vol_threshold_pct": 0.0008, "use_atr_adaptive": true, "atr_multiplier": 0.2, "pullback_multiplier": 1.4, "min_vol_floor_pct": 0.0004}, "segment_relax": {"strict_0_5": 1.0, "base_5_10": 0.85, "relaxed_10_15": 0.7}, "safety_valve": {"no_entry_roots": 2, "temporary_relax_pct": 0.12, "low_size_signal": true, "low_size_ratio": 0.05}, "direction_bias": {"long_weak_trend_boost": -0.15, "short_weak_trend_boost": -0.05}, "micro": {"micro_break_pts": 12.0, "retest_pullback_ratio": 0.4, "min_body_ratio": 0.6, "consecutive_closes": 2, "lookback_1m": 20}, "promotion": {"enabled": true, "net_ratio": 0.95, "adverse_mult": 1.0, "late_net_ratio": 0.9, "minutes": [12, 15], "require_micro": true, "strong_opp_block_thr": 0.2}}, "MAKER_ORDER": {"enabled": true, "price_strategy": "BALANCED", "buy_offset_bps": 5.0, "sell_offset_bps": 5.0, "initial_timeout": 30, "retry_timeout": 20, "max_retries": 3, "allow_partial_fill": true, "emergency_threshold": 90, "use_orderbook": true, "orderbook_level": 3, "max_slippage_bps": 20.0, "comment": "Maker order configuration for limit orders to save on fees"}, "EXCHANGES": {"default": "okx", "binance": {"MAKER_ORDER": {"enabled": true, "price_strategy": "BALANCED", "buy_offset_bps": 5.0, "sell_offset_bps": 5.0, "initial_timeout": 30, "max_retries": 2}}, "okx": {"MAKER_ORDER": {"enabled": true, "price_strategy": "AGGRESSIVE", "buy_offset_bps": 10.0, "sell_offset_bps": 10.0, "initial_timeout": 25, "max_retries": 3, "trade_mode": "isolated"}}}, "AUTO_TRADER": {"enabled": true, "emergency_stop": false, "symbol": "BTCUSDT", "min_order_usdt": 10.0, "max_order_usdt": 1000.0, "max_position_minutes": 30, "order_mode": "SMART", "use_limit_for_open": true, "use_limit_for_close": true, "urgency_threshold": 0.85, "EXTENSION_RULES": {"adverse_pct_stop": 0.008, "disable_on_high_vol": false, "high_vol_threshold_pct": 0.015}}, "TRADING_SYMBOLS": {"BTCUSDT": {"enabled": true, "min_order_usdt": 10.0, "max_order_usdt": 1000.0, "max_position_minutes": 30, "adverse_pct_stop": 0.008, "high_vol_threshold_pct": 0.015, "comment": "Bitcoin with lowest fees for USDT pairs"}, "BTCUSDC": {"enabled": true, "min_order_usdt": 10.0, "max_order_usdt": 1000.0, "max_position_minutes": 30, "adverse_pct_stop": 0.008, "high_vol_threshold_pct": 0.015, "comment": "Bitcoin with lower trading fees than USDT"}, "ETHUSDT": {"enabled": false, "min_order_usdt": 10.0, "max_order_usdt": 800.0, "max_position_minutes": 25, "adverse_pct_stop": 0.012, "high_vol_threshold_pct": 0.02, "comment": "Ethereum - higher volatility requires different parameters"}}, "POSITION_EXIT": {"take_profit_pct": 0.02, "stop_loss_pct": 0.01, "min_hold_minutes": 5, "max_hold_minutes": 30, "default_exit_minutes": 10, "enable_signal_reversal_exit": true, "check_interval_seconds": 10}, "SIMPLE_EXIT": {"exit_before_kline_end_minutes": 2, "min_hold_minutes": 1.0, "profit_hold_minutes": 20.0, "loss_hold_minutes": 10.0, "use_dynamic_exit": true, "max_exit_retries": 3, "retry_backoff_seconds": 2.0, "comment": "动态退出时间：盈利时持仓20分钟让利润奔跑，亏损时10分钟快速止损，提高策略整体盈利能力"}, "SERVICES": {"market_analysis": {"enabled": true}, "settlement": {"enabled": true, "max_concurrent": 20}, "risk_management": {"enabled": true}, "health_monitoring": {"enabled": true}, "system_metrics": {"enabled": true}}}