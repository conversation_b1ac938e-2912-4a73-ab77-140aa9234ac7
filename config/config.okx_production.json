{"extends": "config.json", "comment": "OKX Production configuration - extends base config with OKX-specific settings", "EXCHANGES": {"default": "okx", "active_exchanges": ["okx"], "multi_exchange_mode": false, "okx": {"enabled": true, "api_credentials": {"comment": "API credentials should be set via environment variables for security", "access_key": "${OKX_ACCESS_KEY}", "secret_key": "${OKX_SECRET_KEY}", "passphrase": "${OKX_PASSPHRASE}"}, "trading_settings": {"trade_mode": "isolated", "leverage": 10, "margin_mode": "fixed", "position_mode": "hedge", "symbols": ["BTCUSDT", "ETHUSDT"], "default_symbol": "BTCUSDT"}, "api_limits": {"rate_limit_per_second": 20, "order_limit_per_second": 10, "max_open_orders": 50}, "maker_order": {"enabled": true, "price_strategy": "AGGRESSIVE", "buy_offset_bps": 8.0, "sell_offset_bps": 8.0, "initial_timeout": 25, "retry_timeout": 15, "max_retries": 3}, "risk_controls": {"max_position_size_usdt": 5000.0, "max_daily_trades": 100, "max_open_positions": 5, "force_reduce_only": false}}, "binance": {"enabled": false, "comment": "Binance disabled in OKX production mode"}}, "PLATFORMS": {"okx": {"access_key": "${OKX_ACCESS_KEY}", "secret_key": "${OKX_SECRET_KEY}", "passphrase": "${OKX_PASSPHRASE}", "api_base_url": "https://www.okx.com", "ws_base_url": "wss://ws.okx.com:8443/ws/v5/public", "ws_private_url": "wss://ws.okx.com:8443/ws/v5/private", "testnet": false}}, "AUTO_TRADER": {"enabled": true, "exchange": "okx", "symbol": "BTCUSDT", "min_order_usdt": 10.0, "max_order_usdt": 1000.0, "order_mode": "SMART", "use_exchange_specific_settings": true}, "RISK_MANAGEMENT": {"enabled": true, "exchange_specific": {"okx": {"max_daily_loss": 2000.0, "max_position_size_usdt": 5000.0, "min_position_size_usdt": 10.0, "base_position_size_usdt": 200.0, "max_leverage": 20, "default_leverage": 10}}}, "SIMPLE_EXIT": {"exchange_overrides": {"okx": {"profit_hold_minutes": 25.0, "loss_hold_minutes": 8.0, "use_dynamic_exit": true}}}, "MONITORING": {"exchange_health_check": {"enabled": true, "interval_seconds": 60, "exchanges": ["okx"], "check_balance": true, "check_positions": true, "check_open_orders": true, "alert_on_anomaly": true}}, "NOTIFICATIONS": {"exchange_specific_alerts": {"okx": {"order_filled": true, "order_canceled": true, "position_opened": true, "position_closed": true, "liquidation_warning": true, "api_error": true}}}, "LOGGING": {"exchange_logs": {"okx": {"level": "info", "file": "logs/okx_trading.log", "max_size_mb": 50, "backup_count": 10}}}}