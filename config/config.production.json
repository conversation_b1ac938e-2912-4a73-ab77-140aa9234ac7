{"LOG": {"level": "info", "path": "/var/log/trading-system", "name": "trading.log", "console": false, "backup_count": 10, "clear": false}, "PLATFORMS": {"binance": {"access_key": "configured_via_env_vars", "secret_key": "configured_via_env_vars", "comment": "API credentials loaded from environment variables BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY"}}, "PROXY": null, "DINGTALK": "https://oapi.dingtalk.com/robot/send?access_token=your_production_webhook_here", "ENVIRONMENT": "production", "DEBUG": false}