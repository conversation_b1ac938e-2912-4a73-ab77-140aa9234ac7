{"LOG": {"level": "debug", "path": "./logs", "name": "dev.log", "console": true, "backup_count": 3, "clear": false}, "PLATFORMS": {"binance": {"access_key": "configured_via_env_vars", "secret_key": "configured_via_env_vars", "comment": "API credentials loaded from environment variables BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY"}}, "PROXY": null, "DINGTALK": "https://oapi.dingtalk.com/robot/send?access_token=50347ec7087108ecc2ef7fa8039ce30a3f09bf361df089fc19ef4df09bf174a5", "ENVIRONMENT": "production", "DEBUG": false}