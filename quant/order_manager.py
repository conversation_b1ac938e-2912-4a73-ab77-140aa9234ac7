# quant/order_manager.py

from typing import Any, Dict, Optional

from quant.exchange.exchange_factory import ExchangeFactory
from quant.strategies.limit_order_executor import limit_order_executor, OrderContext
from quant.config_manager import config
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class OrderManager:
    """Unified order manager for multiple exchanges"""

    def __init__(self, exchange_name: str):
        self.exchange = ExchangeFactory.get_exchange(exchange_name)
        logger.info(f"OrderManager initialized for {self.exchange.get_exchange_name()}")

    async def place_order(self, context: OrderContext, order_type: str = "LIMIT") -> Dict[str, Any]:
        if order_type.upper() == "LIMIT":
            return await self.exchange.place_limit_order(context)
        elif order_type.upper() == "MARKET":
            return await self.exchange.place_market_order(context)
        else:
            raise ValueError(f"Unsupported order type: {order_type}")

    async def get_order_status(self, order_id: str, symbol: str) -> Optional[Dict[str, Any]]:
        return await self.exchange.get_order(order_id, symbol)

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        return await self.exchange.cancel_order(order_id, symbol)

