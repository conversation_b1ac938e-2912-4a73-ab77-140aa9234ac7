#!/bin/bash

# 生产环境迁移脚本
# 从旧架构 (main.py) 迁移到新架构 (main_refactored.py)

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查先决条件
check_prerequisites() {
    log_info "检查迁移先决条件..."
    
    # 检查Python环境
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查必要文件
    if [[ ! -f "main.py" ]]; then
        log_error "原系统文件 main.py 不存在"
        exit 1
    fi
    
    if [[ ! -f "main_refactored.py" ]]; then
        log_error "新系统文件 main_refactored.py 不存在"
        exit 1
    fi
    
    if [[ ! -f "config.json" ]]; then
        log_error "配置文件 config.json 不存在"
        exit 1
    fi
    
    log_success "先决条件检查通过"
}

# 创建备份
create_backup() {
    log_info "创建系统备份..."
    
    BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据库
    if [[ -f "data/trading_system.db" ]]; then
        cp data/trading_system.db "$BACKUP_DIR/"
        log_info "备份主数据库完成"
    fi
    
    if [[ -f "data/timeseries.db" ]]; then
        cp data/timeseries.db "$BACKUP_DIR/"
        log_info "备份时序数据库完成"
    fi
    
    # 备份配置文件
    cp config.json "$BACKUP_DIR/"
    
    # 备份日志文件
    if [[ -d "logs" ]]; then
        cp -r logs "$BACKUP_DIR/"
    fi
    
    # 记录当前进程状态
    ps aux | grep -E "(main\.py|python)" > "$BACKUP_DIR/processes_before_migration.log" || true
    netstat -tlnp 2>/dev/null | grep -E ":(888|8888)" > "$BACKUP_DIR/ports_before_migration.log" || true
    
    echo "$BACKUP_DIR" > .last_backup_dir
    log_success "备份创建完成: $BACKUP_DIR"
}

# 部署监控系统
deploy_monitoring() {
    log_info "部署新监控系统..."
    
    # 检查端口8888是否被占用
    if netstat -tlnp 2>/dev/null | grep ":8888" &> /dev/null; then
        log_warning "端口8888已被占用，尝试终止占用进程"
        # 查找并终止占用端口8888的进程
        OLD_MONITOR_PID=$(netstat -tlnp 2>/dev/null | grep ":8888" | awk '{print $7}' | cut -d'/' -f1 | head -1)
        if [[ -n "$OLD_MONITOR_PID" && "$OLD_MONITOR_PID" != "-" ]]; then
            kill -TERM "$OLD_MONITOR_PID" 2>/dev/null || true
            sleep 5
        fi
    fi
    
    # 启动监控系统
    log_info "启动监控仪表板..."
    nohup python3 test_monitoring_system.py > logs/monitoring_system.log 2>&1 &
    MONITORING_PID=$!
    echo $MONITORING_PID > monitoring.pid
    
    # 等待监控系统启动
    sleep 10
    
    # 验证监控系统
    if ps -p $MONITORING_PID > /dev/null 2>&1; then
        log_success "监控系统启动成功 (PID: $MONITORING_PID)"
        log_info "监控面板地址: http://localhost:8888/dashboard"
    else
        log_error "监控系统启动失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试验证新架构..."
    
    # 运行关键工作流测试
    if [[ -f "tests/integration/test_critical_workflows.py" ]]; then
        if python3 tests/integration/test_critical_workflows.py; then
            log_success "集成测试通过"
        else
            log_warning "集成测试部分失败，但可以继续迁移"
        fi
    else
        log_warning "集成测试文件不存在，跳过测试"
    fi
}

# 停止旧系统
stop_old_system() {
    log_info "停止旧系统..."
    
    # 查找旧系统进程
    OLD_PID=$(ps aux | grep -E "python.*main\.py" | grep -v grep | awk '{print $2}' | head -1)
    
    if [[ -n "$OLD_PID" ]]; then
        log_info "发现旧系统进程 (PID: $OLD_PID)，正在停止..."
        
        # 发送TERM信号进行优雅关闭
        kill -TERM "$OLD_PID" 2>/dev/null || true
        
        # 等待进程优雅关闭
        local count=0
        while ps -p "$OLD_PID" > /dev/null 2>&1 && [[ $count -lt 30 ]]; do
            sleep 1
            count=$((count + 1))
        done
        
        # 如果仍在运行，强制终止
        if ps -p "$OLD_PID" > /dev/null 2>&1; then
            log_warning "进程未在30秒内正常关闭，强制终止"
            kill -KILL "$OLD_PID" 2>/dev/null || true
            sleep 2
        fi
        
        if ps -p "$OLD_PID" > /dev/null 2>&1; then
            log_error "无法停止旧系统进程"
            return 1
        else
            log_success "旧系统已成功停止"
        fi
    else
        log_info "未发现运行中的旧系统进程"
    fi
}

# 启动新系统
start_new_system() {
    log_info "启动新架构系统..."
    
    # 确保日志目录存在
    mkdir -p logs
    
    # 启动新系统
    nohup python3 main_refactored.py > logs/main_refactored.log 2>&1 &
    NEW_PID=$!
    echo $NEW_PID > main_refactored.pid
    
    log_info "新系统已启动 (PID: $NEW_PID)"
    
    # 等待系统初始化
    log_info "等待系统初始化..."
    sleep 15
    
    # 验证新系统状态
    if ps -p $NEW_PID > /dev/null 2>&1; then
        log_success "新系统运行正常"
    else
        log_error "新系统启动失败"
        log_error "检查日志文件: logs/main_refactored.log"
        return 1
    fi
    
    # 检查日志中的严重错误
    if [[ -f "logs/main_refactored.log" ]]; then
        if grep -i "fatal\|critical.*error" logs/main_refactored.log > /dev/null; then
            log_warning "日志中发现严重错误，请检查: logs/main_refactored.log"
        fi
    fi
}

# 验证系统功能
verify_system() {
    log_info "验证系统功能..."
    
    # 验证数据库连接
    log_info "验证数据库连接..."
    if python3 -c "
from quant.database_manager import DatabaseManager
db = DatabaseManager()
status = db.get_database_status()
print(f'Database status: {status}')
assert status['status'] == 'healthy', f'Database unhealthy: {status}'
print('✅ Database connection verified')
" 2>/dev/null; then
        log_success "数据库连接验证通过"
    else
        log_error "数据库连接验证失败"
        return 1
    fi
    
    # 验证API连接
    log_info "验证Binance API连接..."
    if timeout 30 python3 -c "
from quant.binance_client import BinanceClient
import asyncio

async def test_api():
    try:
        client = BinanceClient()
        await client.initialize()
        price = await client.get_current_price()
        print(f'Current BTC price: \${price:,.2f}')
        await client.close()
        print('✅ Binance API connection verified')
    except Exception as e:
        print(f'API connection failed: {e}')
        raise

asyncio.run(test_api())
" 2>/dev/null; then
        log_success "API连接验证通过"
    else
        log_warning "API连接验证失败，但系统可以继续运行"
    fi
    
    # 验证服务容器状态
    log_info "验证服务容器状态..."
    if python3 -c "
from quant.trading_system_orchestrator import TradingSystemOrchestrator
from quant.config_manager import ConfigManager
import asyncio

async def health_check():
    try:
        config = ConfigManager('config.json')
        orchestrator = TradingSystemOrchestrator(config)
        status = orchestrator.service_container.get_container_status()
        print(f'Services status: {status}')
        print('✅ Service container verified')
    except Exception as e:
        print(f'Service verification failed: {e}')
        raise

asyncio.run(health_check())
" 2>/dev/null; then
        log_success "服务容器验证通过"
    else
        log_warning "服务容器验证失败"
    fi
}

# 生成迁移报告
generate_migration_report() {
    log_info "生成迁移报告..."
    
    REPORT_FILE="migration_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=========================================="
        echo "生产环境迁移报告"
        echo "=========================================="
        echo "迁移时间: $(date)"
        echo "备份目录: $(cat .last_backup_dir 2>/dev/null || echo 'Unknown')"
        echo ""
        
        echo "系统状态:"
        echo "----------"
        if [[ -f "main_refactored.pid" ]]; then
            NEW_PID=$(cat main_refactored.pid)
            if ps -p $NEW_PID > /dev/null 2>&1; then
                echo "✅ 新系统运行中 (PID: $NEW_PID)"
            else
                echo "❌ 新系统未运行"
            fi
        fi
        
        if [[ -f "monitoring.pid" ]]; then
            MONITOR_PID=$(cat monitoring.pid)
            if ps -p $MONITOR_PID > /dev/null 2>&1; then
                echo "✅ 监控系统运行中 (PID: $MONITOR_PID)"
                echo "   监控面板: http://localhost:8888/dashboard"
            else
                echo "❌ 监控系统未运行"
            fi
        fi
        
        echo ""
        echo "端口状态:"
        echo "----------"
        netstat -tlnp 2>/dev/null | grep -E ":(888|8888)" | head -5 || echo "无相关端口监听"
        
        echo ""
        echo "进程状态:"
        echo "----------"
        ps aux | grep -E "(main|python)" | grep -v grep | head -10 || echo "无相关进程"
        
        echo ""
        echo "迁移完成时间: $(date)"
        
    } > "$REPORT_FILE"
    
    log_success "迁移报告已生成: $REPORT_FILE"
}

# 回滚函数
rollback() {
    log_warning "开始系统回滚..."
    
    # 停止新系统
    if [[ -f "main_refactored.pid" ]]; then
        NEW_PID=$(cat main_refactored.pid)
        if ps -p $NEW_PID > /dev/null 2>&1; then
            kill -TERM "$NEW_PID" 2>/dev/null || true
            sleep 5
            kill -KILL "$NEW_PID" 2>/dev/null || true
        fi
        rm -f main_refactored.pid
    fi
    
    # 停止监控系统
    if [[ -f "monitoring.pid" ]]; then
        MONITOR_PID=$(cat monitoring.pid)
        if ps -p $MONITOR_PID > /dev/null 2>&1; then
            kill -TERM "$MONITOR_PID" 2>/dev/null || true
            sleep 3
            kill -KILL "$MONITOR_PID" 2>/dev/null || true
        fi
        rm -f monitoring.pid
    fi
    
    # 恢复备份（如果需要）
    if [[ -f ".last_backup_dir" ]]; then
        BACKUP_DIR=$(cat .last_backup_dir)
        if [[ -d "$BACKUP_DIR" ]]; then
            log_info "从备份恢复数据库..."
            if [[ -f "$BACKUP_DIR/trading_system.db" ]]; then
                cp "$BACKUP_DIR/trading_system.db" data/
            fi
            if [[ -f "$BACKUP_DIR/timeseries.db" ]]; then
                cp "$BACKUP_DIR/timeseries.db" data/
            fi
            log_info "数据恢复完成"
        fi
    fi
    
    # 重启旧系统
    log_info "重启旧系统..."
    nohup python3 main.py > logs/main_rollback.log 2>&1 &
    echo $! > main_rollback.pid
    
    log_warning "回滚完成，旧系统已重启"
}

# 主函数
main() {
    echo "=========================================="
    echo "🚀 生产环境架构迁移脚本"
    echo "从 main.py 迁移到 main_refactored.py"
    echo "=========================================="
    echo
    
    # 设置错误处理
    trap 'log_error "迁移过程中出现错误"; echo "是否需要回滚? (y/N)"; read -r response; if [[ "$response" =~ ^[Yy]$ ]]; then rollback; fi; exit 1' ERR
    
    # 询问用户确认
    echo "⚠️  这将执行生产环境迁移，包括："
    echo "   - 停止当前运行的系统"
    echo "   - 部署新的架构"
    echo "   - 启动监控系统"
    echo
    read -p "确认继续迁移? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "迁移已取消"
        exit 0
    fi
    
    # 执行迁移步骤
    check_prerequisites
    create_backup
    deploy_monitoring
    run_integration_tests
    stop_old_system
    start_new_system
    verify_system
    generate_migration_report
    
    echo
    echo "=========================================="
    log_success "🎉 迁移成功完成!"
    echo "=========================================="
    echo
    echo "📊 系统状态:"
    echo "  - 新系统: main_refactored.py"
    echo "  - 监控面板: http://localhost:8888/dashboard"
    echo "  - 日志文件: logs/main_refactored.log"
    echo "  - 备份位置: $(cat .last_backup_dir 2>/dev/null || echo 'Unknown')"
    echo
    echo "📝 后续操作建议:"
    echo "  1. 监控系统运行30分钟，确保稳定"
    echo "  2. 检查交易信号生成和处理"
    echo "  3. 验证所有定时任务正常执行"
    echo "  4. 观察系统性能指标"
    echo
    echo "🔄 如需回滚，运行: ./migrate_to_production.sh --rollback"
}

# 处理命令行参数
if [[ "$1" == "--rollback" ]]; then
    rollback
    exit 0
fi

# 运行主函数
main "$@"