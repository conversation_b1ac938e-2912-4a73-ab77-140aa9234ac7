"""
OKX Client wrapper with BinanceClient-compatible interface
Provides seamless integration with existing trading system
"""

import asyncio
from typing import List, Optional, Any, Dict
from datetime import datetime
import pandas as pd

from quant.config_manager import config
from quant.platform.okx_swap import OkxSwapRestApi
from quant.utils.logger import get_logger
from quant.exceptions import APIError

logger = get_logger(__name__)


class OKXClient:
    """OKX client with BinanceClient-compatible interface"""
    
    def __init__(self):
        """Initialize OKX client"""
        # Get OKX configuration
        okx_config = config.get_platform_config("okx")
        
        # Initialize OKX REST API client
        self.api_client = OkxSwapRestApi(
            access_key=okx_config.get("access_key"),
            secret_key=okx_config.get("secret_key"),
            passphrase=okx_config.get("passphrase")
        )
        
        # Get symbol from AUTO_TRADER config
        auto_trader_config = config.get("AUTO_TRADER", {})
        self.default_symbol = auto_trader_config.get("symbol", "BTCUSDT")
        
        # Convert to OKX format (BTCUSDT -> BTC/USDT)
        if "/" not in self.default_symbol:
            # Convert BTCUSDT to BTC/USDT format
            if self.default_symbol.endswith("USDT"):
                base = self.default_symbol[:-4]
                self.default_symbol = f"{base}/USDT"
            elif self.default_symbol.endswith("USDC"):
                base = self.default_symbol[:-4]
                self.default_symbol = f"{base}/USDC"
        
        logger.info(f"OKX Client initialized with symbol: {self.default_symbol}")
    
    def _convert_interval(self, interval: str) -> str:
        """Convert interval format from Binance to OKX
        
        Args:
            interval: Binance-style interval (e.g., '30m', '1h', '1d')
            
        Returns:
            OKX-style interval (e.g., '30m', '1H', '1D')
        """
        # OKX uses lowercase for minutes, uppercase for hours/days
        if interval.endswith('m'):
            return interval.lower()
        else:
            return interval.upper()
    
    async def get_klines(self, symbol: str = None, interval: str = "30m", limit: int = 100) -> List[List]:
        """Get K-line data from OKX (BinanceClient-compatible interface)
        
        Args:
            symbol: Trading symbol (optional, uses default from config)
            interval: K-line interval (e.g., '30m', '1h', '1d')
            limit: Number of K-lines to retrieve
            
        Returns:
            List of K-line data in Binance format:
            [[timestamp, open, high, low, close, volume, ...], ...]
        """
        try:
            # Use default symbol if not provided
            if symbol is None:
                symbol = self.default_symbol
            
            # Ensure symbol is in OKX format (BTC/USDT)
            if "/" not in symbol:
                if symbol.endswith("USDT"):
                    base = symbol[:-4]
                    symbol = f"{base}/USDT"
                elif symbol.endswith("USDC"):
                    base = symbol[:-4]
                    symbol = f"{base}/USDC"
            
            # Convert interval to OKX format
            okx_interval = f"kline_{self._convert_interval(interval)}"
            
            logger.info(f"Fetching OKX klines: symbol={symbol}, interval={okx_interval}, limit={limit}")
            
            # Get klines from OKX
            result, error = await self.api_client.get_kline(
                symbol=symbol,
                interval=okx_interval
            )
            
            if error:
                logger.error(f"Failed to get OKX klines: {error}")
                raise APIError(f"OKX API error: {error}")
            
            if not result or "data" not in result:
                logger.error(f"Invalid OKX response: {result}")
                return []
            
            # OKX returns data in reverse chronological order
            klines_data = result["data"]
            
            # Convert OKX format to Binance format
            # OKX: [timestamp, open, high, low, close, volume, ...]
            # Binance: [timestamp, open, high, low, close, volume, close_time, quote_volume, trades, ...]
            binance_format_klines = []
            
            for kline in klines_data:
                # OKX timestamp is already in milliseconds
                timestamp = int(kline[0])
                open_price = float(kline[1])
                high_price = float(kline[2])
                low_price = float(kline[3])
                close_price = float(kline[4])
                volume = float(kline[5]) if len(kline) > 5 else 0.0
                
                # Create Binance-compatible format
                binance_kline = [
                    timestamp,           # Open time
                    str(open_price),     # Open
                    str(high_price),     # High
                    str(low_price),      # Low
                    str(close_price),    # Close
                    str(volume),         # Volume
                    timestamp + 1800000, # Close time (30 min later for 30m interval)
                    "0",                 # Quote asset volume (not available from OKX)
                    0,                   # Number of trades (not available)
                    "0",                 # Taker buy base asset volume
                    "0",                 # Taker buy quote asset volume
                    "0"                  # Ignore
                ]
                binance_format_klines.append(binance_kline)
            
            # Sort by timestamp (oldest first) to match Binance order
            binance_format_klines.sort(key=lambda x: x[0])
            
            # Limit the number of results
            if len(binance_format_klines) > limit:
                binance_format_klines = binance_format_klines[-limit:]
            
            logger.info(f"Successfully fetched {len(binance_format_klines)} klines from OKX")
            return binance_format_klines
            
        except Exception as e:
            logger.error(f"Error fetching OKX klines: {e}")
            raise APIError(f"Failed to get klines from OKX: {e}")
    
    async def get_account_balance(self) -> Dict[str, Any]:
        """Get account balance (compatible with BinanceClient)
        
        Returns:
            Account balance information
        """
        try:
            # Get USDT balance
            asset, error = await self.api_client.get_asset("USDT")
            
            if error:
                logger.error(f"Failed to get OKX balance: {error}")
                return {"balances": []}
            
            # Convert to Binance-like format
            return {
                "balances": [{
                    "asset": "USDT",
                    "free": asset.get("free", 0) if asset else 0,
                    "locked": asset.get("locked", 0) if asset else 0
                }]
            }
            
        except Exception as e:
            logger.error(f"Error getting OKX balance: {e}")
            return {"balances": []}
    
    async def get_symbol_ticker(self, symbol: str = None) -> Dict[str, Any]:
        """Get current ticker price (compatible with BinanceClient)
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Ticker information
        """
        try:
            # Use default symbol if not provided
            if symbol is None:
                symbol = self.default_symbol
            
            # Get trade data from OKX
            trade, error = await self.api_client.trade(symbol)
            
            if error:
                logger.error(f"Failed to get OKX ticker: {error}")
                return {}
            
            # Convert to Binance-like format
            return {
                "symbol": symbol.replace("/", ""),
                "price": str(trade.price) if trade else "0"
            }
            
        except Exception as e:
            logger.error(f"Error getting OKX ticker: {e}")
            return {}
    
    async def create_order(self, **kwargs) -> Dict[str, Any]:
        """Create order (placeholder for compatibility)
        
        Note: This should be handled by the exchange abstraction layer
        """
        logger.warning("create_order called on OKXClient - should use exchange abstraction")
        return {"orderId": None, "status": "error"}
    
    async def cancel_order(self, **kwargs) -> bool:
        """Cancel order (placeholder for compatibility)
        
        Note: This should be handled by the exchange abstraction layer
        """
        logger.warning("cancel_order called on OKXClient - should use exchange abstraction")
        return False


# Global instance will be created on-demand
okx_client = None

def get_okx_client() -> OKXClient:
    """Get or create the global OKX client instance"""
    global okx_client
    if okx_client is None:
        okx_client = OKXClient()
    return okx_client
