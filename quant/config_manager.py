"""
Configuration Management Module

Handles loading and providing access to configuration files with secure API key management.
"""

import json
import os
from pathlib import Path
from typing import Any, Optional


class ConfigManager:
    """Manages application configuration loading and access."""

    def __init__(self, config_path: str = "config/config.json"):
        self.config_path = Path(config_path)
        self._config: dict[str, Any] | None = None

    def load_config(self) -> dict[str, Any]:
        """Load configuration from JSON file."""
        if self._config is None:
            try:
                with open(self.config_path, encoding="utf-8") as f:
                    self._config = json.load(f)
            except FileNotFoundError:
                raise FileNotFoundError(
                    f"Configuration file not found: {self.config_path}"
                )
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in configuration file: {e}")

        return self._config

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        config = self.load_config()
        keys = key.split(".")
        value = config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def get_platform_config(self, platform: str) -> dict[str, Any]:
        """Get platform-specific configuration with secure API key loading."""
        # Get base platform config from JSON (without sensitive keys)
        platform_config = self.get(f"PLATFORMS.{platform}", {}).copy()
        
        # Override with environment variables for security
        if platform.lower() == "binance":
            env_access_key = os.getenv("BINANCE_ACCESS_KEY")
            env_secret_key = os.getenv("BINANCE_SECRET_KEY")
            
            if env_access_key and env_secret_key:
                platform_config["access_key"] = env_access_key
                platform_config["secret_key"] = env_secret_key
            elif not platform_config.get("access_key") or not platform_config.get("secret_key"):
                raise ValueError(
                    "Binance API credentials not found. Please set BINANCE_ACCESS_KEY and BINANCE_SECRET_KEY "
                    "environment variables or ensure they exist in the config file."
                )
                
        elif platform.lower() == "okx":
            env_access_key = os.getenv("OKX_ACCESS_KEY")
            env_secret_key = os.getenv("OKX_SECRET_KEY")
            env_passphrase = os.getenv("OKX_PASSPHRASE")
            
            if env_access_key and env_secret_key and env_passphrase:
                platform_config["access_key"] = env_access_key
                platform_config["secret_key"] = env_secret_key
                platform_config["passphrase"] = env_passphrase
            elif not (platform_config.get("access_key") and platform_config.get("secret_key") and platform_config.get("passphrase")):
                raise ValueError(
                    "OKX API credentials not found. Please set OKX_ACCESS_KEY, OKX_SECRET_KEY, and OKX_PASSPHRASE "
                    "environment variables or ensure they exist in the config file."
                )
        
        return platform_config

    def get_log_config(self) -> dict[str, Any]:
        """Get logging configuration."""
        return self.get("LOG", {})

    def get_dingtalk_config(self) -> str:
        """Get DingTalk webhook URL from environment variable or config file."""
        # Priority: environment variable > config file
        env_webhook = os.getenv("DINGTALK_WEBHOOK")
        if env_webhook:
            return env_webhook
        return self.get("DINGTALK", "")

    def get_proxy_config(self) -> str | None:
        """Get proxy configuration."""
        return self.get("PROXY")


# Global config instance
config = ConfigManager()
