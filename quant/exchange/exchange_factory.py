# quant/exchange/exchange_factory.py
"""
Exchange Factory with configuration management and singleton pattern

Provides unified interface for creating and managing exchange instances
with support for multiple exchanges running simultaneously.
"""

import asyncio
from typing import Dict, Optional, List
from quant.exchange.binance_exchange import BinanceExchange
from quant.exchange.okx_exchange import OkxExchange
from quant.exchange.exchange_interface import ExchangeInterface
from quant.config_manager import config
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class ExchangeFactory:
    """Factory class for creating and managing exchange instances"""
    
    # Singleton instances cache
    _instances: Dict[str, ExchangeInterface] = {}
    _lock = asyncio.Lock()
    
    # Supported exchanges mapping
    _exchange_classes = {
        "binance": BinanceExchange,
        "okx": OkxExchange,
    }
    
    @classmethod
    async def get_exchange(cls, exchange_name: Optional[str] = None, 
                          force_new: bool = False) -> ExchangeInterface:
        """Get exchange instance with singleton pattern
        
        Args:
            exchange_name: Name of the exchange (binance, okx)
                         If None, uses default from config
            force_new: Force create new instance instead of using cached
            
        Returns:
            Exchange instance implementing ExchangeInterface
        """
        # Get exchange name from config if not provided
        if exchange_name is None:
            exchange_name = cls._get_default_exchange()
        
        exchange_name = exchange_name.lower()
        
        # Validate exchange name
        if exchange_name not in cls._exchange_classes:
            raise ValueError(
                f"Exchange '{exchange_name}' not supported. "
                f"Supported exchanges: {list(cls._exchange_classes.keys())}"
            )
        
        async with cls._lock:
            # Return cached instance if exists and not forcing new
            if not force_new and exchange_name in cls._instances:
                logger.debug(f"Returning cached {exchange_name} instance")
                return cls._instances[exchange_name]
            
            # Create new instance
            logger.info(f"Creating new {exchange_name} exchange instance")
            exchange_class = cls._exchange_classes[exchange_name]
            exchange = exchange_class()
            
            # Initialize the exchange
            try:
                await exchange.initialize()
                logger.info(f"{exchange_name} exchange initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize {exchange_name}: {e}")
                raise
            
            # Cache the instance
            cls._instances[exchange_name] = exchange
            return exchange
    
    @classmethod
    async def get_multiple_exchanges(cls, exchange_names: List[str]) -> Dict[str, ExchangeInterface]:
        """Get multiple exchange instances
        
        Args:
            exchange_names: List of exchange names
            
        Returns:
            Dictionary mapping exchange name to instance
        """
        exchanges = {}
        for name in exchange_names:
            try:
                exchange = await cls.get_exchange(name)
                exchanges[name] = exchange
            except Exception as e:
                logger.error(f"Failed to get {name} exchange: {e}")
        return exchanges
    
    @classmethod
    async def get_all_configured_exchanges(cls) -> Dict[str, ExchangeInterface]:
        """Get all exchanges configured in config file
        
        Returns:
            Dictionary mapping exchange name to instance
        """
        configured = cls._get_configured_exchanges()
        return await cls.get_multiple_exchanges(configured)
    
    @classmethod
    def _get_default_exchange(cls) -> str:
        """Get default exchange from configuration
        
        Returns:
            Default exchange name
        """
        # Check EXCHANGES config section
        exchanges_config = config.get("EXCHANGES", {})
        default = exchanges_config.get("default")
        
        if default:
            return default
        
        # Fallback to checking which exchange has API keys configured
        if config.get("BINANCE", {}).get("API_KEY"):
            return "binance"
        
        # Check for OKX in PLATFORMS config
        platforms = config.get("PLATFORMS", {})
        if "okx" in platforms:
            return "okx"
        
        # Default to OKX for this project
        return "okx"
    
    @classmethod
    def _get_configured_exchanges(cls) -> List[str]:
        """Get list of all configured exchanges
        
        Returns:
            List of configured exchange names
        """
        configured = []
        
        # Check EXCHANGES config
        exchanges_config = config.get("EXCHANGES", {})
        for name in cls._exchange_classes.keys():
            if name in exchanges_config:
                configured.append(name)
        
        # Check legacy configs
        if config.get("BINANCE", {}).get("API_KEY"):
            if "binance" not in configured:
                configured.append("binance")
        
        platforms = config.get("PLATFORMS", {})
        if "okx" in platforms:
            if "okx" not in configured:
                configured.append("okx")
        
        return configured if configured else ["okx"]
    
    @classmethod
    async def close_all(cls):
        """Close all exchange connections"""
        async with cls._lock:
            for name, exchange in cls._instances.items():
                try:
                    await exchange.close()
                    logger.info(f"Closed {name} exchange connection")
                except Exception as e:
                    logger.error(f"Error closing {name} exchange: {e}")
            cls._instances.clear()
    
    @classmethod
    def get_supported_exchanges(cls) -> List[str]:
        """Get list of supported exchange names
        
        Returns:
            List of supported exchange names
        """
        return list(cls._exchange_classes.keys())
    
    @classmethod
    def is_exchange_supported(cls, exchange_name: str) -> bool:
        """Check if an exchange is supported
        
        Args:
            exchange_name: Name of the exchange
            
        Returns:
            True if exchange is supported
        """
        return exchange_name.lower() in cls._exchange_classes

