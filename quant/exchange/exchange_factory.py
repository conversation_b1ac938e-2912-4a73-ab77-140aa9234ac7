# quant/exchange/exchange_factory.py

from quant.exchange.binance_exchange import BinanceExchange
from quant.exchange.okx_exchange import OkxExchange
from quant.exchange.exchange_interface import ExchangeInterface


class ExchangeFactory:
    @staticmethod
    def get_exchange(exchange_name: str) -> ExchangeInterface:
        if exchange_name.lower() == "binance":
            return BinanceExchange()
        elif exchange_name.lower() == "okx":
            return OkxExchange()
        else:
            raise ValueError(f"Exchange {exchange_name} not supported")

