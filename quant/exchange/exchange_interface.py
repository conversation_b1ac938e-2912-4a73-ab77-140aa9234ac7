# quant/exchange/exchange_interface.py

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from quant.strategies.limit_order_executor import OrderContext


class ExchangeInterface(ABC):
    """Abstract base class for exchange integrations"""

    @abstractmethod
    async def place_limit_order(self, context: OrderContext) -> Dict[str, Any]:
        """Place a limit order"""
        pass

    @abstractmethod
    async def place_market_order(self, context: OrderContext) -> Dict[str, Any]:
        """Place a market order"""
        pass

    @abstractmethod
    async def get_order(self, order_id: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get order status"""
        pass

    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        pass

    @abstractmethod
    async def get_orderbook(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get orderbook"""
        pass

    @abstractmethod
    async def get_current_price(self, symbol: str) -> float:
        """Get current price"""
        pass

    @abstractmethod
    def get_exchange_name(self) -> str:
        """Get exchange name"""
        pass

