# quant/exchange/exchange_interface.py

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime


class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """Order status enumeration"""
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class PositionSide(Enum):
    """Position side enumeration"""
    LONG = "long"
    SHORT = "short"
    BOTH = "both"  # For one-way mode


@dataclass
class OrderRequest:
    """Unified order request structure"""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    position_side: Optional[PositionSide] = None
    reduce_only: bool = False
    client_order_id: Optional[str] = None
    time_in_force: str = "GTC"  # GTC, IOC, FOK


@dataclass
class Position:
    """Position information structure"""
    symbol: str
    side: PositionSide
    quantity: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    margin: float
    leverage: int


@dataclass
class Balance:
    """Balance information structure"""
    asset: str
    free: float
    locked: float
    total: float


class ExchangeInterface(ABC):
    """Abstract base class for exchange integrations"""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize exchange connection and verify credentials"""
        pass
    
    @abstractmethod
    async def close(self) -> None:
        """Close exchange connection and cleanup resources"""
        pass

    @abstractmethod
    async def place_order(self, request: OrderRequest) -> Dict[str, Any]:
        """Place an order with unified request structure"""
        pass

    @abstractmethod
    async def get_order(self, order_id: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get order status"""
        pass

    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        pass
    
    @abstractmethod
    async def cancel_all_orders(self, symbol: Optional[str] = None) -> int:
        """Cancel all open orders, returns number of canceled orders"""
        pass

    @abstractmethod
    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all open orders"""
        pass

    @abstractmethod
    async def get_positions(self, symbol: Optional[str] = None) -> List[Position]:
        """Get current positions"""
        pass
    
    @abstractmethod
    async def get_balance(self, asset: Optional[str] = None) -> List[Balance]:
        """Get account balance"""
        pass

    @abstractmethod
    async def get_orderbook(self, symbol: str, depth: int = 20) -> Dict[str, Any]:
        """Get orderbook with specified depth"""
        pass

    @abstractmethod
    async def get_current_price(self, symbol: str) -> float:
        """Get current price"""
        pass
    
    @abstractmethod
    async def get_klines(self, symbol: str, interval: str, limit: int = 100) -> List[List]:
        """Get kline/candlestick data"""
        pass
    
    @abstractmethod
    async def get_24hr_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get 24hr ticker statistics"""
        pass
    
    @abstractmethod
    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for a symbol"""
        pass

    @abstractmethod
    def get_exchange_name(self) -> str:
        """Get exchange name"""
        pass
    
    @abstractmethod
    def get_supported_symbols(self) -> List[str]:
        """Get list of supported trading symbols"""
        pass
    
    @abstractmethod
    def convert_symbol_to_exchange(self, symbol: str) -> str:
        """Convert unified symbol format to exchange-specific format"""
        pass
    
    @abstractmethod
    def convert_symbol_from_exchange(self, symbol: str) -> str:
        """Convert exchange-specific symbol format to unified format"""
        pass
    
    # Backward compatibility methods (will be deprecated)
    async def place_limit_order(self, context: Any) -> Dict[str, Any]:
        """Legacy method for backward compatibility"""
        from quant.strategies.limit_order_executor import OrderContext
        if isinstance(context, OrderContext):
            request = OrderRequest(
                symbol=context.symbol,
                side=OrderSide.BUY if context.side.upper() == "BUY" else OrderSide.SELL,
                order_type=OrderType.LIMIT,
                quantity=context.quantity,
                price=context.limit_price,
                position_side=PositionSide.LONG if context.position_side == "LONG" else PositionSide.SHORT,
                client_order_id=context.client_order_id
            )
            return await self.place_order(request)
        raise ValueError("Invalid context type")
    
    async def place_market_order(self, context: Any) -> Dict[str, Any]:
        """Legacy method for backward compatibility"""
        from quant.strategies.limit_order_executor import OrderContext
        if isinstance(context, OrderContext):
            request = OrderRequest(
                symbol=context.symbol,
                side=OrderSide.BUY if context.side.upper() == "BUY" else OrderSide.SELL,
                order_type=OrderType.MARKET,
                quantity=context.quantity,
                position_side=PositionSide.LONG if context.position_side == "LONG" else PositionSide.SHORT,
                client_order_id=context.client_order_id
            )
            return await self.place_order(request)
        raise ValueError("Invalid context type")

