# quant/exchange/binance_exchange.py

from typing import Any, Dict, List, Optional

from quant.binance_client import binance_client
from quant.exchange.exchange_interface import ExchangeInterface
from quant.strategies.limit_order_executor import OrderContext


class BinanceExchange(ExchangeInterface):
    """Binance exchange implementation"""

    def get_exchange_name(self) -> str:
        return "binance"

    async def place_limit_order(self, context: OrderContext) -> Dict[str, Any]:
        return await binance_client.place_limit_order_futures(
            symbol=context.symbol,
            side=context.side,
            price=context.limit_price,
            quantity=context.quantity,
            position_side=context.position_side,
            client_order_id=context.client_order_id
        )

    async def place_market_order(self, context: OrderContext) -> Dict[str, Any]:
        current_price = await self.get_current_price(context.symbol)
        notional_usdt = context.quantity * current_price

        return await binance_client.place_market_order_futures(
            symbol=context.symbol,
            side=context.side,
            notional_usdt=notional_usdt,
            position_side=context.position_side
        )

    async def get_order(self, order_id: str, symbol: str) -> Optional[Dict[str, Any]]:
        return await binance_client.get_order(symbol=symbol, orderId=order_id)

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        response = await binance_client.cancel_order(symbol=symbol, orderId=order_id)
        return response and response.get("status") == "CANCELED"

    async def get_orderbook(self, symbol: str) -> Optional[Dict[str, Any]]:
        return await binance_client.get_orderbook(symbol=symbol)

    async def get_current_price(self, symbol: str) -> float:
        return await binance_client.get_current_price(symbol=symbol)

