# quant/exchange/okx_exchange.py
"""
OKX Exchange Implementation with Full Async Support

Provides OKX-specific trading functionality with proper async/await patterns
and comprehensive error handling for maker orders.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from quant.platform.okx_swap import OkxSwapRestApi
from quant.exchange.exchange_interface import ExchangeInterface
from quant.strategies.limit_order_executor import OrderContext
from quant.config_manager import config
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class OkxOrderAdapter:
    """OKX order parameter adapter for handling exchange-specific formats"""
    
    @staticmethod
    def convert_symbol(symbol: str) -> str:
        """Convert symbol format: BTC/USDT → BTC-USDT-SWAP"""
        if "/" in symbol:
            base, quote = symbol.split("/")
            return f"{base}-{quote}-SWAP"
        return symbol
    
    @staticmethod
    def convert_position_side(side: str, position_side: Optional[str] = None) -> str:
        """Convert position side for OKX format"""
        if position_side:
            return position_side.lower()
        # Default position side based on order side
        return "long" if side.upper() == "BUY" else "short"
    
    @staticmethod
    def format_order_response(response: Dict[str, Any]) -> Dict[str, Any]:
        """Format OKX order response to standard format"""
        if not response or "data" not in response:
            return {"success": False, "error": "Invalid response"}
        
        data = response["data"][0] if response["data"] else {}
        return {
            "success": True,
            "order_id": data.get("ordId"),
            "client_order_id": data.get("clOrdId"),
            "status": data.get("state"),
            "timestamp": datetime.utcnow().isoformat()
        }


class OkxExchange(ExchangeInterface):
    """OKX exchange implementation with full async support"""

    def __init__(self):
        self.okx_client = OkxSwapRestApi()
        self.order_adapter = OkxOrderAdapter()
        self._initialized = False
        
    async def initialize(self):
        """Initialize OKX exchange connection"""
        if self._initialized:
            return
        
        # Verify API credentials
        try:
            # Test API connection with a simple request
            result, error = await self.okx_client.get_exchange_info()
            if error:
                logger.error(f"Failed to initialize OKX exchange: {error}")
                raise Exception(f"OKX initialization failed: {error}")
            
            self._initialized = True
            logger.info("OKX exchange initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing OKX exchange: {e}")
            raise

    def get_exchange_name(self) -> str:
        """Get exchange name identifier"""
        return "okx"

    def _get_trade_mode(self) -> str:
        """Get trade mode from configuration"""
        # First check exchange-specific config
        okx_config = config.get("EXCHANGES", {}).get("okx", {})
        maker_config = okx_config.get("MAKER_ORDER", {})
        
        # Fallback to global OKX config
        if not maker_config:
            maker_config = config.get("OKX_MAKER_ORDER", {})
        
        return maker_config.get("trade_mode", "isolated")

    async def place_limit_order(self, context: OrderContext) -> Dict[str, Any]:
        """Place a limit order on OKX exchange
        
        Args:
            context: Order context with order details
            
        Returns:
            Dict with order result including order_id and status
        """
        try:
            # Ensure exchange is initialized
            if not self._initialized:
                await self.initialize()
            
            # Convert symbol format for OKX
            inst_id = self.order_adapter.convert_symbol(context.symbol)
            
            # Prepare position side
            pos_side = self.order_adapter.convert_position_side(
                context.side,
                context.position_side
            )
            
            # Build order request body
            order_body = {
                "instId": inst_id,
                "tdMode": self._get_trade_mode(),
                "side": context.side.lower(),
                "posSide": pos_side,
                "ordType": "limit",
                "sz": str(context.quantity),
                "px": str(context.limit_price)
            }
            
            # Add client order ID if provided
            if context.client_order_id:
                order_body["clOrdId"] = context.client_order_id
            
            logger.info(f"Placing OKX limit order: {order_body}")
            
            # Execute order request
            result, error = await self.okx_client.request(
                method="POST",
                uri="/api/v5/trade/order",
                body=order_body,
                auth=True
            )
            
            if error:
                logger.error(f"OKX limit order failed: {error}")
                return {
                    "success": False,
                    "error": str(error),
                    "context": context.__dict__
                }
            
            # Format and return response
            formatted_response = self.order_adapter.format_order_response(result)
            logger.info(f"OKX limit order placed successfully: {formatted_response}")
            return formatted_response
            
        except Exception as e:
            logger.error(f"Exception placing OKX limit order: {e}")
            return {
                "success": False,
                "error": str(e),
                "context": context.__dict__
            }

    async def place_market_order(self, context: OrderContext) -> Dict[str, Any]:
        """Place a market order on OKX exchange
        
        Args:
            context: Order context with order details
            
        Returns:
            Dict with order result
        """
        try:
            # Ensure exchange is initialized
            if not self._initialized:
                await self.initialize()
            
            # Convert symbol format for OKX
            inst_id = self.order_adapter.convert_symbol(context.symbol)
            
            # Prepare position side
            pos_side = self.order_adapter.convert_position_side(
                context.side,
                context.position_side
            )
            
            # Build order request body
            order_body = {
                "instId": inst_id,
                "tdMode": self._get_trade_mode(),
                "side": context.side.lower(),
                "posSide": pos_side,
                "ordType": "market",
                "sz": str(context.quantity)
            }
            
            logger.info(f"Placing OKX market order: {order_body}")
            
            # Execute order request
            result, error = await self.okx_client.request(
                method="POST",
                uri="/api/v5/trade/order",
                body=order_body,
                auth=True
            )
            
            if error:
                logger.error(f"OKX market order failed: {error}")
                return {
                    "success": False,
                    "error": str(error),
                    "context": context.__dict__
                }
            
            # Format and return response
            formatted_response = self.order_adapter.format_order_response(result)
            logger.info(f"OKX market order placed successfully: {formatted_response}")
            return formatted_response
            
        except Exception as e:
            logger.error(f"Exception placing OKX market order: {e}")
            return {
                "success": False,
                "error": str(e),
                "context": context.__dict__
            }

    async def get_order(self, order_id: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get order details by order ID
        
        Args:
            order_id: Order ID to query
            symbol: Trading symbol
            
        Returns:
            Order details dict or None if error
        """
        try:
            response, error = await self.okx_client.get_order_status(
                symbol=symbol,
                order_no=order_id
            )
            
            if error:
                logger.error(f"Failed to get OKX order {order_id}: {error}")
                return None
            
            return response
            
        except Exception as e:
            logger.error(f"Exception getting OKX order {order_id}: {e}")
            return None

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order on OKX
        
        Args:
            order_id: Order ID to cancel
            symbol: Trading symbol
            
        Returns:
            True if successful, False otherwise
        """
        try:
            _, error = await self.okx_client.revoke_order(
                symbol=symbol,
                order_no=order_id
            )
            
            if error:
                logger.error(f"Failed to cancel OKX order {order_id}: {error}")
                return False
            
            logger.info(f"OKX order {order_id} cancelled successfully")
            return True
            
        except Exception as e:
            logger.error(f"Exception cancelling OKX order {order_id}: {e}")
            return False

    async def get_orderbook(self, symbol: str, depth: int = 5) -> Optional[Dict[str, Any]]:
        """Get orderbook for a symbol
        
        Args:
            symbol: Trading symbol
            depth: Orderbook depth (default 5)
            
        Returns:
            Orderbook data dict or None if error
        """
        try:
            response, error = await self.okx_client.get_orderbook(symbol=symbol)
            
            if error:
                logger.error(f"Failed to get OKX orderbook for {symbol}: {error}")
                return None
            
            return response
            
        except Exception as e:
            logger.error(f"Exception getting OKX orderbook: {e}")
            return None

    async def get_current_price(self, symbol: str) -> float:
        """Get current market price for a symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Current price as float, 0.0 if error
        """
        try:
            # Try to get last trade price
            response, error = await self.okx_client.get_trade(symbol=symbol)
            
            if not error and response and response.get("data"):
                price = float(response["data"][0]["px"])
                return price
            
            # Fallback to orderbook mid price
            orderbook = await self.get_orderbook(symbol)
            if orderbook and orderbook.get("data"):
                data = orderbook["data"][0]
                if data.get("asks") and data.get("bids"):
                    ask_price = float(data["asks"][0][0])
                    bid_price = float(data["bids"][0][0])
                    return (ask_price + bid_price) / 2
            
            logger.warning(f"Could not get price for {symbol}")
            return 0.0
            
        except Exception as e:
            logger.error(f"Exception getting OKX price for {symbol}: {e}")
            return 0.0
    
    async def get_balance(self, currency: str = "USDT") -> Optional[Dict[str, Any]]:
        """Get account balance for a currency
        
        Args:
            currency: Currency to query (default USDT)
            
        Returns:
            Balance dict or None if error
        """
        try:
            response, error = await self.okx_client.get_asset(currency=currency)
            
            if error:
                logger.error(f"Failed to get OKX balance for {currency}: {error}")
                return None
            
            return response
            
        except Exception as e:
            logger.error(f"Exception getting OKX balance: {e}")
            return None
    
    async def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get position for a symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Position dict or None if error
        """
        try:
            response, error = await self.okx_client.get_position(symbol=symbol)
            
            if error:
                logger.error(f"Failed to get OKX position for {symbol}: {error}")
                return None
            
            return response
            
        except Exception as e:
            logger.error(f"Exception getting OKX position: {e}")
            return None

