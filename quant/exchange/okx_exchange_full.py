# quant/exchange/okx_exchange_full.py
"""
Complete OKX Exchange Implementation with all required methods

Provides full implementation of ExchangeInterface for OKX exchange
with comprehensive error handling and data conversion.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime
import json

from quant.platform.okx_swap import OkxSwapRestApi
from quant.exchange.exchange_interface import (
    ExchangeInterface, OrderRequest, Position, Balance,
    OrderType, OrderSide, PositionSide, OrderStatus
)
from quant.config_manager import config
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class OkxExchangeFull(ExchangeInterface):
    """Complete OKX exchange implementation"""

    def __init__(self):
        """Initialize OKX exchange client"""
        # Get OKX configuration
        okx_config = config.get("PLATFORMS", {}).get("okx", {})
        
        # Initialize OKX REST API client
        self.api_client = OkxSwapRestApi(
            access_key=okx_config.get("access_key"),
            secret_key=okx_config.get("secret_key"),
            passphrase=okx_config.get("passphrase")
        )
        
        self._initialized = False
        self._supported_symbols = []
        
    async def initialize(self) -> None:
        """Initialize exchange connection and verify credentials"""
        if self._initialized:
            return
        
        try:
            # Test API connection
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/public/instruments",
                params={"instType": "SWAP"},
                auth=False
            )
            
            if error:
                raise Exception(f"Failed to initialize OKX: {error}")
            
            # Parse supported symbols
            if result and "data" in result:
                self._supported_symbols = [
                    inst["instId"] for inst in result["data"]
                ]
            
            # Verify account access
            account_result, account_error = await self.api_client.request(
                method="GET",
                uri="/api/v5/account/balance",
                auth=True
            )
            
            if account_error:
                logger.warning(f"Account verification failed: {account_error}")
            
            self._initialized = True
            logger.info("OKX exchange initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing OKX exchange: {e}")
            raise
    
    async def close(self) -> None:
        """Close exchange connection and cleanup resources"""
        self._initialized = False
        logger.info("OKX exchange connection closed")
    
    async def place_order(self, request: OrderRequest) -> Dict[str, Any]:
        """Place an order with unified request structure"""
        try:
            # Ensure initialized
            if not self._initialized:
                await self.initialize()
            
            # Convert symbol
            inst_id = self.convert_symbol_to_exchange(request.symbol)
            
            # Build order body
            order_body = {
                "instId": inst_id,
                "tdMode": self._get_trade_mode(),
                "side": request.side.value,
                "ordType": request.order_type.value,
                "sz": str(request.quantity)
            }
            
            # Add position side
            if request.position_side:
                order_body["posSide"] = request.position_side.value
            
            # Add price for limit orders
            if request.order_type == OrderType.LIMIT and request.price:
                order_body["px"] = str(request.price)
            
            # Add reduce only flag
            if request.reduce_only:
                order_body["reduceOnly"] = True
            
            # Add client order ID
            if request.client_order_id:
                order_body["clOrdId"] = request.client_order_id
            
            # Add time in force
            if request.time_in_force:
                order_body["tif"] = request.time_in_force
            
            logger.info(f"Placing OKX order: {order_body}")
            
            # Execute order
            result, error = await self.api_client.request(
                method="POST",
                uri="/api/v5/trade/order",
                body=order_body,
                auth=True
            )
            
            if error:
                return {
                    "success": False,
                    "error": str(error),
                    "request": request.__dict__
                }
            
            # Parse response
            if result and "data" in result and result["data"]:
                order_data = result["data"][0]
                return {
                    "success": True,
                    "order_id": order_data.get("ordId"),
                    "client_order_id": order_data.get("clOrdId"),
                    "status": self._convert_order_status(order_data.get("state")),
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            return {
                "success": False,
                "error": "Invalid response",
                "response": result
            }
            
        except Exception as e:
            logger.error(f"Error placing OKX order: {e}")
            return {
                "success": False,
                "error": str(e),
                "request": request.__dict__
            }
    
    async def get_order(self, order_id: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get order status"""
        try:
            inst_id = self.convert_symbol_to_exchange(symbol)
            
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/trade/order",
                params={"instId": inst_id, "ordId": order_id},
                auth=True
            )
            
            if error:
                logger.error(f"Failed to get order: {error}")
                return None
            
            if result and "data" in result and result["data"]:
                order = result["data"][0]
                return {
                    "order_id": order.get("ordId"),
                    "client_order_id": order.get("clOrdId"),
                    "symbol": self.convert_symbol_from_exchange(order.get("instId")),
                    "side": order.get("side"),
                    "type": order.get("ordType"),
                    "quantity": float(order.get("sz", 0)),
                    "filled_quantity": float(order.get("fillSz", 0)),
                    "price": float(order.get("px", 0)) if order.get("px") else None,
                    "avg_price": float(order.get("avgPx", 0)) if order.get("avgPx") else None,
                    "status": self._convert_order_status(order.get("state")),
                    "timestamp": order.get("cTime")
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting order: {e}")
            return None
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        try:
            inst_id = self.convert_symbol_to_exchange(symbol)
            
            body = {
                "instId": inst_id,
                "ordId": order_id
            }
            
            result, error = await self.api_client.request(
                method="POST",
                uri="/api/v5/trade/cancel-order",
                body=body,
                auth=True
            )
            
            if error:
                logger.error(f"Failed to cancel order: {error}")
                return False
            
            return result and result.get("code") == "0"
            
        except Exception as e:
            logger.error(f"Error canceling order: {e}")
            return False
    
    async def cancel_all_orders(self, symbol: Optional[str] = None) -> int:
        """Cancel all open orders"""
        try:
            params = {"instType": "SWAP"}
            if symbol:
                params["instId"] = self.convert_symbol_to_exchange(symbol)
            
            result, error = await self.api_client.request(
                method="POST",
                uri="/api/v5/trade/cancel-batch-orders",
                body=params,
                auth=True
            )
            
            if error:
                logger.error(f"Failed to cancel all orders: {error}")
                return 0
            
            if result and "data" in result:
                return len(result["data"])
            
            return 0
            
        except Exception as e:
            logger.error(f"Error canceling all orders: {e}")
            return 0
    
    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all open orders"""
        try:
            params = {"instType": "SWAP"}
            if symbol:
                params["instId"] = self.convert_symbol_to_exchange(symbol)
            
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/trade/orders-pending",
                params=params,
                auth=True
            )
            
            if error:
                logger.error(f"Failed to get open orders: {error}")
                return []
            
            orders = []
            if result and "data" in result:
                for order in result["data"]:
                    orders.append({
                        "order_id": order.get("ordId"),
                        "client_order_id": order.get("clOrdId"),
                        "symbol": self.convert_symbol_from_exchange(order.get("instId")),
                        "side": order.get("side"),
                        "type": order.get("ordType"),
                        "quantity": float(order.get("sz", 0)),
                        "filled_quantity": float(order.get("fillSz", 0)),
                        "price": float(order.get("px", 0)) if order.get("px") else None,
                        "status": self._convert_order_status(order.get("state")),
                        "timestamp": order.get("cTime")
                    })
            
            return orders
            
        except Exception as e:
            logger.error(f"Error getting open orders: {e}")
            return []
    
    async def get_positions(self, symbol: Optional[str] = None) -> List[Position]:
        """Get current positions"""
        try:
            params = {"instType": "SWAP"}
            if symbol:
                params["instId"] = self.convert_symbol_to_exchange(symbol)
            
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/account/positions",
                params=params,
                auth=True
            )
            
            if error:
                logger.error(f"Failed to get positions: {error}")
                return []
            
            positions = []
            if result and "data" in result:
                for pos in result["data"]:
                    if float(pos.get("pos", 0)) != 0:
                        positions.append(Position(
                            symbol=self.convert_symbol_from_exchange(pos.get("instId")),
                            side=PositionSide.LONG if pos.get("posSide") == "long" else PositionSide.SHORT,
                            quantity=abs(float(pos.get("pos", 0))),
                            entry_price=float(pos.get("avgPx", 0)),
                            mark_price=float(pos.get("markPx", 0)),
                            unrealized_pnl=float(pos.get("upl", 0)),
                            margin=float(pos.get("margin", 0)),
                            leverage=int(pos.get("lever", 1))
                        ))
            
            return positions
            
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    async def get_balance(self, asset: Optional[str] = None) -> List[Balance]:
        """Get account balance"""
        try:
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/account/balance",
                auth=True
            )
            
            if error:
                logger.error(f"Failed to get balance: {error}")
                return []
            
            balances = []
            if result and "data" in result and result["data"]:
                for detail in result["data"][0].get("details", []):
                    ccy = detail.get("ccy")
                    if asset and ccy != asset:
                        continue
                    
                    eq = float(detail.get("eq", 0))
                    frozen = float(detail.get("frozenBal", 0))
                    
                    balances.append(Balance(
                        asset=ccy,
                        free=eq - frozen,
                        locked=frozen,
                        total=eq
                    ))
            
            return balances
            
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return []
    
    async def get_orderbook(self, symbol: str, depth: int = 20) -> Dict[str, Any]:
        """Get orderbook with specified depth"""
        try:
            inst_id = self.convert_symbol_to_exchange(symbol)
            
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/market/books",
                params={"instId": inst_id, "sz": str(depth)},
                auth=False
            )
            
            if error:
                logger.error(f"Failed to get orderbook: {error}")
                return {}
            
            if result and "data" in result and result["data"]:
                book = result["data"][0]
                return {
                    "bids": [[float(b[0]), float(b[1])] for b in book.get("bids", [])],
                    "asks": [[float(a[0]), float(a[1])] for a in book.get("asks", [])],
                    "timestamp": book.get("ts")
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting orderbook: {e}")
            return {}
    
    async def get_current_price(self, symbol: str) -> float:
        """Get current price"""
        try:
            inst_id = self.convert_symbol_to_exchange(symbol)
            
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/market/ticker",
                params={"instId": inst_id},
                auth=False
            )
            
            if error:
                logger.error(f"Failed to get current price: {error}")
                return 0.0
            
            if result and "data" in result and result["data"]:
                return float(result["data"][0].get("last", 0))
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error getting current price: {e}")
            return 0.0
    
    async def get_klines(self, symbol: str, interval: str, limit: int = 100) -> List[List]:
        """Get kline/candlestick data"""
        try:
            inst_id = self.convert_symbol_to_exchange(symbol)
            bar = self._convert_interval(interval)
            
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/market/candles",
                params={"instId": inst_id, "bar": bar, "limit": str(limit)},
                auth=False
            )
            
            if error:
                logger.error(f"Failed to get klines: {error}")
                return []
            
            klines = []
            if result and "data" in result:
                for k in result["data"]:
                    klines.append([
                        int(k[0]),      # timestamp
                        k[1],           # open
                        k[2],           # high
                        k[3],           # low
                        k[4],           # close
                        k[5],           # volume
                        int(k[0]) + self._get_interval_ms(interval),  # close time
                        "0",            # quote volume
                        0,              # trades
                        "0",            # taker buy base
                        "0",            # taker buy quote
                        "0"             # ignore
                    ])
            
            # Sort by timestamp (oldest first)
            klines.sort(key=lambda x: x[0])
            return klines
            
        except Exception as e:
            logger.error(f"Error getting klines: {e}")
            return []
    
    async def get_24hr_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get 24hr ticker statistics"""
        try:
            inst_id = self.convert_symbol_to_exchange(symbol)
            
            result, error = await self.api_client.request(
                method="GET",
                uri="/api/v5/market/ticker",
                params={"instId": inst_id},
                auth=False
            )
            
            if error:
                logger.error(f"Failed to get ticker: {error}")
                return {}
            
            if result and "data" in result and result["data"]:
                ticker = result["data"][0]
                return {
                    "symbol": symbol,
                    "price": float(ticker.get("last", 0)),
                    "price_change": float(ticker.get("last", 0)) - float(ticker.get("sodUtc0", 0)),
                    "price_change_percent": float(ticker.get("sodUtc0", 0)) / float(ticker.get("last", 1)) * 100 if float(ticker.get("last", 0)) > 0 else 0,
                    "high": float(ticker.get("high24h", 0)),
                    "low": float(ticker.get("low24h", 0)),
                    "volume": float(ticker.get("vol24h", 0)),
                    "quote_volume": float(ticker.get("volCcy24h", 0)),
                    "open_time": int(ticker.get("ts", 0)) - 86400000,
                    "close_time": int(ticker.get("ts", 0))
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting 24hr ticker: {e}")
            return {}
    
    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for a symbol"""
        try:
            inst_id = self.convert_symbol_to_exchange(symbol)
            
            # Set leverage for both long and short positions
            for pos_side in ["long", "short"]:
                body = {
                    "instId": inst_id,
                    "lever": str(leverage),
                    "mgnMode": self._get_trade_mode(),
                    "posSide": pos_side
                }
                
                result, error = await self.api_client.request(
                    method="POST",
                    uri="/api/v5/account/set-leverage",
                    body=body,
                    auth=True
                )
                
                if error:
                    logger.error(f"Failed to set leverage for {pos_side}: {error}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting leverage: {e}")
            return False
    
    def get_exchange_name(self) -> str:
        """Get exchange name"""
        return "okx"
    
    def get_supported_symbols(self) -> List[str]:
        """Get list of supported trading symbols"""
        return [self.convert_symbol_from_exchange(s) for s in self._supported_symbols]
    
    def convert_symbol_to_exchange(self, symbol: str) -> str:
        """Convert unified symbol format to OKX format
        BTCUSDT -> BTC-USDT-SWAP
        """
        if "-" in symbol:
            return symbol  # Already in OKX format
        
        # Handle common formats
        if symbol.endswith("USDT"):
            base = symbol[:-4]
            return f"{base}-USDT-SWAP"
        elif symbol.endswith("USDC"):
            base = symbol[:-4]
            return f"{base}-USDC-SWAP"
        elif symbol.endswith("USD"):
            base = symbol[:-3]
            return f"{base}-USD-SWAP"
        
        # Fallback
        return f"{symbol}-USDT-SWAP"
    
    def convert_symbol_from_exchange(self, symbol: str) -> str:
        """Convert OKX symbol format to unified format
        BTC-USDT-SWAP -> BTCUSDT
        """
        if not symbol or "-" not in symbol:
            return symbol
        
        parts = symbol.split("-")
        if len(parts) >= 2:
            return f"{parts[0]}{parts[1]}"
        
        return symbol
    
    def _get_trade_mode(self) -> str:
        """Get trade mode from configuration"""
        okx_config = config.get("EXCHANGES", {}).get("okx", {})
        maker_config = okx_config.get("MAKER_ORDER", {})
        return maker_config.get("trade_mode", "isolated")
    
    def _convert_interval(self, interval: str) -> str:
        """Convert interval format to OKX format"""
        interval_map = {
            "1m": "1m",
            "3m": "3m",
            "5m": "5m",
            "15m": "15m",
            "30m": "30m",
            "1h": "1H",
            "2h": "2H",
            "4h": "4H",
            "6h": "6H",
            "12h": "12H",
            "1d": "1D",
            "1w": "1W",
            "1M": "1M"
        }
        return interval_map.get(interval, "30m")
    
    def _get_interval_ms(self, interval: str) -> int:
        """Get interval duration in milliseconds"""
        interval_ms = {
            "1m": 60000,
            "3m": 180000,
            "5m": 300000,
            "15m": 900000,
            "30m": 1800000,
            "1h": 3600000,
            "2h": 7200000,
            "4h": 14400000,
            "6h": 21600000,
            "12h": 43200000,
            "1d": 86400000,
            "1w": 604800000,
            "1M": 2592000000
        }
        return interval_ms.get(interval, 1800000)
    
    def _convert_order_status(self, status: str) -> str:
        """Convert OKX order status to unified status"""
        status_map = {
            "live": "new",
            "partially_filled": "partially_filled",
            "filled": "filled",
            "canceled": "canceled",
            "mmp_canceled": "canceled"
        }
        return status_map.get(status, status)
