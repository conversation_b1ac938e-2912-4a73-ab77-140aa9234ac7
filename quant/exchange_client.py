"""
Exchange Client Abstraction Layer
Dynamically selects the appropriate exchange client based on configuration
"""

from typing import List, Optional, Any, Dict
from quant.config_manager import config
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class ExchangeClient:
    """Universal exchange client that dynamically selects the appropriate exchange"""
    
    def __init__(self):
        """Initialize the exchange client based on configuration"""
        # Get exchange configuration
        exchanges_config = config.get("EXCHANGES", {})
        auto_trader_config = config.get("AUTO_TRADER", {})
        
        # Determine which exchange to use (priority order):
        # 1. AUTO_TRADER.exchange
        # 2. EXCHANGES.default
        # 3. Fallback to binance
        self.exchange_name = (
            auto_trader_config.get("exchange") or
            exchanges_config.get("default") or
            "binance"
        ).lower()
        
        logger.info(f"Initializing exchange client for: {self.exchange_name}")
        
        # Initialize the appropriate client
        if self.exchange_name == "okx":
            from quant.okx_client import OKX<PERSON>lient
            self._client = OKXClient()
            logger.info("Using OKX exchange client")
        else:
            from quant.binance_client import BinanceClient
            self._client = BinanceClient()
            logger.info("Using Binance exchange client")
    
    async def get_klines(self, symbol: str = None, interval: str = "30m", limit: int = 100) -> List[List]:
        """Get K-line data from the configured exchange
        
        Args:
            symbol: Trading symbol (optional, uses default from config)
            interval: K-line interval (e.g., '30m', '1h', '1d')
            limit: Number of K-lines to retrieve
            
        Returns:
            List of K-line data in standard format:
            [[timestamp, open, high, low, close, volume, ...], ...]
        """
        try:
            return await self._client.get_klines(symbol=symbol, interval=interval, limit=limit)
        except Exception as e:
            logger.error(f"Error getting klines from {self.exchange_name}: {e}")
            raise
    
    async def get_account_balance(self) -> Dict[str, Any]:
        """Get account balance from the configured exchange
        
        Returns:
            Account balance information
        """
        try:
            return await self._client.get_account_balance()
        except Exception as e:
            logger.error(f"Error getting balance from {self.exchange_name}: {e}")
            return {"balances": []}
    
    async def get_symbol_ticker(self, symbol: str = None) -> Dict[str, Any]:
        """Get current ticker price from the configured exchange
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Ticker information
        """
        try:
            return await self._client.get_symbol_ticker(symbol=symbol)
        except Exception as e:
            logger.error(f"Error getting ticker from {self.exchange_name}: {e}")
            return {}
    
    def get_exchange_name(self) -> str:
        """Get the name of the currently configured exchange
        
        Returns:
            Exchange name (e.g., 'binance', 'okx')
        """
        return self.exchange_name
    
    async def create_order(self, **kwargs) -> Dict[str, Any]:
        """Create order on the configured exchange
        
        Note: Prefer using the exchange abstraction layer for orders
        """
        return await self._client.create_order(**kwargs)
    
    async def cancel_order(self, **kwargs) -> bool:
        """Cancel order on the configured exchange
        
        Note: Prefer using the exchange abstraction layer for orders
        """
        return await self._client.cancel_order(**kwargs)


# Create global instance for easy import
exchange_client = ExchangeClient()


def get_exchange_client() -> ExchangeClient:
    """Get the global exchange client instance
    
    Returns:
        The global ExchangeClient instance
    """
    return exchange_client
