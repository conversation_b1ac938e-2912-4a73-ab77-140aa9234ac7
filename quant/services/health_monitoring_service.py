"""
Health Monitoring Service

Handles system health monitoring, alerts, and graceful degradation.
Extracted from TradingSystem health and process monitoring methods.
"""

import time
from dataclasses import asdict
from datetime import datetime
from typing import Any, Dict, List, Optional

from quant.database_manager import db
from quant.notification_manager import notification_manager
from quant.real_time_data_manager import real_time_data_manager
from quant.recovery_manager import recovery_manager, RecoveryPriority
from quant.system_monitor import system_monitor
from quant.trading_alert_manager import trading_alert_manager
from .base_service import BaseService


class HealthMonitoringService(BaseService):
    """
    Service responsible for system health monitoring and alerting.
    
    Core responsibilities:
    - Monitor trading-critical system metrics
    - Check system alerts and send notifications
    - Monitor process health and trigger recovery
    - Handle graceful degradation under system stress
    - Clean up old alert suppressions
    """
    
    def __init__(self):
        super().__init__("health_monitoring")
        self._last_health_check: Optional[datetime] = None
        self._alert_counts = {
            "critical": 0,
            "warning": 0,
            "system": 0
        }
        self._degradation_active = False
    
    async def initialize(self) -> None:
        """Initialize health monitoring service."""
        self.logger.info("HealthMonitoringService initialized")
    
    async def cleanup(self) -> None:
        """Cleanup health monitoring resources."""
        self.logger.info("HealthMonitoringService cleanup completed")
    
    async def run_health_monitoring(self) -> Dict[str, Any]:
        """
        Run comprehensive trading-critical health monitoring.
        
        Returns:
            Health monitoring results
        """
        return await self.execute_safe(
            "health_monitoring",
            self._execute_health_monitoring
        ) or {}
    
    async def monitor_processes(self) -> Dict[str, Any]:
        """
        Monitor system processes and trigger recovery if needed.
        
        Returns:
            Process monitoring results
        """
        return await self.execute_safe(
            "process_monitoring",
            self._execute_process_monitoring
        ) or {}
    
    async def _execute_health_monitoring(self) -> Dict[str, Any]:
        """Execute the health monitoring workflow."""
        self.logger.debug("Starting trading-critical health monitoring...")
        
        # Collect trading-specific metrics
        trading_metrics = await self._collect_trading_metrics()
        
        # Check trading alerts (primary focus)
        trading_alerts = trading_alert_manager.check_trading_alerts(trading_metrics)
        
        # Check system alerts (secondary focus, higher thresholds)
        system_alerts = trading_alert_manager.check_system_alerts(trading_metrics)
        
        # Process all alerts
        all_alerts = trading_alerts + system_alerts
        health_results = await self._process_alerts(all_alerts)
        
        # Clean up old suppression rules
        trading_alert_manager.cleanup_old_suppressions()
        
        self._last_health_check = datetime.utcnow()
        self.logger.debug("Health monitoring task completed")
        
        return {
            "trading_metrics": trading_metrics,
            "health_results": health_results,
            "timestamp": self._last_health_check.isoformat()
        }
    
    async def _execute_process_monitoring(self) -> Dict[str, Any]:
        """Execute process monitoring workflow."""
        self.logger.debug("Starting process monitoring task...")
        
        # Get recovery status
        recovery_status = recovery_manager.get_recovery_status()
        
        # Log key metrics
        self._log_process_metrics(recovery_status)
        
        # Check for stuck operations
        await self._check_stuck_operations(recovery_status)
        
        # Monitor WebSocket connections
        await self._monitor_websocket_health()
        
        # Monitor database health
        await self._monitor_database_health()
        
        # Check for graceful degradation needs
        await self._check_system_stress()
        
        self.logger.debug("Process monitoring task completed")
        
        return {
            "recovery_status": recovery_status,
            "degradation_active": self._degradation_active,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def _collect_trading_metrics(self) -> Dict[str, Any]:
        """Collect trading-specific metrics for health monitoring."""
        try:
            return {
                "last_data_update": time.time(),
                "websocket_status": real_time_data_manager.get_stream_status(),
                "settlement_status": {"pending_settlements": len(db.get_pending_trades())},
                "system_metrics": asdict(system_monitor.collect_system_metrics())
            }
        except Exception as e:
            self.logger.error(f"Error collecting trading metrics: {e}")
            return {}
    
    async def _process_alerts(self, all_alerts: List[Any]) -> Dict[str, Any]:
        """Process system alerts and send notifications."""
        # Filter out None alerts
        valid_alerts = [a for a in all_alerts if a is not None]
        
        if not valid_alerts:
            self.logger.debug("No trading-critical alerts detected")
            return {"total_alerts": 0}
        
        # Categorize alerts
        critical_count = len([a for a in valid_alerts if a.severity == "trading_critical"])
        warning_count = len([a for a in valid_alerts if a.severity == "trading_warning"])
        system_count = len([a for a in valid_alerts if a.severity == "system_info"])
        
        # Update internal counters
        self._alert_counts["critical"] += critical_count
        self._alert_counts["warning"] += warning_count
        self._alert_counts["system"] += system_count
        
        self.logger.info(
            f"Trading Health Status - Critical: {critical_count}, "
            f"Warning: {warning_count}, System: {system_count}"
        )
        
        # Send notifications for critical trading alerts only
        await self._send_critical_alerts(valid_alerts)
        
        return {
            "total_alerts": len(valid_alerts),
            "critical_alerts": critical_count,
            "warning_alerts": warning_count,
            "system_alerts": system_count
        }
    
    async def _send_critical_alerts(self, alerts: List[Any]) -> None:
        """Send notifications for critical alerts."""
        try:
            for alert in alerts:
                if alert.severity == "trading_critical":
                    notification_manager.send_system_health_alert({
                        "alert_type": "TRADING_CRITICAL",
                        "health_report": {"trading_alert": asdict(alert)},
                        "message": alert.message
                    })
        except Exception as e:
            self.logger.error(f"Error sending critical alerts: {e}")
    
    def _log_process_metrics(self, recovery_status: Dict[str, Any]) -> None:
        """Log key process metrics."""
        self.logger.info(
            f"Process Monitor - "
            f"Active operations: {recovery_status['active_operations']}, "
            f"Pending operations: {recovery_status['pending_operations']}, "
            f"Total history: {recovery_status['total_history']}"
        )
    
    async def _check_stuck_operations(self, recovery_status: Dict[str, Any]) -> None:
        """Check for stuck recovery operations."""
        try:
            for operation in recovery_status['active_operation_details']:
                if operation['retry_count'] >= 2:
                    self.logger.warning(
                        f"Operation {operation['operation_id']} has high retry count: "
                        f"{operation['retry_count']} attempts"
                    )
        except Exception as e:
            self.logger.error(f"Error checking stuck operations: {e}")
    
    async def _monitor_websocket_health(self) -> None:
        """Monitor WebSocket connection health."""
        try:
            ws_status = real_time_data_manager.get_stream_status()
            if ws_status['failed_connections'] > 0:
                self.logger.warning(
                    f"WebSocket connection issues detected: {ws_status['failed_connections']} failures"
                )
        except Exception as e:
            self.logger.error(f"Error monitoring WebSocket health: {e}")
    
    async def _monitor_database_health(self) -> None:
        """Monitor database health."""
        try:
            db_status = db.get_database_status()
            if db_status['status'] != 'healthy':
                self.logger.warning(
                    f"Database health issues detected: {db_status.get('error', 'Unknown error')}"
                )
        except Exception as e:
            self.logger.error(f"Error monitoring database health: {e}")
    
    async def _check_system_stress(self) -> None:
        """Check for system stress and trigger graceful degradation if needed."""
        try:
            system_metrics = system_monitor.collect_system_metrics()
            
            if system_metrics.cpu_percent > 90 or system_metrics.memory_percent > 90:
                self.logger.warning("System under high stress - considering graceful degradation")
                await self._handle_graceful_degradation()
        except Exception as e:
            self.logger.error(f"Error checking system stress: {e}")
    
    async def _handle_graceful_degradation(self) -> None:
        """Handle graceful degradation during system stress."""
        if self._degradation_active:
            return  # Already in degradation mode
        
        try:
            # Trigger system recovery operation
            await recovery_manager.trigger_recovery(
                component="system",
                operation_type="graceful_degradation",
                priority=RecoveryPriority.HIGH,
                data={
                    "reason": "system_stress",
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            self._degradation_active = True
            self.logger.info("Graceful degradation activated - reducing system load")
            
        except Exception as e:
            self.logger.error(f"Error handling graceful degradation: {e}")
    
    def get_health_metrics(self) -> Dict[str, Any]:
        """Get health monitoring metrics."""
        return {
            "last_health_check": self._last_health_check.isoformat() if self._last_health_check else None,
            "alert_counts": self._alert_counts.copy(),
            "degradation_active": self._degradation_active
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health monitoring service health status."""
        health = super().get_health_status()
        health.update(self.get_health_metrics())
        
        # Add health warnings
        if self._alert_counts["critical"] > 10:
            health["health_warning"] = f"High number of critical alerts: {self._alert_counts['critical']}"
        
        if self._degradation_active:
            health["health_warning"] = "System currently in graceful degradation mode"
        
        return health