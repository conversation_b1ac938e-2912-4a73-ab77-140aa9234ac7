"""
Settlement Service

Handles trade settlement, reconciliation, and backlog monitoring.
Extracted from TradingSystem settlement-related methods.
"""

from datetime import datetime, date
from typing import Any, Dict, List, Optional

from quant.database_manager import db
from quant.notification_manager import notification_manager
from quant.risk_manager import risk_manager
from quant.settlement_checker import settlement_checker
from .base_service import BaseService


class SettlementService(BaseService):
    """
    Service responsible for trade settlement and reconciliation.
    
    Core responsibilities:
    - Process pending settlements with optimized concurrency
    - Monitor settlement backlog and alert on issues
    - Run reconciliation tasks
    - Generate daily settlement reports
    - Update risk manager with settlement results
    """
    
    def __init__(self, max_concurrent_settlements: int = 20):
        super().__init__("settlement")
        self.max_concurrent_settlements = max_concurrent_settlements
        self._last_settlement_time: Optional[datetime] = None
        self._settlement_stats = {
            "total_processed": 0,
            "successful_settlements": 0,
            "failed_settlements": 0,
            "last_batch_size": 0
        }
    
    async def initialize(self) -> None:
        """Initialize settlement service."""
        self.logger.info("SettlementService initialized")
    
    async def cleanup(self) -> None:
        """Cleanup settlement resources."""
        self.logger.info("SettlementService cleanup completed")
    
    async def process_pending_settlements(self) -> List[Dict[str, Any]]:
        """
        Check and settle pending trades with optimized concurrency.
        
        Returns:
            List of settled trades
        """
        return await self.execute_safe(
            "process_pending_settlements",
            self._execute_settlement_process
        ) or []
    
    async def check_settlement_backlog(self) -> Dict[str, Any]:
        """
        Check for settlement backlog and return status information.
        
        Returns:
            Backlog information dictionary
        """
        return await self.execute_safe(
            "check_settlement_backlog", 
            settlement_checker.check_settlement_backlog
        ) or {}
    
    async def run_reconciliation(self) -> None:
        """Run reconciliation task."""
        await self.execute_safe(
            "reconciliation",
            settlement_checker.reconciliation_task
        )
    
    async def generate_daily_report(self) -> Optional[Dict[str, Any]]:
        """Generate and send daily settlement report."""
        return await self.execute_safe(
            "daily_report",
            self._generate_daily_report
        )
    
    async def _execute_settlement_process(self) -> List[Dict[str, Any]]:
        """Execute the settlement processing workflow."""
        self.logger.info("Starting optimized settlement check task...")
        
        # Use concurrent settlement processing for efficiency
        settled_trades = await settlement_checker.check_pending_settlements_concurrent(
            max_concurrent=self.max_concurrent_settlements
        )
        
        if settled_trades:
            self.logger.info(f"Settled {len(settled_trades)} trades concurrently")
            
            # Update statistics
            self._update_settlement_stats(settled_trades)
            
            # Update risk manager with trade results
            await self._update_risk_manager(settled_trades)
            
            # Log settlement processing details
            await self._log_settlement_results(settled_trades)
            
        else:
            self.logger.debug("No trades to settle")
        
        self._last_settlement_time = datetime.utcnow()
        self.logger.info("Optimized settlement check task completed")
        return settled_trades
    
    def _update_settlement_stats(self, settled_trades: List[Dict[str, Any]]) -> None:
        """Update internal settlement statistics."""
        batch_size = len(settled_trades)
        successful = len([t for t in settled_trades if t.get("settlement_status") == "settled"])
        failed = batch_size - successful
        
        self._settlement_stats.update({
            "total_processed": self._settlement_stats["total_processed"] + batch_size,
            "successful_settlements": self._settlement_stats["successful_settlements"] + successful,
            "failed_settlements": self._settlement_stats["failed_settlements"] + failed,
            "last_batch_size": batch_size
        })
    
    async def _update_risk_manager(self, settled_trades: List[Dict[str, Any]]) -> None:
        """Update risk manager with settlement results."""
        try:
            for trade in settled_trades:
                if trade.get("pnl") is not None:
                    risk_manager.update_trade_result(
                        trade["pnl"],
                        datetime.fromisoformat(trade["exit_timestamp"]).date(),
                    )
        except Exception as e:
            self.logger.error(f"Error updating risk manager: {e}")
    
    async def _log_settlement_results(self, settled_trades: List[Dict[str, Any]]) -> None:
        """Log detailed settlement results."""
        try:
            for trade in settled_trades:
                signal_id = trade.get("original_signal_id", "Unknown")
                self.logger.info(f"Processed settlement for signal {signal_id}")
        except Exception as e:
            self.logger.error(f"Error logging settlement results: {e}")
    
    async def _generate_daily_report(self) -> Optional[Dict[str, Any]]:
        """Generate daily settlement report."""
        self.logger.info("Starting daily report task...")
        
        report = await settlement_checker.run_daily_settlement_report()
        
        if report:
            # Format report message
            message = f"""
### 📊 每日交易报告

**日期**: {report['date']}
**总交易数**: {report['total_trades']}
**盈利交易**: {report['winning_trades']}
**亏损交易**: {report['losing_trades']}
**胜率**: {report['win_rate']}%

报告生成时间: {report['generated_at']}
"""
            self.logger.info(f"Daily report: {message}")
            
            # Send notification (in production this would go to DingTalk)
            # notification_manager.send_daily_report(report)
            
        self.logger.info("Daily report task completed")
        return report
    
    def get_settlement_metrics(self) -> Dict[str, Any]:
        """Get settlement processing metrics."""
        return {
            "last_settlement_time": self._last_settlement_time.isoformat() if self._last_settlement_time else None,
            "settlement_stats": self._settlement_stats.copy(),
            "max_concurrent_settlements": self.max_concurrent_settlements
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get settlement service health status."""
        health = super().get_health_status()
        health.update(self.get_settlement_metrics())
        
        # Add health indicators
        if self._settlement_stats["failed_settlements"] > 0:
            failure_rate = (
                self._settlement_stats["failed_settlements"] / 
                max(self._settlement_stats["total_processed"], 1)
            )
            health["failure_rate"] = failure_rate
            
            if failure_rate > 0.1:  # 10% failure rate threshold
                health["health_warning"] = f"High settlement failure rate: {failure_rate:.2%}"
        
        return health