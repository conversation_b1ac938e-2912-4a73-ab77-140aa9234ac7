"""
System Metrics Service

Handles system metrics monitoring, performance reporting, and data stream management.
Extracted from TradingSystem metrics, data stream, and performance methods.
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from quant.advanced_analytics import advanced_analytics
from quant.notification_manager import notification_manager
from quant.real_time_data_manager import real_time_data_manager
from .base_service import BaseService


class SystemMetricsService(BaseService):
    """
    Service responsible for system metrics and performance monitoring.
    
    Core responsibilities:
    - Monitor system performance metrics
    - Manage real-time data streams
    - Generate comprehensive performance reports
    - Monitor data stream health
    - Generate extension statistics reports
    """
    
    def __init__(self):
        super().__init__("system_metrics")
        self._last_metrics_check: Optional[datetime] = None
        self._stream_management_count = 0
        self._performance_reports_generated = 0
    
    async def initialize(self) -> None:
        """Initialize system metrics service."""
        self.logger.info("SystemMetricsService initialized")
    
    async def cleanup(self) -> None:
        """Cleanup system metrics resources."""
        self.logger.info("SystemMetricsService cleanup completed")
    
    async def collect_system_metrics(self) -> Dict[str, Any]:
        """
        Monitor system metrics and performance.
        
        Returns:
            System metrics dictionary
        """
        return await self.execute_safe(
            "collect_system_metrics",
            self._execute_metrics_collection
        ) or {}
    
    async def manage_data_streams(self, symbol: str = "BTCUSDT") -> Dict[str, Any]:
        """
        Manage real-time data streams.
        
        Args:
            symbol: Trading symbol to manage streams for
            
        Returns:
            Data stream status
        """
        return await self.execute_safe(
            "manage_data_streams",
            self._execute_data_stream_management,
            symbol
        ) or {}
    
    async def generate_performance_report(self, days: int = 30) -> Optional[Dict[str, Any]]:
        """
        Generate comprehensive performance report.
        
        Args:
            days: Number of days to include in report
            
        Returns:
            Performance report dictionary
        """
        return await self.execute_safe(
            "generate_performance_report",
            self._execute_performance_report,
            days
        )
    
    async def generate_extension_stats_report(self) -> Optional[Dict[str, Any]]:
        """
        Generate weekly extension decision stats report.
        
        Returns:
            Extension stats report dictionary
        """
        return await self.execute_safe(
            "generate_extension_stats",
            self._execute_extension_stats_report
        )
    
    async def _execute_metrics_collection(self) -> Dict[str, Any]:
        """Execute system metrics collection workflow."""
        self.logger.debug("Starting system metrics collection...")
        
        # Get real-time data manager metrics
        metrics = real_time_data_manager.get_metrics()
        
        # Log key metrics
        self._log_system_metrics(metrics)
        
        # Check for potential issues
        await self._check_metrics_issues(metrics)
        
        self._last_metrics_check = datetime.utcnow()
        self.logger.debug("System metrics collection completed")
        
        return metrics
    
    async def _execute_data_stream_management(self, symbol: str) -> Dict[str, Any]:
        """Execute data stream management workflow."""
        self.logger.debug("Starting data stream management task...")
        
        # Start K-line streams for multiple timeframes
        intervals = ["1m", "5m", "15m", "30m"]
        stream_results = {}
        
        for interval in intervals:
            try:
                await real_time_data_manager.start_kline_stream(symbol, interval)
                stream_results[interval] = "started"
            except Exception as e:
                self.logger.error(f"Error starting {interval} stream: {e}")
                stream_results[interval] = f"error: {str(e)}"
        
        # Get and log stream status
        status = real_time_data_manager.get_stream_status()
        self.logger.debug(f"Data stream status: {status}")
        
        self._stream_management_count += 1
        self.logger.debug("Data stream management task completed")
        
        return {
            "symbol": symbol,
            "intervals": stream_results,
            "status": status,
            "management_count": self._stream_management_count
        }
    
    async def _execute_performance_report(self, days: int) -> Optional[Dict[str, Any]]:
        """Execute performance report generation."""
        self.logger.info("Starting comprehensive performance report task...")
        
        # Generate comprehensive report
        report = advanced_analytics.get_comprehensive_performance_report(days=days)
        
        if "error" not in report:
            # Log key metrics
            await self._log_performance_metrics(report)
            
            # Send performance report notification
            await self._send_performance_notification(report)
            
            self._performance_reports_generated += 1
            self.logger.info("Comprehensive performance report completed")
            
        else:
            self.logger.error(f"Error generating performance report: {report['error']}")
        
        return report
    
    async def _execute_extension_stats_report(self) -> Optional[Dict[str, Any]]:
        """Execute extension statistics report generation."""
        try:
            script = Path("tests/analyze_extension_stats.py")
            if not script.exists():
                self.logger.warning("Extension stats script not found, skipping.")
                return None
            
            # Run the analyzer as a subprocess to avoid DB contention
            proc = await asyncio.create_subprocess_exec(
                sys.executable, str(script),
                stdout=asyncio.subprocess.PIPE, 
                stderr=asyncio.subprocess.PIPE,
            )
            
            stdout, stderr = await proc.communicate()
            
            if stderr:
                self.logger.warning(f"Extension stats stderr: {stderr.decode().strip()}")
            
            result_text = stdout.decode().strip() or "{}"
            
            try:
                data = json.loads(result_text)
            except Exception:
                data = {"raw": result_text}
            
            # Send formatted report
            await self._send_extension_stats_notification(data)
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error in extension stats report: {e}")
            return None
    
    def _log_system_metrics(self, metrics: Dict[str, Any]) -> None:
        """Log key system metrics."""
        self.logger.info(
            f"System Metrics - "
            f"Messages: {metrics['messages_processed']} processed, "
            f"{metrics['messages_failed']} failed, "
            f"Storage ops: {metrics['storage_operations']}, "
            f"Active streams: {metrics['active_streams']}, "
            f"Buffer size: {metrics['buffer_size']}, "
            f"Uptime: {metrics['uptime_seconds']:.1f}s"
        )
    
    async def _check_metrics_issues(self, metrics: Dict[str, Any]) -> None:
        """Check for potential metrics issues."""
        try:
            # Check message failure rate
            if metrics["messages_failed"] > 0:
                failure_rate = metrics["messages_failed"] / max(
                    metrics["messages_processed"], 1
                )
                if failure_rate > 0.05:  # 5% failure rate threshold
                    self.logger.warning(f"High message failure rate: {failure_rate:.2%}")
            
            # Check buffer size
            if metrics["buffer_size"] > 500:
                self.logger.warning(f"Large buffer size: {metrics['buffer_size']} items")
                
        except Exception as e:
            self.logger.error(f"Error checking metrics issues: {e}")
    
    async def _log_performance_metrics(self, report: Dict[str, Any]) -> None:
        """Log key performance metrics from report."""
        try:
            performance_summary = report.get("performance_summary", {})
            risk_metrics = report.get("risk_metrics", {})
            
            self.logger.info(
                f"Performance Report - "
                f"Trades: {performance_summary.get('total_trades', 0)}, "
                f"Win Rate: {performance_summary.get('win_rate', 0)}%, "
                f"Total P&L: ${performance_summary.get('total_pnl', 0):.2f}, "
                f"Sharpe Ratio: {risk_metrics.get('sharpe_ratio', 0):.3f}, "
                f"Max Drawdown: {risk_metrics.get('max_drawdown', 0):.2%}"
            )
            
            # Log trading recommendations
            recommendations = report.get("trading_recommendations", [])
            if recommendations:
                self.logger.info("Trading Recommendations:")
                for rec in recommendations:
                    self.logger.info(f"  - {rec}")
            
            # Log cache performance
            cache_stats = report.get("cache_stats", {}).get("cache_performance", {})
            if cache_stats:
                self.logger.info(
                    f"Cache Performance - "
                    f"Hit Rate: {cache_stats.get('hit_rate', 0):.1%}, "
                    f"Entries: {cache_stats.get('cache_entries', 0)}, "
                    f"Memory: {cache_stats.get('memory_usage_mb', 0):.1f}MB"
                )
                
        except Exception as e:
            self.logger.error(f"Error logging performance metrics: {e}")
    
    async def _send_performance_notification(self, report: Dict[str, Any]) -> None:
        """Send performance report notification."""
        try:
            performance_summary = report.get("performance_summary", {})
            risk_metrics = report.get("risk_metrics", {})
            recommendations = report.get("trading_recommendations", [])
            
            notification_manager.send_performance_alert({
                "alert_type": "PERFORMANCE_REPORT",
                "report_summary": {
                    "total_trades": performance_summary.get('total_trades', 0),
                    "win_rate": performance_summary.get('win_rate', 0),
                    "total_pnl": performance_summary.get('total_pnl', 0),
                    "sharpe_ratio": risk_metrics.get('sharpe_ratio', 0),
                    "max_drawdown": risk_metrics.get('max_drawdown', 0)
                },
                "recommendations": recommendations,
                "report_timestamp": report.get("report_timestamp")
            })
            
        except Exception as e:
            self.logger.error(f"Error sending performance notification: {e}")
    
    async def _send_extension_stats_notification(self, data: Dict[str, Any]) -> None:
        """Send extension statistics notification."""
        try:
            msg_lines = [
                "### 📈 二次延时统计周报",
                f"- 样本数: {data.get('extension_trades', 0)}",
                f"- 亏损笔数: {data.get('loss_trades', 0)}",
                f"- 亏损比例: {data.get('loss_ratio', 0)*100:.2f}%",
            ]
            
            pnl_stats = data.get("pnl_pct_stats") or {}
            if pnl_stats:
                msg_lines.append("- pnl/投注额 分布(%)：")
                msg_lines.append(f"  - 最差: {pnl_stats.get('min', 0)}")
                msg_lines.append(f"  - 中位: {pnl_stats.get('median', 0)}")
                msg_lines.append(f"  - 平均: {pnl_stats.get('mean', 0)}")
            
            worst = data.get("worst_case_loss_pct")
            if worst is not None:
                msg_lines.append(f"- 最坏亏损(%)：{worst}")
            
            notification_manager.send_system_health_alert({
                "alert_type": "WEEKLY_EXTENSION_STATS",
                "message": "\\n".join(msg_lines),
                "health_report": {"extension_stats": data}
            })
            
        except Exception as e:
            self.logger.error(f"Error sending extension stats notification: {e}")
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system metrics service metrics."""
        return {
            "last_metrics_check": self._last_metrics_check.isoformat() if self._last_metrics_check else None,
            "stream_management_count": self._stream_management_count,
            "performance_reports_generated": self._performance_reports_generated
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get system metrics service health status."""
        health = super().get_health_status()
        health.update(self.get_system_metrics())
        return health