"""
Base Service Class

Abstract base class for all business services providing common functionality
like lifecycle management, error handling, and logging.
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional
from dataclasses import dataclass

from quant.utils.logger import get_logger


@dataclass
class ServiceStatus:
    """Service status information."""
    name: str
    running: bool
    last_execution: Optional[datetime] = None
    error_count: int = 0
    last_error: Optional[str] = None


class BaseService(ABC):
    """
    Abstract base class for all business services.
    
    Provides common functionality:
    - Lifecycle management (start/stop)
    - Error handling and recovery
    - Status reporting
    - Structured logging
    """
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = get_logger(f"service.{service_name}")
        self._running = False
        self._task: Optional[asyncio.Task] = None
        self._status = ServiceStatus(name=service_name, running=False)
    
    @property
    def is_running(self) -> bool:
        """Check if service is currently running."""
        return self._running and self._task and not self._task.done()
    
    @property 
    def status(self) -> ServiceStatus:
        """Get current service status."""
        self._status.running = self.is_running
        return self._status
    
    async def start(self) -> None:
        """Start the service."""
        if self.is_running:
            self.logger.warning(f"Service {self.service_name} is already running")
            return
        
        try:
            self.logger.info(f"Starting service: {self.service_name}")
            await self.initialize()
            self._running = True
            self.logger.info(f"Service {self.service_name} started successfully")
        except Exception as e:
            self.logger.error(f"Failed to start service {self.service_name}: {e}")
            self._status.error_count += 1
            self._status.last_error = str(e)
            raise
    
    async def stop(self) -> None:
        """Stop the service gracefully."""
        if not self.is_running:
            return
        
        self.logger.info(f"Stopping service: {self.service_name}")
        self._running = False
        
        if self._task and not self._task.done():
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        
        await self.cleanup()
        self.logger.info(f"Service {self.service_name} stopped")
    
    async def execute_safe(self, operation_name: str, operation_func, *args, **kwargs) -> Any:
        """
        Execute an operation with error handling and status tracking.
        
        Args:
            operation_name: Name of the operation for logging
            operation_func: Function to execute
            *args, **kwargs: Arguments for the operation function
        
        Returns:
            Result of the operation, or None if failed
        """
        try:
            self.logger.debug(f"Executing {operation_name}")
            result = await operation_func(*args, **kwargs)
            self._status.last_execution = datetime.utcnow()
            return result
        except Exception as e:
            self.logger.error(f"Error in {operation_name}: {e}")
            self._status.error_count += 1
            self._status.last_error = f"{operation_name}: {str(e)}"
            return None
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the service. Override in subclasses."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup resources. Override in subclasses."""
        pass
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get service health information."""
        return {
            "service_name": self.service_name,
            "running": self.is_running,
            "last_execution": self._status.last_execution.isoformat() if self._status.last_execution else None,
            "error_count": self._status.error_count,
            "last_error": self._status.last_error
        }