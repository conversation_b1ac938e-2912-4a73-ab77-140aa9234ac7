"""
Risk Management Service

Handles risk monitoring, alerts, and market data collection.
Extracted from TradingSystem risk monitoring and market data methods.
"""

import numpy as np
from datetime import datetime
from typing import Any, Dict, Optional

from quant.binance_client import binance_client
from quant.notification_manager import notification_manager
from quant.risk_manager import risk_manager
from .base_service import BaseService


class RiskManagementService(BaseService):
    """
    Service responsible for risk monitoring and market data collection.
    
    Core responsibilities:
    - Monitor risk metrics and send alerts
    - Collect and analyze market data
    - Track risk status changes
    - Send risk notifications when thresholds exceeded
    """
    
    def __init__(self):
        super().__init__("risk_management")
        self._last_risk_check: Optional[datetime] = None
        self._current_risk_status: str = "unknown"
        self._risk_alerts_sent = 0
    
    async def initialize(self) -> None:
        """Initialize risk management service."""
        self.logger.info("RiskManagementService initialized")
    
    async def cleanup(self) -> None:
        """Cleanup risk management resources."""
        self.logger.info("RiskManagementService cleanup completed")
    
    async def monitor_risk_metrics(self) -> Dict[str, Any]:
        """
        Monitor risk metrics and send alerts if necessary.
        
        Returns:
            Risk report dictionary
        """
        return await self.execute_safe(
            "monitor_risk_metrics",
            self._execute_risk_monitoring
        ) or {}
    
    async def get_market_data(self) -> Dict[str, Any]:
        """
        Get current market data for risk calculations.
        
        Returns:
            Market data dictionary with price, volatility, etc.
        """
        return await self.execute_safe(
            "get_market_data",
            self._collect_market_data
        ) or self._get_fallback_market_data()
    
    async def _execute_risk_monitoring(self) -> Dict[str, Any]:
        """Execute risk monitoring workflow."""
        self.logger.debug("Starting risk monitoring task...")
        
        # Get comprehensive risk report
        risk_report = risk_manager.get_risk_report()
        
        # Log key risk metrics
        await self._log_risk_metrics(risk_report)
        
        # Check for risk status changes and send alerts
        await self._handle_risk_alerts(risk_report)
        
        # Update internal tracking
        self._last_risk_check = datetime.utcnow()
        self._current_risk_status = risk_report["risk_status"]
        
        self.logger.debug("Risk monitoring task completed")
        return risk_report
    
    async def _log_risk_metrics(self, risk_report: Dict[str, Any]) -> None:
        """Log key risk metrics for monitoring."""
        try:
            account_state = risk_report.get("account_state", {})
            
            self.logger.info(
                f"Risk Status: {risk_report['risk_status']}, "
                f"Balance: ${account_state.get('balance_usdt', 0):.2f}, "
                f"Daily P&L: ${account_state.get('daily_pnl', 0):.2f}, "
                f"Consecutive Losses: {account_state.get('consecutive_losses', 0)}"
            )
        except Exception as e:
            self.logger.error(f"Error logging risk metrics: {e}")
    
    async def _handle_risk_alerts(self, risk_report: Dict[str, Any]) -> None:
        """Handle risk alerts based on current status."""
        risk_status = risk_report["risk_status"]
        
        try:
            if risk_status == "suspended":
                await self._send_suspension_alert(risk_report)
            elif risk_status == "warning":
                await self._send_warning_alert(risk_report)
            elif risk_status == "normal" and self._current_risk_status in ["suspended", "warning"]:
                await self._send_recovery_alert(risk_report)
        except Exception as e:
            self.logger.error(f"Error handling risk alerts: {e}")
    
    async def _send_suspension_alert(self, risk_report: Dict[str, Any]) -> None:
        """Send trading suspension alert."""
        suspension_reason = risk_report["account_state"].get("suspension_reason", "Unknown")
        
        self.logger.warning(f"Trading suspended: {suspension_reason}")
        
        notification_manager.send_risk_alert({
            "alert_type": "TRADING_SUSPENDED",
            "reason": suspension_reason,
            "risk_report": risk_report,
        })
        
        self._risk_alerts_sent += 1
    
    async def _send_warning_alert(self, risk_report: Dict[str, Any]) -> None:
        """Send risk warning alert."""
        self.logger.warning("Risk status is WARNING - monitoring closely")
        
        notification_manager.send_risk_alert({
            "alert_type": "RISK_WARNING",
            "reason": "Risk parameters approaching limits",
            "risk_report": risk_report,
        })
        
        self._risk_alerts_sent += 1
    
    async def _send_recovery_alert(self, risk_report: Dict[str, Any]) -> None:
        """Send risk recovery alert."""
        self.logger.info("Risk status recovered to NORMAL")
        
        # Optional: Send recovery notification
        # notification_manager.send_risk_alert({
        #     "alert_type": "RISK_RECOVERY",
        #     "reason": "Risk status returned to normal",
        #     "risk_report": risk_report,
        # })
    
    async def _collect_market_data(self) -> Dict[str, Any]:
        """Collect current market data for risk calculations."""
        self.logger.debug("Collecting market data...")
        
        # Get current price
        current_price = await binance_client.get_current_price()
        self.logger.info(f"Current price from Binance: ${current_price:,.2f}")
        
        # Calculate volatility using recent price data
        volatility = await self._calculate_volatility()
        
        market_data = {
            "current_price": current_price,
            "volatility": volatility,
            "timestamp": datetime.now().isoformat(),
            "market_cap": "N/A",  # Could be expanded
            "volume_24h": "N/A"   # Could be expanded
        }
        
        self.logger.info(f"Market data collected: {market_data}")
        return market_data
    
    async def _calculate_volatility(self) -> float:
        """Calculate volatility from recent price data."""
        try:
            # Get recent K-line data
            klines = await binance_client.get_klines(interval="1m", limit=20)
            
            if klines and len(klines) >= 10:
                # Extract close prices and calculate returns
                prices = [float(kline[4]) for kline in klines[-10:]]
                returns = [
                    (prices[i] - prices[i - 1]) / prices[i - 1]
                    for i in range(1, len(prices))
                ]
                
                # Calculate volatility (standard deviation of returns)
                if returns:
                    mean_return = sum(returns) / len(returns)
                    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
                    volatility = (variance ** 0.5) * (60 ** 0.5)  # Annualized to hourly
                    # Ensure minimum volatility for trading
                    return max(volatility, 0.05)  # Minimum 5% volatility
                
            return 0.1  # Default volatility
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility: {e}")
            return 0.1  # Default volatility
    
    def _get_fallback_market_data(self) -> Dict[str, Any]:
        """Get fallback market data when collection fails."""
        return {
            "current_price": 50000.0,
            "volatility": 0.2,
            "timestamp": datetime.now().isoformat(),
            "market_cap": "N/A",
            "volume_24h": "N/A"
        }
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """Get risk management metrics."""
        return {
            "last_risk_check": self._last_risk_check.isoformat() if self._last_risk_check else None,
            "current_risk_status": self._current_risk_status,
            "risk_alerts_sent": self._risk_alerts_sent
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get risk management service health status."""
        health = super().get_health_status()
        health.update(self.get_risk_metrics())
        
        # Add health warnings based on risk status
        if self._current_risk_status == "suspended":
            health["health_warning"] = "Trading currently suspended due to risk limits"
        elif self._current_risk_status == "warning":
            health["health_warning"] = "Risk status is WARNING - approaching limits"
        
        return health