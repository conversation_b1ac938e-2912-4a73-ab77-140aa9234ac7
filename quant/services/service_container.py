"""
Service Container

Dependency injection container for managing service lifecycle and dependencies.
Provides centralized service management with proper initialization order.
"""

import asyncio
from typing import Any, Dict, List, Optional, Type, TypeVar
from dataclasses import dataclass

from quant.config_manager import ConfigManager
from quant.utils.logger import get_logger
from .base_service import BaseService
from .market_analysis_service import MarketAnalysisService
from .settlement_service import SettlementService
from .risk_management_service import RiskManagementService
from .health_monitoring_service import HealthMonitoringService
from .system_metrics_service import SystemMetricsService

T = TypeVar('T', bound=BaseService)

logger = get_logger(__name__)


@dataclass
class ServiceConfig:
    """Configuration for a service."""
    service_class: Type[BaseService]
    enabled: bool = True
    init_args: Dict[str, Any] = None
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.init_args is None:
            self.init_args = {}
        if self.dependencies is None:
            self.dependencies = []


class ServiceContainer:
    """
    Dependency injection container for managing business services.
    
    Features:
    - Lazy service instantiation
    - Proper dependency resolution and initialization order
    - Service lifecycle management (start/stop)
    - Health monitoring across all services
    - Configuration-based service enabling/disabling
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.logger = get_logger(f"{__name__}.ServiceContainer")
        
        self._services: Dict[str, BaseService] = {}
        self._service_configs: Dict[str, ServiceConfig] = {}
        self._initialization_order: List[str] = []
        
        # Register default services
        self._register_default_services()
    
    def _register_default_services(self) -> None:
        """Register default services with their configurations."""
        
        # Get service configuration from config file
        service_config = self.config.get("SERVICES", {})
        
        # Market Analysis Service
        self._service_configs["market_analysis"] = ServiceConfig(
            service_class=MarketAnalysisService,
            enabled=service_config.get("market_analysis", {}).get("enabled", True),
            dependencies=[]  # Core service, no dependencies
        )
        
        # Settlement Service  
        settlement_config = service_config.get("settlement", {})
        self._service_configs["settlement"] = ServiceConfig(
            service_class=SettlementService,
            enabled=settlement_config.get("enabled", True),
            init_args={
                "max_concurrent_settlements": settlement_config.get("max_concurrent", 20)
            },
            dependencies=[]  # Independent service
        )
        
        # Risk Management Service
        self._service_configs["risk_management"] = ServiceConfig(
            service_class=RiskManagementService,
            enabled=service_config.get("risk_management", {}).get("enabled", True),
            dependencies=[]  # Independent service
        )
        
        # Health Monitoring Service
        self._service_configs["health_monitoring"] = ServiceConfig(
            service_class=HealthMonitoringService,
            enabled=service_config.get("health_monitoring", {}).get("enabled", True),
            dependencies=["risk_management"]  # May need risk data
        )
        
        # System Metrics Service
        self._service_configs["system_metrics"] = ServiceConfig(
            service_class=SystemMetricsService,
            enabled=service_config.get("system_metrics", {}).get("enabled", True),
            dependencies=[]  # Independent service
        )
        
        # Calculate initialization order based on dependencies
        self._calculate_initialization_order()
    
    def _calculate_initialization_order(self) -> None:
        """Calculate service initialization order based on dependencies."""
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(service_name: str):
            if service_name in temp_visited:
                raise ValueError(f"Circular dependency detected involving {service_name}")
            if service_name in visited:
                return
            
            temp_visited.add(service_name)
            
            # Visit dependencies first
            config = self._service_configs.get(service_name)
            if config:
                for dep in config.dependencies:
                    if dep in self._service_configs:
                        visit(dep)
            
            temp_visited.remove(service_name)
            visited.add(service_name)
            order.append(service_name)
        
        # Visit all enabled services
        for service_name, config in self._service_configs.items():
            if config.enabled:
                visit(service_name)
        
        self._initialization_order = order
        self.logger.info(f"Service initialization order: {self._initialization_order}")
    
    def register_service(self, name: str, service_config: ServiceConfig) -> None:
        """
        Register a custom service.
        
        Args:
            name: Service name
            service_config: Service configuration
        """
        self._service_configs[name] = service_config
        self._calculate_initialization_order()
        self.logger.info(f"Registered custom service: {name}")
    
    def get_service(self, service_name: str) -> Optional[BaseService]:
        """
        Get a service instance by name.
        
        Args:
            service_name: Name of the service
            
        Returns:
            Service instance or None if not found/enabled
        """
        if service_name not in self._service_configs:
            self.logger.warning(f"Service '{service_name}' not registered")
            return None
        
        config = self._service_configs[service_name]
        if not config.enabled:
            self.logger.debug(f"Service '{service_name}' is disabled")
            return None
        
        # Lazy instantiation
        if service_name not in self._services:
            self._instantiate_service(service_name)
        
        return self._services.get(service_name)
    
    def _instantiate_service(self, service_name: str) -> None:
        """Instantiate a service with its dependencies."""
        config = self._service_configs[service_name]
        
        try:
            # Instantiate the service
            service = config.service_class(**config.init_args)
            self._services[service_name] = service
            
            self.logger.info(f"Instantiated service: {service_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to instantiate service '{service_name}': {e}")
            raise
    
    async def start_all_services(self) -> None:
        """Start all enabled services in proper dependency order."""
        self.logger.info("Starting all services...")
        
        started_services = []
        
        try:
            for service_name in self._initialization_order:
                service = self.get_service(service_name)
                if service:
                    await service.start()
                    started_services.append(service_name)
                    self.logger.info(f"Started service: {service_name}")
            
            self.logger.info(f"Successfully started {len(started_services)} services")
            
        except Exception as e:
            self.logger.error(f"Error starting services: {e}")
            
            # Stop any services that were started
            await self._stop_services(started_services)
            raise
    
    async def stop_all_services(self) -> None:
        """Stop all services in reverse dependency order."""
        self.logger.info("Stopping all services...")
        
        # Stop in reverse order
        service_names = list(self._services.keys())
        await self._stop_services(reversed(service_names))
        
        self.logger.info("All services stopped")
    
    async def _stop_services(self, service_names: List[str]) -> None:
        """Stop specific services."""
        for service_name in service_names:
            service = self._services.get(service_name)
            if service:
                try:
                    await service.stop()
                    self.logger.info(f"Stopped service: {service_name}")
                except Exception as e:
                    self.logger.error(f"Error stopping service '{service_name}': {e}")
    
    def get_all_services(self) -> Dict[str, BaseService]:
        """Get all instantiated services."""
        return self._services.copy()
    
    def get_service_health_status(self) -> Dict[str, Any]:
        """Get health status of all services."""
        health_status = {}
        
        for service_name, service in self._services.items():
            try:
                health_status[service_name] = service.get_health_status()
            except Exception as e:
                health_status[service_name] = {
                    "service_name": service_name,
                    "running": False,
                    "error": f"Health check failed: {str(e)}"
                }
        
        return health_status
    
    async def restart_service(self, service_name: str) -> bool:
        """
        Restart a specific service.
        
        Args:
            service_name: Name of the service to restart
            
        Returns:
            True if restart successful, False otherwise
        """
        service = self._services.get(service_name)
        if not service:
            self.logger.warning(f"Service '{service_name}' not found for restart")
            return False
        
        try:
            self.logger.info(f"Restarting service: {service_name}")
            await service.stop()
            await service.start()
            self.logger.info(f"Successfully restarted service: {service_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to restart service '{service_name}': {e}")
            return False
    
    def get_container_status(self) -> Dict[str, Any]:
        """Get overall container status."""
        total_services = len(self._service_configs)
        enabled_services = len([c for c in self._service_configs.values() if c.enabled])
        running_services = len([s for s in self._services.values() if s.is_running])
        
        return {
            "total_services": total_services,
            "enabled_services": enabled_services,
            "instantiated_services": len(self._services),
            "running_services": running_services,
            "initialization_order": self._initialization_order,
            "service_configs": {
                name: {
                    "enabled": config.enabled,
                    "service_class": config.service_class.__name__,
                    "dependencies": config.dependencies
                }
                for name, config in self._service_configs.items()
            }
        }