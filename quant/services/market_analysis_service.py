"""
Market Analysis Service

Handles market analysis, signal generation, and trade execution.
Extracted from TradingSystem.market_analysis_and_trade_task().
"""

import time
from datetime import datetime
from dataclasses import asdict
from typing import Any, Dict, Optional

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.notification_manager import notification_manager
from quant.risk_manager import risk_manager
from quant.simple_analysis_engine import analysis_engine
from quant.strategies.auto_trader import auto_trader
from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.utils.logger import trade_logger
from .base_service import BaseService


class MarketAnalysisService(BaseService):
    """
    Service responsible for market analysis and trade execution.
    
    Core responsibilities:
    - Generate trading signals via analysis engine
    - Calculate position sizes via risk management
    - Execute trades via auto trader
    - Send notifications
    - Manage position lifecycle
    """
    
    def __init__(self):
        super().__init__("market_analysis")
        self._last_signal_time: Optional[datetime] = None
    
    async def initialize(self) -> None:
        """Initialize market analysis service."""
        self.logger.info("MarketAnalysisService initialized")
    
    async def cleanup(self) -> None:
        """Cleanup market analysis resources."""
        self.logger.info("MarketAnalysisService cleanup completed")
    
    async def analyze_market_and_trade(self) -> Optional[Dict[str, Any]]:
        """
        Execute comprehensive market analysis and trading workflow.
        
        Returns:
            Signal dictionary if generated, None otherwise
        """
        return await self.execute_safe(
            "market_analysis_and_trade",
            self._execute_analysis_workflow
        )
    
    async def _execute_analysis_workflow(self) -> Optional[Dict[str, Any]]:
        """Execute the complete market analysis and trading workflow."""
        start_time = time.time()
        self.logger.info("=" * 60)
        self.logger.info("Starting 30-minute market analysis and trading task...")
        
        # Step 1: Generate market signal
        signal = await analysis_engine.analyze_market()
        
        if not signal:
            self.logger.info("No signal generated in this analysis cycle")
            return None
        
        # Step 2: Calculate position size and risk assessment
        signal = await self._apply_risk_management(signal)
        
        # Step 3: Enrich signal with metadata
        signal = await self._enrich_signal_metadata(signal, start_time)
        
        # Step 4: Persist signal to database (if tradeable)
        if not signal.get("analysis_only"):
            await self._persist_signal(signal)
        else:
            self.logger.info("Analysis-only signal generated (not persisted to database)")
        
        # Step 5: Add current market data for notifications
        signal = await self._add_market_context(signal)
        
        # Step 6: Send notifications
        await self._send_signal_notification(signal, start_time)
        
        # Step 7: Execute trade if not suspended
        if not signal.get("trading_suspended"):
            await self._execute_auto_trade(signal)
        
        self._last_signal_time = datetime.utcnow()
        self.logger.info("Market analysis task completed")
        return signal
    
    async def _apply_risk_management(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Apply risk management calculations to the signal."""
        try:
            # Calculate position size
            position_sizing = risk_manager.calculate_position_size(signal)
            signal["suggested_bet"] = position_sizing.position_size_usdt
            signal["risk_level"] = position_sizing.risk_level.value
            signal["risk_notes"] = position_sizing.notes
            
            # Check if trading should be suspended
            if position_sizing.position_size_usdt == 0:
                signal["trading_suspended"] = True
                signal["suspension_reason"] = "Risk management blocked trade"
                self.logger.warning(f"Trading suspended: {'; '.join(position_sizing.notes)}")
            
            return signal
        except Exception as e:
            self.logger.error(f"Error in risk management: {e}")
            signal["trading_suspended"] = True
            signal["suspension_reason"] = f"Risk management error: {str(e)}"
            return signal
    
    async def _enrich_signal_metadata(self, signal: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """Add metadata and timing information to the signal."""
        signal["original_signal_id"] = f"{signal.get('signal_timestamp', '')}_{signal.get('direction', '')}"
        signal["analysis_latency_ms"] = int((time.time() - start_time) * 1000)
        return signal
    
    async def _persist_signal(self, signal: Dict[str, Any]) -> None:
        """Save tradeable signal to database."""
        try:
            trade_id = db.save_trade_signal(signal)
            signal["trade_id"] = trade_id
            
            # Log trade signal
            trade_logger.log_signal(signal)
            self.logger.info(f"Tradable signal saved to database with ID: {trade_id}")
        except Exception as e:
            self.logger.error(f"Error persisting signal: {e}")
    
    async def _add_market_context(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Add current market data for enhanced notifications."""
        try:
            current_price = await binance_client.get_current_price()
            if current_price:
                signal["current_price"] = current_price
                signal["price_change"] = current_price - signal["entry_price"]
                signal["price_change_pct"] = (signal["price_change"] / signal["entry_price"]) * 100
        except Exception as e:
            self.logger.error(f"Error adding market context: {e}")
        
        return signal
    
    async def _send_signal_notification(self, signal: Dict[str, Any], start_time: float) -> None:
        """Send enhanced signal notification."""
        try:
            self.logger.info(f"Signal data before notification: {signal}")
            
            notification_start = time.time()
            notification_manager.send_enhanced_signal_notification(signal)
            notification_latency = time.time() - notification_start
            
            if signal.get("trading_suspended"):
                self.logger.info(
                    f"Signal generated (ANALYSIS ONLY): {signal['direction']} - "
                    f"Trading suspended: {signal['suspension_reason']}"
                )
            else:
                self.logger.info(
                    f"Signal generated and sent: {signal['direction']} "
                    f"(Size: ${signal.get('suggested_bet', 'N/A')})"
                )
                # Log risk assessment
                if signal.get("risk_notes"):
                    self.logger.info(f"Risk assessment: {'; '.join(signal['risk_notes'])}")
            
        except Exception as e:
            self.logger.error(f"Error sending notification: {e}")
    
    async def _execute_auto_trade(self, signal: Dict[str, Any]) -> None:
        """Execute automatic trading for the signal."""
        try:
            exec_result = await auto_trader.handle_new_signal(signal)
            
            if exec_result.success:
                self.logger.info(f"Auto trade executed: trade_id={exec_result.trade_id}")
                
                # Add to simple exit manager for automatic close
                await self._register_for_exit_management(signal, exec_result.trade_id)
            else:
                self.logger.warning(f"Auto trade skipped: {exec_result.message}")
                
        except Exception as e:
            self.logger.error(f"Auto trading error: {e}")
    
    async def _register_for_exit_management(self, signal: Dict[str, Any], trade_id: int) -> None:
        """Register trade with exit management service."""
        try:
            # Import here to avoid circular dependency
            from quant.symbol_manager import symbol_manager
            
            trade_data = {
                "id": trade_id,
                "entry_price": signal["entry_price"],
                "direction": signal["direction"],
                "symbol": signal.get("symbol", symbol_manager.get_current_symbol()),
                "suggested_bet": signal.get("suggested_bet", 0),
                "signal_timestamp": signal.get("signal_timestamp")
            }
            
            simple_exit_manager.add_position(trade_id, trade_data)
            self.logger.info("Position added to simple exit manager for automatic close")
            
        except Exception as e:
            self.logger.error(f"Error registering for exit management: {e}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get market analysis service health status."""
        health = super().get_health_status()
        health.update({
            "last_signal_time": self._last_signal_time.isoformat() if self._last_signal_time else None,
            "analysis_engine_status": "active"  # Could be expanded with actual engine health
        })
        return health