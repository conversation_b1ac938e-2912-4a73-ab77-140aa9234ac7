"""
Signal Generation Modules

Modular signal generation strategies with clear separation of concerns.
Each strategy focuses on a specific approach to signal generation.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, List
import pandas as pd
import random

from quant.utils.logger import get_logger

logger = get_logger(__name__)


class SignalDirection(Enum):
    """Signal direction enumeration."""
    LONG = "LONG"
    SHORT = "SHORT"


class MarketState(Enum):
    """Market state enumeration."""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    VOLATILE = "volatile"


class SignalStrength(Enum):
    """Signal strength levels."""
    WEAK = 1
    MEDIUM = 2
    STRONG = 3


class SignalResult:
    """Standardized signal result structure."""
    
    def __init__(self, 
                 direction: SignalDirection,
                 confidence: float,
                 strength: SignalStrength,
                 entry_price: float,
                 market_state: str,
                 trigger_pattern: str,
                 indicators: List[str],
                 metadata: Dict[str, Any] = None):
        self.direction = direction
        self.confidence = confidence
        self.strength = strength
        self.entry_price = entry_price
        self.market_state = market_state
        self.trigger_pattern = trigger_pattern
        self.indicators = indicators
        self.metadata = metadata or {}
        self.timestamp = datetime.utcnow().isoformat() + "Z"
    
    def to_dict(self, symbol: str, suggested_bet: float) -> Dict[str, Any]:
        """Convert to standard signal dictionary format."""
        return {
            "signal_timestamp": self.timestamp,
            "symbol": symbol,
            "direction": self.direction.value,
            "entry_price": self.entry_price,
            "confidence_score": self.confidence,
            "market_state": self.market_state,
            "trigger_pattern": self.trigger_pattern,
            "confirmed_indicators": self.indicators,
            "suggested_bet": suggested_bet,
            "decision_details": self.metadata
        }


class BaseSignalGenerator(ABC):
    """Abstract base class for signal generators."""
    
    def __init__(self, confidence_threshold: float = 0.3):
        self.confidence_threshold = confidence_threshold
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def generate_signal(self, market_data: pd.DataFrame) -> Optional[SignalResult]:
        """Generate a trading signal from market data."""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """Return the name of this strategy."""
        pass
    
    def _get_current_price(self, market_data: pd.DataFrame) -> float:
        """Extract current price from market data."""
        return float(market_data['close'].iloc[-1])
    
    def _validate_market_data(self, market_data: pd.DataFrame) -> bool:
        """Validate that market data has required columns and sufficient data."""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        if market_data.empty:
            self.logger.warning("Market data is empty")
            return False
        
        missing_columns = [col for col in required_columns if col not in market_data.columns]
        if missing_columns:
            self.logger.warning(f"Missing required columns: {missing_columns}")
            return False
        
        if len(market_data) < 2:
            self.logger.warning("Insufficient market data for analysis")
            return False
        
        return True


class ConfidenceScoringSignalGenerator(BaseSignalGenerator):
    """Signal generator using intelligent confidence scoring."""
    
    def __init__(self, confidence_threshold: float = 0.3):
        super().__init__(confidence_threshold)
        # Lazy import to avoid circular dependency
        try:
            from quant.confidence_scorer import ConfidenceScorer
            self.confidence_scorer = ConfidenceScorer()
        except ImportError as e:
            self.logger.error(f"Could not import ConfidenceScorer: {e}")
            self.confidence_scorer = None
    
    def get_strategy_name(self) -> str:
        return "confidence_scoring"
    
    def generate_signal(self, market_data: pd.DataFrame) -> Optional[SignalResult]:
        """Generate signal using confidence scoring system."""
        if not self._validate_market_data(market_data):
            return None
        
        try:
            # Calculate confidence score
            confidence_obj = self.confidence_scorer.calculate_confidence(market_data)
            
            # Check confidence threshold
            if confidence_obj.overall_confidence < self.confidence_threshold:
                self.logger.debug(f"Confidence {confidence_obj.overall_confidence:.3f} below threshold {self.confidence_threshold}")
                return self._generate_fallback_signal(market_data, confidence_obj)
            
            # Determine signal direction
            direction = self._determine_signal_direction(confidence_obj)
            if direction is None:
                self.logger.debug("No clear signal direction determined")
                return self._generate_fallback_signal(market_data, confidence_obj)
            
            # Get signal strength
            strength = self.confidence_scorer.get_signal_strength(confidence_obj.overall_confidence)
            
            # Create signal result
            return SignalResult(
                direction=direction,
                confidence=confidence_obj.overall_confidence,
                strength=SignalStrength(strength.value),
                entry_price=self._get_current_price(market_data),
                market_state=confidence_obj.calculation_details.get('market_regime', 'unknown'),
                trigger_pattern="intelligent_confidence_scoring",
                indicators=list(confidence_obj.indicator_scores.keys()),
                metadata={
                    "confidence_breakdown": {
                        "trend_score": confidence_obj.trend_score,
                        "momentum_score": confidence_obj.momentum_score,
                        "volatility_score": confidence_obj.volatility_score,
                        "volume_score": confidence_obj.volume_score,
                        "market_regime_score": confidence_obj.market_regime_score
                    },
                    "individual_indicator_scores": confidence_obj.indicator_scores,
                    "calculation_details": confidence_obj.calculation_details
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error in confidence scoring signal generation: {e}")
            return None
    
    def _determine_signal_direction(self, confidence_obj) -> Optional[SignalDirection]:
        """Determine signal direction based on confidence scoring."""
        trend_score = confidence_obj.trend_score
        momentum_score = confidence_obj.momentum_score
        
        # Clear upward trend and momentum
        if trend_score > 0.6 and momentum_score > 0.6:
            return SignalDirection.LONG
        
        # Clear downward trend and momentum  
        if trend_score < 0.4 and momentum_score < 0.4:
            return SignalDirection.SHORT
        
        # Mixed signals - use stronger signal
        if abs(trend_score - 0.5) > abs(momentum_score - 0.5):
            return SignalDirection.LONG if trend_score > 0.5 else SignalDirection.SHORT
        else:
            return SignalDirection.LONG if momentum_score > 0.5 else SignalDirection.SHORT
    
    def _generate_fallback_signal(self, market_data: pd.DataFrame, confidence_obj) -> Optional[SignalResult]:
        """Generate fallback signal to ensure 30-minute frequency."""
        try:
            # Use basic price action for direction
            current_price = self._get_current_price(market_data)
            prev_price = float(market_data['close'].iloc[-2])
            
            direction = SignalDirection.LONG if current_price > prev_price else SignalDirection.SHORT
            
            # Use minimum acceptable confidence
            fallback_confidence = max(confidence_obj.overall_confidence, 0.25)
            
            return SignalResult(
                direction=direction,
                confidence=fallback_confidence,
                strength=SignalStrength.WEAK,
                entry_price=current_price,
                market_state="fallback_mode",
                trigger_pattern="confidence_fallback",
                indicators=["price_action"],
                metadata={
                    "fallback_reason": "confidence_below_threshold",
                    "original_confidence": confidence_obj.overall_confidence,
                    "price_change": current_price - prev_price
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error in fallback signal generation: {e}")
            return None


class SimpleSignalGenerator(BaseSignalGenerator):
    """Simple signal generator based on basic price action."""
    
    def get_strategy_name(self) -> str:
        return "simple_price_action"
    
    def generate_signal(self, market_data: pd.DataFrame) -> Optional[SignalResult]:
        """Generate signal using simple price action analysis."""
        if not self._validate_market_data(market_data):
            return None
        
        try:
            current_price = self._get_current_price(market_data)
            
            # Simple analysis based on recent price movement
            analysis = self._analyze_price_action(market_data)
            
            if analysis is None:
                return None
            
            return SignalResult(
                direction=analysis['direction'],
                confidence=analysis['confidence'],
                strength=analysis['strength'],
                entry_price=current_price,
                market_state=analysis['market_state'],
                trigger_pattern="simple_price_action",
                indicators=analysis['indicators'],
                metadata=analysis['metadata']
            )
            
        except Exception as e:
            self.logger.error(f"Error in simple signal generation: {e}")
            return None
    
    def _analyze_price_action(self, market_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Analyze price action to determine signal."""
        try:
            # Get recent prices
            closes = market_data['close'].tail(5).tolist()
            highs = market_data['high'].tail(5).tolist()
            lows = market_data['low'].tail(5).tolist()
            volumes = market_data['volume'].tail(5).tolist()
            
            if len(closes) < 2:
                return None
            
            current_price = closes[-1]
            prev_price = closes[-2]
            price_change = current_price - prev_price
            price_change_pct = price_change / prev_price
            
            # Determine direction based on price movement
            if price_change_pct > 0.001:  # > 0.1% increase
                direction = SignalDirection.LONG
                base_confidence = 0.4
            elif price_change_pct < -0.001:  # > 0.1% decrease  
                direction = SignalDirection.SHORT
                base_confidence = 0.4
            else:
                # Sideways movement - random direction for frequency maintenance
                direction = random.choice([SignalDirection.LONG, SignalDirection.SHORT])
                base_confidence = 0.3
            
            # Adjust confidence based on volume
            avg_volume = sum(volumes) / len(volumes)
            current_volume = volumes[-1]
            
            if current_volume > avg_volume * 1.2:
                confidence_adjustment = 0.1
            elif current_volume < avg_volume * 0.8:
                confidence_adjustment = -0.1
            else:
                confidence_adjustment = 0
            
            final_confidence = max(0.2, min(0.8, base_confidence + confidence_adjustment))
            
            # Determine strength
            if final_confidence >= 0.6:
                strength = SignalStrength.STRONG
            elif final_confidence >= 0.4:
                strength = SignalStrength.MEDIUM
            else:
                strength = SignalStrength.WEAK
            
            # Determine market state
            if abs(price_change_pct) > 0.005:  # > 0.5% movement
                market_state = "volatile"
            elif price_change_pct > 0.002:
                market_state = "trending_up"
            elif price_change_pct < -0.002:
                market_state = "trending_down"
            else:
                market_state = "ranging"
            
            return {
                'direction': direction,
                'confidence': final_confidence,
                'strength': strength,
                'market_state': market_state,
                'indicators': ['price_action', 'volume'],
                'metadata': {
                    'price_change': price_change,
                    'price_change_pct': price_change_pct,
                    'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 1,
                    'confidence_adjustment': confidence_adjustment
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing price action: {e}")
            return None


class TrendFollowingSignalGenerator(BaseSignalGenerator):
    """Trend following signal generator using moving averages."""
    
    def __init__(self, confidence_threshold: float = 0.3, 
                 short_window: int = 5, long_window: int = 20):
        super().__init__(confidence_threshold)
        self.short_window = short_window
        self.long_window = long_window
    
    def get_strategy_name(self) -> str:
        return "trend_following"
    
    def generate_signal(self, market_data: pd.DataFrame) -> Optional[SignalResult]:
        """Generate signal based on trend following strategy."""
        if not self._validate_market_data(market_data):
            return None
        
        if len(market_data) < self.long_window:
            self.logger.warning(f"Insufficient data for trend analysis (need {self.long_window}, got {len(market_data)})")
            return None
        
        try:
            # Calculate moving averages
            short_ma = market_data['close'].rolling(self.short_window).mean().iloc[-1]
            long_ma = market_data['close'].rolling(self.long_window).mean().iloc[-1]
            
            current_price = self._get_current_price(market_data)
            
            # Determine trend direction
            if short_ma > long_ma:
                direction = SignalDirection.LONG
                trend_strength = (short_ma - long_ma) / long_ma
            else:
                direction = SignalDirection.SHORT
                trend_strength = (long_ma - short_ma) / long_ma
            
            # Calculate confidence based on trend strength
            base_confidence = min(0.8, 0.3 + trend_strength * 10)
            
            # Adjust for price position relative to MAs
            if direction == SignalDirection.LONG and current_price > short_ma:
                confidence_boost = 0.1
            elif direction == SignalDirection.SHORT and current_price < short_ma:
                confidence_boost = 0.1
            else:
                confidence_boost = -0.1
            
            final_confidence = max(0.2, base_confidence + confidence_boost)
            
            # Determine strength and market state
            if trend_strength > 0.02:
                strength = SignalStrength.STRONG
                market_state = "trending_up" if direction == SignalDirection.LONG else "trending_down"
            elif trend_strength > 0.01:
                strength = SignalStrength.MEDIUM  
                market_state = "trending_up" if direction == SignalDirection.LONG else "trending_down"
            else:
                strength = SignalStrength.WEAK
                market_state = "ranging"
            
            return SignalResult(
                direction=direction,
                confidence=final_confidence,
                strength=strength,
                entry_price=current_price,
                market_state=market_state,
                trigger_pattern="trend_following",
                indicators=[f"MA{self.short_window}", f"MA{self.long_window}"],
                metadata={
                    "short_ma": short_ma,
                    "long_ma": long_ma,
                    "trend_strength": trend_strength,
                    "price_above_short_ma": current_price > short_ma,
                    "price_above_long_ma": current_price > long_ma
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error in trend following signal generation: {e}")
            return None


class SignalGeneratorFactory:
    """Factory for creating signal generators."""
    
    _generators = {
        "confidence_scoring": ConfidenceScoringSignalGenerator,
        "simple": SimpleSignalGenerator,
        "trend_following": TrendFollowingSignalGenerator
    }
    
    @classmethod
    def create_generator(cls, strategy_name: str, **kwargs) -> BaseSignalGenerator:
        """Create a signal generator by strategy name."""
        if strategy_name not in cls._generators:
            available = ", ".join(cls._generators.keys())
            raise ValueError(f"Unknown strategy '{strategy_name}'. Available: {available}")
        
        return cls._generators[strategy_name](**kwargs)
    
    @classmethod
    def get_available_strategies(cls) -> List[str]:
        """Get list of available signal generation strategies."""
        return list(cls._generators.keys())