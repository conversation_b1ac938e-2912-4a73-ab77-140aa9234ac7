"""
Analysis package for trading signal generation and market analysis.

This package provides modular and extensible components for:
- Signal generation strategies
- Market data analysis  
- Technical indicators
- Position sizing
"""

from .signal_generators import (
    SignalDirection,
    SignalStrength, 
    MarketState,
    SignalResult,
    BaseSignalGenerator,
    ConfidenceScoringSignalGenerator,
    SimpleSignalGenerator,
    TrendFollowingSignalGenerator,
    SignalGeneratorFactory
)

__all__ = [
    "SignalDirection",
    "SignalStrength",
    "MarketState", 
    "SignalResult",
    "BaseSignalGenerator",
    "ConfidenceScoringSignalGenerator",
    "SimpleSignalGenerator", 
    "TrendFollowingSignalGenerator",
    "SignalGeneratorFactory"
]