"""
Position Sizing Module

Handles position sizing logic with different strategies for determining
bet amounts based on confidence, risk parameters, and market conditions.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from enum import Enum

from quant.utils.logger import get_logger

logger = get_logger(__name__)


class SizingMode(Enum):
    """Position sizing modes."""
    FIXED = "fixed"
    CONFIDENCE_BASED = "confidence_based"  
    RISK_PARITY = "risk_parity"
    KELLY = "kelly"


class BaseSizer(ABC):
    """Abstract base class for position sizing strategies."""
    
    def __init__(self, min_amount: float, max_amount: float):
        self.min_amount = min_amount
        self.max_amount = max_amount
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def calculate_size(self, confidence: float, **kwargs) -> float:
        """Calculate position size based on confidence and other factors."""
        pass
    
    def _validate_and_clamp(self, size: float) -> float:
        """Validate and clamp position size to acceptable range."""
        if size < self.min_amount:
            self.logger.debug(f"Position size {size} below minimum {self.min_amount}, clamping")
            return self.min_amount
        
        if size > self.max_amount:
            self.logger.debug(f"Position size {size} above maximum {self.max_amount}, clamping")
            return self.max_amount
        
        return size


class FixedSizer(BaseSizer):
    """Fixed position sizing - always returns the same amount."""
    
    def __init__(self, fixed_amount: float, min_amount: float = None, max_amount: float = None):
        # For fixed sizing, min and max are the same as fixed amount
        super().__init__(
            min_amount or fixed_amount,
            max_amount or fixed_amount
        )
        self.fixed_amount = fixed_amount
    
    def calculate_size(self, confidence: float, **kwargs) -> float:
        """Return fixed amount regardless of confidence."""
        return self._validate_and_clamp(self.fixed_amount)


class ConfidenceBasedSizer(BaseSizer):
    """Confidence-based position sizing with optional adjustments."""
    
    def __init__(self, 
                 min_amount: float,
                 max_amount: float,
                 min_confidence_for_full: float = 0.8,
                 confidence_curve: str = "linear"):
        super().__init__(min_amount, max_amount)
        self.min_confidence_for_full = min_confidence_for_full
        self.confidence_curve = confidence_curve
    
    def calculate_size(self, confidence: float, **kwargs) -> float:
        """Calculate size based on confidence score."""
        if confidence >= self.min_confidence_for_full:
            # Full size for high confidence
            size = self.max_amount
        else:
            # Scale between min and max based on confidence
            if self.confidence_curve == "linear":
                ratio = confidence / self.min_confidence_for_full
            elif self.confidence_curve == "exponential":
                ratio = (confidence / self.min_confidence_for_full) ** 2
            elif self.confidence_curve == "logarithmic":
                import math
                ratio = math.log(1 + confidence) / math.log(1 + self.min_confidence_for_full)
            else:
                ratio = confidence / self.min_confidence_for_full
            
            size = self.min_amount + (self.max_amount - self.min_amount) * ratio
        
        return self._validate_and_clamp(size)


class RiskParitySizer(BaseSizer):
    """Risk parity position sizing based on volatility."""
    
    def __init__(self, 
                 min_amount: float,
                 max_amount: float,
                 target_risk: float = 0.02,
                 lookback_periods: int = 20):
        super().__init__(min_amount, max_amount)
        self.target_risk = target_risk
        self.lookback_periods = lookback_periods
    
    def calculate_size(self, confidence: float, **kwargs) -> float:
        """Calculate size based on risk parity principles."""
        market_data = kwargs.get('market_data')
        
        if market_data is None or len(market_data) < self.lookback_periods:
            self.logger.warning("Insufficient market data for risk parity sizing, using confidence-based fallback")
            # Fallback to simple confidence scaling
            return self._validate_and_clamp(self.min_amount + (self.max_amount - self.min_amount) * confidence)
        
        try:
            # Calculate historical volatility
            returns = market_data['close'].pct_change().dropna()
            volatility = returns.tail(self.lookback_periods).std()
            
            if volatility <= 0:
                volatility = 0.01  # Minimum volatility assumption
            
            # Calculate position size based on target risk
            # Position size = target_risk / volatility
            risk_adjusted_size = (self.target_risk / volatility) * self.max_amount
            
            # Apply confidence scaling
            confidence_adjusted_size = risk_adjusted_size * confidence
            
            return self._validate_and_clamp(confidence_adjusted_size)
            
        except Exception as e:
            self.logger.error(f"Error in risk parity calculation: {e}")
            # Fallback to simple confidence scaling
            return self._validate_and_clamp(self.min_amount + (self.max_amount - self.min_amount) * confidence)


class KellySizer(BaseSizer):
    """Kelly criterion position sizing."""
    
    def __init__(self, 
                 min_amount: float,
                 max_amount: float,
                 win_rate: float = 0.55,
                 avg_win: float = 1.0,
                 avg_loss: float = 1.0,
                 kelly_fraction: float = 0.25):
        super().__init__(min_amount, max_amount)
        self.win_rate = win_rate
        self.avg_win = avg_win
        self.avg_loss = avg_loss
        self.kelly_fraction = kelly_fraction  # Fractional Kelly to reduce risk
    
    def calculate_size(self, confidence: float, **kwargs) -> float:
        """Calculate size using Kelly criterion."""
        try:
            # Adjust win rate based on confidence
            adjusted_win_rate = self.win_rate * confidence
            lose_rate = 1 - adjusted_win_rate
            
            # Kelly formula: f = (bp - q) / b
            # where b = avg_win/avg_loss, p = win_rate, q = lose_rate
            if self.avg_loss <= 0:
                self.logger.warning("Invalid average loss for Kelly calculation")
                return self._validate_and_clamp(self.min_amount)
            
            b = self.avg_win / self.avg_loss
            kelly_fraction_optimal = (b * adjusted_win_rate - lose_rate) / b
            
            # Apply fractional Kelly to reduce risk
            kelly_fraction_actual = kelly_fraction_optimal * self.kelly_fraction
            
            # Ensure Kelly fraction is positive and reasonable
            kelly_fraction_actual = max(0.01, min(0.5, kelly_fraction_actual))
            
            # Calculate position size
            size = self.max_amount * kelly_fraction_actual
            
            return self._validate_and_clamp(size)
            
        except Exception as e:
            self.logger.error(f"Error in Kelly calculation: {e}")
            return self._validate_and_clamp(self.min_amount)


class PositionSizer:
    """Main position sizing class that uses different sizing strategies."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize position sizer with configuration."""
        self.config = config
        self.logger = get_logger(__name__)
        
        # Extract configuration with error handling
        mode_str = config.get('mode', 'fixed')
        try:
            self.mode = SizingMode(mode_str)
        except ValueError:
            self.logger.warning(f"Unknown sizing mode '{mode_str}', falling back to fixed mode")
            self.mode = SizingMode.FIXED
            
        self.min_amount = float(config.get('min_amount', 10))
        self.max_amount = float(config.get('max_amount', 150))
        
        # Create appropriate sizer
        self.sizer = self._create_sizer()
        
        self.logger.info(f"Position sizer initialized with mode: {self.mode.value}")
    
    def _create_sizer(self) -> BaseSizer:
        """Create appropriate sizer based on configuration."""
        if self.mode == SizingMode.FIXED:
            fixed_amount = float(self.config.get('fixed_amount', self.max_amount))
            return FixedSizer(fixed_amount, self.min_amount, self.max_amount)
        
        elif self.mode == SizingMode.CONFIDENCE_BASED:
            min_conf_for_full = float(self.config.get('min_confidence_for_full', 0.8))
            curve = self.config.get('confidence_curve', 'linear')
            return ConfidenceBasedSizer(
                self.min_amount, 
                self.max_amount,
                min_conf_for_full,
                curve
            )
        
        elif self.mode == SizingMode.RISK_PARITY:
            target_risk = float(self.config.get('target_risk', 0.02))
            lookback = int(self.config.get('lookback_periods', 20))
            return RiskParitySizer(
                self.min_amount,
                self.max_amount, 
                target_risk,
                lookback
            )
        
        elif self.mode == SizingMode.KELLY:
            win_rate = float(self.config.get('win_rate', 0.55))
            avg_win = float(self.config.get('avg_win', 1.0))
            avg_loss = float(self.config.get('avg_loss', 1.0))
            kelly_frac = float(self.config.get('kelly_fraction', 0.25))
            return KellySizer(
                self.min_amount,
                self.max_amount,
                win_rate,
                avg_win,
                avg_loss,
                kelly_frac
            )
        
        else:
            self.logger.warning(f"Unknown sizing mode {self.mode}, falling back to fixed")
            return FixedSizer(self.max_amount, self.min_amount, self.max_amount)
    
    def calculate_position_size(self, 
                              confidence: float,
                              market_data = None,
                              **kwargs) -> float:
        """Calculate position size for a trade."""
        try:
            size = self.sizer.calculate_size(
                confidence=confidence,
                market_data=market_data,
                **kwargs
            )
            
            self.logger.debug(f"Calculated position size: {size} (confidence: {confidence:.3f})")
            return size
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return self.min_amount
    
    def update_performance_stats(self, win_rate: float, avg_win: float, avg_loss: float):
        """Update performance statistics for adaptive sizing."""
        if isinstance(self.sizer, KellySizer):
            self.sizer.win_rate = win_rate
            self.sizer.avg_win = avg_win
            self.sizer.avg_loss = avg_loss
            self.logger.info(f"Updated Kelly sizer stats: WR={win_rate:.3f}, AvgWin={avg_win:.3f}, AvgLoss={avg_loss:.3f}")


def create_position_sizer_from_config(config_dict: Dict[str, Any]) -> PositionSizer:
    """Create position sizer from configuration dictionary."""
    
    # Handle legacy simple bet control configuration
    simple_bet_config = config_dict.get('SIMPLE_BET_CONTROL', {})
    risk_config = config_dict.get('RISK_MANAGEMENT', {})
    auto_trader_config = config_dict.get('AUTO_TRADER', {})
    
    if simple_bet_config.get('enabled', False):
        # Legacy fixed bet mode
        sizing_config = {
            'mode': 'fixed',
            'fixed_amount': simple_bet_config.get('fixed_bet_amount', 150.0),
            'min_amount': auto_trader_config.get('min_order_usdt', 10),
            'max_amount': simple_bet_config.get('fixed_bet_amount', 150.0)
        }
    else:
        # Modern confidence-based sizing
        min_amount = float(auto_trader_config.get('min_order_usdt', 10))
        max_amount = float(risk_config.get('base_position_size_usdt', 150))
        
        sizing_config = {
            'mode': 'confidence_based',
            'min_amount': min_amount,
            'max_amount': max(min_amount, max_amount),
            'min_confidence_for_full': simple_bet_config.get('min_confidence_for_full_bet', 0.8),
            'confidence_curve': 'linear'
        }
    
    return PositionSizer(sizing_config)