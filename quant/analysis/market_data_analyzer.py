"""
Market Data Analysis Module

Provides clean abstractions for market data analysis and validation.
Separates data processing concerns from signal generation logic.
"""

from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from quant.utils.logger import get_logger

logger = get_logger(__name__)


class MarketDataValidator:
    """Validates market data quality and completeness."""
    
    @staticmethod
    def validate_ohlcv_data(df: pd.DataFrame) -> Dict[str, Any]:
        """Validate OHLCV market data and return validation results."""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'data_quality_score': 1.0,
            'row_count': len(df),
            'missing_values': 0,
            'anomalies': []
        }
        
        # Check if DataFrame is empty
        if df.empty:
            validation_result['is_valid'] = False
            validation_result['errors'].append('Market data is empty')
            validation_result['data_quality_score'] = 0.0
            return validation_result
        
        # Check required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f'Missing required columns: {missing_columns}')
        
        # Check for missing values
        total_values = len(df) * len(required_columns)
        missing_values = df[required_columns].isnull().sum().sum()
        if missing_values > 0:
            validation_result['missing_values'] = missing_values
            validation_result['warnings'].append(f'Found {missing_values} missing values')
            validation_result['data_quality_score'] *= (total_values - missing_values) / total_values
        
        # Check for data anomalies
        try:
            # Check for negative prices
            price_columns = ['open', 'high', 'low', 'close']
            negative_prices = (df[price_columns] <= 0).any().any()
            if negative_prices:
                validation_result['anomalies'].append('Found negative or zero prices')
                validation_result['data_quality_score'] *= 0.8
            
            # Check for invalid OHLC relationships
            invalid_ohlc = (
                (df['high'] < df['low']) |
                (df['high'] < df['open']) |
                (df['high'] < df['close']) |
                (df['low'] > df['open']) |
                (df['low'] > df['close'])
            ).any()
            
            if invalid_ohlc:
                validation_result['anomalies'].append('Found invalid OHLC relationships')
                validation_result['data_quality_score'] *= 0.7
            
            # Check for extreme price movements (>50% in one period)
            if len(df) > 1:
                price_changes = df['close'].pct_change().abs()
                extreme_moves = (price_changes > 0.5).sum()
                if extreme_moves > 0:
                    validation_result['anomalies'].append(f'Found {extreme_moves} extreme price movements (>50%)')
                    validation_result['data_quality_score'] *= 0.9
            
        except Exception as e:
            validation_result['warnings'].append(f'Error during anomaly detection: {str(e)}')
        
        # Set overall validity
        if validation_result['data_quality_score'] < 0.5:
            validation_result['is_valid'] = False
            validation_result['errors'].append('Data quality score too low')
        
        return validation_result


class TechnicalIndicatorCalculator:
    """Calculate common technical indicators with error handling."""
    
    @staticmethod
    def simple_moving_average(data: pd.Series, window: int) -> pd.Series:
        """Calculate Simple Moving Average."""
        if len(data) < window:
            logger.warning(f"Insufficient data for SMA({window}): got {len(data)} points")
            return pd.Series(index=data.index, dtype=float)
        
        return data.rolling(window=window, min_periods=1).mean()
    
    @staticmethod
    def exponential_moving_average(data: pd.Series, window: int) -> pd.Series:
        """Calculate Exponential Moving Average."""
        if len(data) < window:
            logger.warning(f"Insufficient data for EMA({window}): got {len(data)} points")
            return pd.Series(index=data.index, dtype=float)
        
        return data.ewm(span=window, min_periods=1).mean()
    
    @staticmethod
    def bollinger_bands(data: pd.Series, window: int = 20, num_std: float = 2) -> Dict[str, pd.Series]:
        """Calculate Bollinger Bands."""
        if len(data) < window:
            logger.warning(f"Insufficient data for Bollinger Bands({window}): got {len(data)} points")
            empty_series = pd.Series(index=data.index, dtype=float)
            return {
                'middle': empty_series,
                'upper': empty_series,
                'lower': empty_series
            }
        
        sma = TechnicalIndicatorCalculator.simple_moving_average(data, window)
        std = data.rolling(window=window, min_periods=1).std()
        
        return {
            'middle': sma,
            'upper': sma + (std * num_std),
            'lower': sma - (std * num_std)
        }
    
    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Relative Strength Index."""
        if len(data) < window + 1:
            logger.warning(f"Insufficient data for RSI({window}): got {len(data)} points")
            return pd.Series(index=data.index, dtype=float)
        
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window, min_periods=1).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window, min_periods=1).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi.fillna(50)  # Fill NaN with neutral RSI
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """Calculate MACD indicator."""
        if len(data) < slow:
            logger.warning(f"Insufficient data for MACD: got {len(data)} points, need at least {slow}")
            empty_series = pd.Series(index=data.index, dtype=float)
            return {
                'macd': empty_series,
                'signal': empty_series,
                'histogram': empty_series
            }
        
        ema_fast = TechnicalIndicatorCalculator.exponential_moving_average(data, fast)
        ema_slow = TechnicalIndicatorCalculator.exponential_moving_average(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicatorCalculator.exponential_moving_average(macd_line, signal)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    @staticmethod
    def stochastic_oscillator(high: pd.Series, low: pd.Series, close: pd.Series, 
                            k_window: int = 14, d_window: int = 3) -> Dict[str, pd.Series]:
        """Calculate Stochastic Oscillator."""
        if len(close) < k_window:
            logger.warning(f"Insufficient data for Stochastic({k_window}): got {len(close)} points")
            empty_series = pd.Series(index=close.index, dtype=float)
            return {
                'k_percent': empty_series,
                'd_percent': empty_series
            }
        
        lowest_low = low.rolling(window=k_window, min_periods=1).min()
        highest_high = high.rolling(window=k_window, min_periods=1).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_window, min_periods=1).mean()
        
        return {
            'k_percent': k_percent.fillna(50),
            'd_percent': d_percent.fillna(50)
        }


class MarketDataAnalyzer:
    """Comprehensive market data analysis with built-in error handling."""
    
    def __init__(self):
        self.validator = MarketDataValidator()
        self.indicator_calc = TechnicalIndicatorCalculator()
        self.logger = get_logger(__name__)
    
    def analyze_market_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Perform comprehensive market data analysis."""
        analysis_result = {
            'validation': None,
            'basic_stats': None,
            'technical_indicators': None,
            'market_conditions': None,
            'analysis_timestamp': datetime.utcnow().isoformat()
        }
        
        # Step 1: Validate data
        validation = self.validator.validate_ohlcv_data(df)
        analysis_result['validation'] = validation
        
        if not validation['is_valid']:
            self.logger.error(f"Market data validation failed: {validation['errors']}")
            return analysis_result
        
        try:
            # Step 2: Calculate basic statistics
            analysis_result['basic_stats'] = self._calculate_basic_stats(df)
            
            # Step 3: Calculate technical indicators
            analysis_result['technical_indicators'] = self._calculate_technical_indicators(df)
            
            # Step 4: Determine market conditions
            analysis_result['market_conditions'] = self._analyze_market_conditions(
                df, analysis_result['technical_indicators']
            )
            
        except Exception as e:
            self.logger.error(f"Error during market analysis: {e}")
            analysis_result['error'] = str(e)
        
        return analysis_result
    
    def _calculate_basic_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate basic market statistics."""
        try:
            current_price = float(df['close'].iloc[-1])
            prev_price = float(df['close'].iloc[-2]) if len(df) > 1 else current_price
            
            # Price change statistics
            returns = df['close'].pct_change().dropna()
            
            stats = {
                'current_price': current_price,
                'price_change': current_price - prev_price,
                'price_change_pct': ((current_price - prev_price) / prev_price) if prev_price != 0 else 0,
                'volume_current': float(df['volume'].iloc[-1]),
                'volume_avg_20': float(df['volume'].tail(20).mean()) if len(df) >= 20 else float(df['volume'].mean()),
                'volatility_20d': float(returns.tail(20).std()) if len(returns) >= 20 else float(returns.std()) if len(returns) > 0 else 0,
                'high_24h': float(df['high'].tail(24).max()) if len(df) >= 24 else float(df['high'].max()),
                'low_24h': float(df['low'].tail(24).min()) if len(df) >= 24 else float(df['low'].min()),
                'data_points': len(df),
                'time_span_hours': len(df) * 0.5  # Assuming 30-minute data
            }
            
            # Add relative volume indicator
            if stats['volume_avg_20'] > 0:
                stats['volume_ratio'] = stats['volume_current'] / stats['volume_avg_20']
            else:
                stats['volume_ratio'] = 1.0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error calculating basic stats: {e}")
            return {}
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate various technical indicators."""
        indicators = {}
        
        try:
            close = df['close']
            high = df['high']
            low = df['low']
            
            # Moving Averages
            indicators['sma_5'] = self.indicator_calc.simple_moving_average(close, 5).iloc[-1] if len(df) >= 5 else None
            indicators['sma_20'] = self.indicator_calc.simple_moving_average(close, 20).iloc[-1] if len(df) >= 20 else None
            indicators['ema_12'] = self.indicator_calc.exponential_moving_average(close, 12).iloc[-1] if len(df) >= 12 else None
            indicators['ema_26'] = self.indicator_calc.exponential_moving_average(close, 26).iloc[-1] if len(df) >= 26 else None
            
            # Bollinger Bands
            if len(df) >= 20:
                bb = self.indicator_calc.bollinger_bands(close, 20)
                indicators['bb_upper'] = bb['upper'].iloc[-1]
                indicators['bb_middle'] = bb['middle'].iloc[-1]
                indicators['bb_lower'] = bb['lower'].iloc[-1]
                indicators['bb_position'] = ((close.iloc[-1] - bb['lower'].iloc[-1]) / 
                                           (bb['upper'].iloc[-1] - bb['lower'].iloc[-1])) if bb['upper'].iloc[-1] != bb['lower'].iloc[-1] else 0.5
            
            # RSI
            if len(df) >= 15:
                indicators['rsi'] = self.indicator_calc.rsi(close, 14).iloc[-1]
            
            # MACD
            if len(df) >= 26:
                macd = self.indicator_calc.macd(close)
                indicators['macd'] = macd['macd'].iloc[-1]
                indicators['macd_signal'] = macd['signal'].iloc[-1]
                indicators['macd_histogram'] = macd['histogram'].iloc[-1]
            
            # Stochastic
            if len(df) >= 14:
                stoch = self.indicator_calc.stochastic_oscillator(high, low, close)
                indicators['stoch_k'] = stoch['k_percent'].iloc[-1]
                indicators['stoch_d'] = stoch['d_percent'].iloc[-1]
            
            # Remove None values
            indicators = {k: v for k, v in indicators.items() if v is not None and not pd.isna(v)}
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
        
        return indicators
    
    def _analyze_market_conditions(self, df: pd.DataFrame, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze current market conditions based on price action and indicators."""
        conditions = {
            'trend_direction': 'neutral',
            'trend_strength': 'weak',
            'volatility_level': 'normal',
            'volume_condition': 'normal',
            'momentum': 'neutral',
            'market_phase': 'consolidation'
        }
        
        try:
            current_price = df['close'].iloc[-1]
            
            # Trend Analysis
            if 'sma_5' in indicators and 'sma_20' in indicators:
                sma_5 = indicators['sma_5']
                sma_20 = indicators['sma_20']
                
                if sma_5 > sma_20 and current_price > sma_5:
                    conditions['trend_direction'] = 'bullish'
                elif sma_5 < sma_20 and current_price < sma_5:
                    conditions['trend_direction'] = 'bearish'
                
                # Trend strength based on MA separation
                ma_separation = abs(sma_5 - sma_20) / sma_20
                if ma_separation > 0.02:
                    conditions['trend_strength'] = 'strong'
                elif ma_separation > 0.01:
                    conditions['trend_strength'] = 'moderate'
            
            # Volatility Analysis
            if len(df) >= 20:
                recent_volatility = df['close'].pct_change().tail(20).std()
                historical_volatility = df['close'].pct_change().std()
                
                if recent_volatility > historical_volatility * 1.5:
                    conditions['volatility_level'] = 'high'
                elif recent_volatility < historical_volatility * 0.5:
                    conditions['volatility_level'] = 'low'
            
            # Volume Analysis
            current_volume = df['volume'].iloc[-1]
            avg_volume = df['volume'].tail(20).mean() if len(df) >= 20 else df['volume'].mean()
            
            if current_volume > avg_volume * 1.5:
                conditions['volume_condition'] = 'high'
            elif current_volume < avg_volume * 0.5:
                conditions['volume_condition'] = 'low'
            
            # Momentum Analysis using RSI
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                if rsi > 70:
                    conditions['momentum'] = 'overbought'
                elif rsi < 30:
                    conditions['momentum'] = 'oversold'
                elif rsi > 55:
                    conditions['momentum'] = 'bullish'
                elif rsi < 45:
                    conditions['momentum'] = 'bearish'
            
            # Market Phase Analysis
            if 'bb_position' in indicators:
                bb_pos = indicators['bb_position']
                if bb_pos > 0.8 or bb_pos < 0.2:
                    conditions['market_phase'] = 'trending'
                elif 0.3 < bb_pos < 0.7:
                    conditions['market_phase'] = 'consolidation'
                else:
                    conditions['market_phase'] = 'transition'
            
        except Exception as e:
            self.logger.error(f"Error analyzing market conditions: {e}")
        
        return conditions
    
    def get_data_summary(self, df: pd.DataFrame) -> str:
        """Get a human-readable summary of market data."""
        if df.empty:
            return "No market data available"
        
        try:
            current_price = df['close'].iloc[-1]
            prev_price = df['close'].iloc[-2] if len(df) > 1 else current_price
            change_pct = ((current_price - prev_price) / prev_price * 100) if prev_price != 0 else 0
            
            return (f"Price: ${current_price:,.2f} "
                   f"({change_pct:+.2f}%) "
                   f"| Data points: {len(df)} "
                   f"| Timespan: {len(df) * 0.5:.1f}h")
        
        except Exception as e:
            return f"Error generating summary: {e}"