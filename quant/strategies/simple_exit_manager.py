"""
Simple Exit Manager - 简化的平仓管理器

设计原则：
1. 简单可靠：只在K线结束前平仓
2. 单一职责：只负责平仓，不处理复杂逻辑
3. 易于调试：清晰的日志和状态跟踪
"""

from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import asyncio

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.notification_manager import notification_manager
from quant.utils.logger import get_logger
from quant.utils.structured_logger import get_trade_logger, get_retry_logger, OperationType
from quant.config_manager import config

logger = get_logger(__name__)
structured_logger = get_trade_logger("exit_manager")


class SimpleExitManager:
    """简化的平仓管理器 - 在K线结束前自动平仓"""
    
    def __init__(self):
        # 从配置读取，默认值更简单
        exit_cfg = config.get("SIMPLE_EXIT", {}) or {}

        # 核心参数：K线结束前多少分钟平仓
        self.exit_before_kline_end_minutes = int(exit_cfg.get("exit_before_kline_end_minutes", 2))  # K线结束前2分钟平仓

        # 30分钟K线周期
        self.kline_period_minutes = 30

        # 最小持仓时间保护（分钟）
        self.min_hold_minutes = float(exit_cfg.get("min_hold_minutes", 1.0))  # 最少持仓1分钟
        
        # 基于盈亏状态的持仓时间设置
        self.profit_hold_minutes = float(exit_cfg.get("profit_hold_minutes", 20.0))  # 盈利时持仓20分钟
        self.loss_hold_minutes = float(exit_cfg.get("loss_hold_minutes", 10.0))  # 亏损时持仓10分钟
        self.use_dynamic_exit = bool(exit_cfg.get("use_dynamic_exit", True))  # 是否启用动态退出时间

        # 待平仓的交易队列
        self._pending_exits: Dict[int, Dict[str, Any]] = {}

        # 单一的检查任务
        self._check_task: Optional[asyncio.Task] = None

        # 检查间隔（秒）
        self.check_interval_seconds = 10

        # 平仓重试配置
        self.exit_max_retries = int(exit_cfg.get("max_exit_retries", 3))
        self.exit_retry_backoff_seconds = float(exit_cfg.get("retry_backoff_seconds", 2.0))

        # 平仓处理锁，防止重复处理同一交易
        self._processing_locks: set[int] = set()

        logger.info(f"SimpleExitManager initialized: exit {self.exit_before_kline_end_minutes} minutes before K-line end; min_hold={self.min_hold_minutes} minutes; profit_hold={self.profit_hold_minutes} minutes; loss_hold={self.loss_hold_minutes} minutes; dynamic_exit={self.use_dynamic_exit}; retries={self.exit_max_retries} backoff={self.exit_retry_backoff_seconds}s")
    
    async def start(self):
        """启动平仓管理器"""
        if self._check_task and not self._check_task.done():
            logger.warning("Exit manager already running")
            return
        
        self._check_task = asyncio.create_task(self._check_loop())
        logger.info("Simple exit manager started")
        
        # 回填数据库中的未结交易，避免重启后漏管
        try:
            await self._seed_pending_from_db()
        except Exception as e:
            logger.error(f"Failed to seed pending trades from DB: {e}")

    async def stop(self):
        """停止平仓管理器"""
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
            self._check_task = None
        logger.info("Simple exit manager stopped")
    
    async def _seed_pending_from_db(self):
        """从数据库回填未结交易，确保已开仓的挂入监控队列。"""
        try:
            pending_trades = db.get_pending_trades() or []
            if not pending_trades:
                logger.info("No pending trades in DB to seed")
                return
            logger.info(f"Seeding {len(pending_trades)} pending trades from DB into exit manager")
            for trade in pending_trades:
                trade_id = trade.get("id")
                if trade_id in self._pending_exits:
                    continue
                trade_data = {
                    "id": trade_id,
                    "entry_price": trade.get("entry_price"),
                    "direction": trade.get("direction"),
                    "symbol": trade.get("symbol", "BTCUSDT"),
                    "suggested_bet": trade.get("suggested_bet", 0),
                    "signal_timestamp": trade.get("signal_timestamp"),
                }
                # 仅回填合理的数据
                if trade_data["entry_price"] and trade_data["direction"] and trade_data["suggested_bet"]:
                    self.add_position(trade_id, trade_data)
        except Exception as e:
            logger.error(f"Error seeding pending trades: {e}")

    def add_position(self, trade_id: int, trade_data: Dict[str, Any]):
        """添加需要监控的持仓"""
        # 检查是否是测试交易
        entry_price = trade_data.get("entry_price", 0)
        if entry_price == 50000.0:
            logger.warning(f"Detected test trade {trade_id} with test price {entry_price}, applying stricter protection")
            # 对测试交易应用更严格的保护（最少持仓2分钟）
            test_min_hold = 2.0
        else:
            test_min_hold = self.min_hold_minutes

        # 计算该持仓应该平仓的时间
        signal_time = trade_data.get("signal_timestamp")
        if isinstance(signal_time, str):
            # 处理字符串时间，确保转换为naive UTC时间
            if 'Z' in signal_time or '+' in signal_time or 'T' in signal_time:
                # 处理ISO格式的时间字符串
                signal_time = datetime.fromisoformat(signal_time.replace('Z', '+00:00'))
                # 如果是aware datetime，转换为naive UTC
                if signal_time.tzinfo is not None:
                    signal_time = signal_time.replace(tzinfo=None)
            else:
                signal_time = datetime.fromisoformat(signal_time)
        elif not isinstance(signal_time, datetime):
            signal_time = datetime.utcnow()
        else:
            # 如果已经是datetime对象，确保是naive的
            if signal_time.tzinfo is not None:
                signal_time = signal_time.replace(tzinfo=None)

        # 如果启用动态退出，根据盈亏状态设置不同的持仓时间
        if self.use_dynamic_exit:
            # 初始设置为亏损时间（因为开仓时无法确定盈亏）
            # 实际持仓时间将在检查循环中动态调整
            dynamic_exit_time = signal_time + timedelta(minutes=self.loss_hold_minutes)
            actual_exit_time = dynamic_exit_time
            kline_end_time = self._get_kline_end_time(signal_time)  # 仍然计算K线结束时间供参考
            exit_time = dynamic_exit_time  # 使用动态时间
        else:
            # 使用原有的K线结束前平仓逻辑
            kline_end_time = self._get_kline_end_time(signal_time)
            exit_time = kline_end_time - timedelta(minutes=self.exit_before_kline_end_minutes)
            min_exit_time = signal_time + timedelta(minutes=test_min_hold)
            actual_exit_time = max(exit_time, min_exit_time)

        self._pending_exits[trade_id] = {
            "trade_data": trade_data,
            "signal_time": signal_time,
            "kline_end_time": kline_end_time,
            "planned_exit_time": actual_exit_time,
            "original_exit_time": exit_time,
            "min_exit_time": signal_time + timedelta(minutes=test_min_hold),
            "min_hold_minutes": test_min_hold,
            "is_test_trade": entry_price == 50000.0,
            "status": "PENDING",
            "use_dynamic_exit": self.use_dynamic_exit,
            "last_pnl_check": None  # 记录上次盈亏检查时间
        }

        # 记录详细的时间计算信息
        trade_type = "TEST" if entry_price == 50000.0 else "REAL"
        if self.use_dynamic_exit:
            logger.info(f"Added {trade_type} trade {trade_id} for dynamic exit:")
            logger.info(f"  Signal time: {signal_time.isoformat()}")
            logger.info(f"  Initial exit time: {actual_exit_time.isoformat()}")
            logger.info(f"  Will adjust based on P&L (profit: {self.profit_hold_minutes}min, loss: {self.loss_hold_minutes}min)")
        else:
            logger.info(f"Added {trade_type} trade {trade_id} for exit:")
            logger.info(f"  Signal time: {signal_time.isoformat()}")
            logger.info(f"  K-line end: {kline_end_time.isoformat()}")
            logger.info(f"  Original exit: {exit_time.isoformat()}")
            logger.info(f"  Min exit: {self._pending_exits[trade_id]['min_exit_time'].isoformat()} (min_hold: {test_min_hold} min)")
            logger.info(f"  Actual exit: {actual_exit_time.isoformat()}")
    
    def _get_kline_end_time(self, signal_time: datetime) -> datetime:
        """计算K线结束时间，确保返回naive UTC时间"""
        # 确保输入时间是naive的
        if hasattr(signal_time, 'tzinfo') and signal_time.tzinfo is not None:
            signal_time = signal_time.replace(tzinfo=None)

        # 找到当前30分钟K线的起始点
        minute = (signal_time.minute // 30) * 30
        kline_start = signal_time.replace(minute=minute, second=0, microsecond=0)

        # K线结束时间
        kline_end = kline_start + timedelta(minutes=self.kline_period_minutes)

        # 确保返回的时间是naive的
        if hasattr(kline_end, 'tzinfo') and kline_end.tzinfo is not None:
            kline_end = kline_end.replace(tzinfo=None)

        return kline_end
    
    async def _check_loop(self):
        """主检查循环"""
        logger.info("Starting exit check loop")
        
        while True:
            try:
                await self._check_exits()
                await asyncio.sleep(self.check_interval_seconds)
            except asyncio.CancelledError:
                logger.info("Exit check loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in exit check loop: {e}")
                await asyncio.sleep(self.check_interval_seconds)
    
    async def _check_exits(self):
        """检查是否有需要平仓的交易"""
        current_time = datetime.utcnow()

        # 复制字典以避免在迭代时修改
        pending_exits_copy = dict(self._pending_exits)

        for trade_id, exit_info in pending_exits_copy.items():
            if exit_info["status"] != "PENDING":
                continue

            # 获取时间信息
            signal_time = exit_info.get("signal_time")
            planned_exit = exit_info.get("planned_exit_time")

            # 确保所有时间都是naive UTC
            try:
                if hasattr(signal_time, "tzinfo") and signal_time.tzinfo is not None:
                    signal_time = signal_time.replace(tzinfo=None)
                if hasattr(planned_exit, "tzinfo") and planned_exit.tzinfo is not None:
                    planned_exit = planned_exit.replace(tzinfo=None)
            except Exception as e:
                logger.error(f"Time normalization error for trade {trade_id}: {e}")
                continue

            # 检查最小持仓时间（使用交易特定的最小持仓时间）
            if signal_time:
                time_held = (current_time - signal_time).total_seconds() / 60
                required_min_hold = exit_info.get("min_hold_minutes", self.min_hold_minutes)
                if time_held < required_min_hold:
                    trade_type = "TEST" if exit_info.get("is_test_trade", False) else "REAL"
                    logger.debug(f"{trade_type} trade {trade_id} held for {time_held:.2f} minutes, minimum is {required_min_hold}")
                    continue
            
            # 如果启用动态退出，需要根据当前盈亏状态调整退出时间
            if exit_info.get("use_dynamic_exit", False):
                # 第一次立即检查，之后每分钟检查一次盈亏状态
                last_pnl_check = exit_info.get("last_pnl_check")
                should_check = last_pnl_check is None or (current_time - last_pnl_check).total_seconds() >= 60
                
                if should_check:
                    # 获取当前价格并计算盈亏
                    try:
                        current_price = await binance_client.get_current_price()
                        trade_data = exit_info.get("trade_data", {})
                        entry_price = float(trade_data.get("entry_price", 0))
                        direction = trade_data.get("direction")
                        
                        if current_price and entry_price > 0 and direction:
                            # 计算当前盈亏
                            if direction == "LONG":
                                pnl = (current_price - entry_price) / entry_price
                            else:  # SHORT
                                pnl = (entry_price - current_price) / entry_price
                            
                            # 根据盈亏状态调整退出时间
                            if pnl > 0:  # 盈利状态（预测正确）
                                new_exit_time = signal_time + timedelta(minutes=self.profit_hold_minutes)
                                if planned_exit != new_exit_time:
                                    logger.info(f"Trade {trade_id} is profitable ({pnl:.2%}), adjusting exit time to {self.profit_hold_minutes} minutes")
                                    self._pending_exits[trade_id]["planned_exit_time"] = new_exit_time
                                    planned_exit = new_exit_time
                            else:  # 亏损状态（预测错误）
                                new_exit_time = signal_time + timedelta(minutes=self.loss_hold_minutes)
                                if planned_exit != new_exit_time:
                                    logger.info(f"Trade {trade_id} is losing ({pnl:.2%}), adjusting exit time to {self.loss_hold_minutes} minutes")
                                    self._pending_exits[trade_id]["planned_exit_time"] = new_exit_time
                                    planned_exit = new_exit_time
                            
                            # 更新最后检查时间
                            self._pending_exits[trade_id]["last_pnl_check"] = current_time
                    except Exception as e:
                        logger.error(f"Error checking P&L for dynamic exit: {e}")

            # 检查是否到达平仓时间
            try:
                if current_time >= planned_exit:
                    logger.info(f"Trade {trade_id} reached exit time, executing close")
                    logger.info(f"  Current: {current_time.isoformat()}")
                    logger.info(f"  Planned: {planned_exit.isoformat()}")
                    logger.info(f"  Held: {time_held:.2f} minutes")

                    # 标记为处理中，避免重复处理
                    self._pending_exits[trade_id]["status"] = "PROCESSING"

                    # 执行平仓
                    success = await self._execute_exit(trade_id, exit_info)

                    if success:
                        # 从队列中移除
                        if trade_id in self._pending_exits:
                            del self._pending_exits[trade_id]
                        logger.info(f"Trade {trade_id} closed successfully")
                    else:
                        # 失败则重置状态，下次重试
                        self._pending_exits[trade_id]["status"] = "PENDING"
                        logger.warning(f"Failed to close trade {trade_id}, will retry")
                else:
                    # 记录距离平仓还有多久
                    time_to_exit = (planned_exit - current_time).total_seconds() / 60
                    if time_to_exit <= 5:
                        logger.debug(f"Trade {trade_id} will exit in {time_to_exit:.1f} minutes (held {time_held:.2f} minutes)")
            except TypeError as te:
                logger.error(f"Datetime compare TypeError for trade {trade_id}: {te}")
                logger.error(f"  Current time type: {type(current_time)}, value: {current_time}")
                logger.error(f"  Planned exit type: {type(planned_exit)}, value: {planned_exit}")

                # 不再使用危险的回退策略，而是跳过这次检查
                logger.warning(f"Skipping exit check for trade {trade_id} due to time comparison error")
                continue
            except Exception as e:
                logger.error(f"Unexpected error checking exit for trade {trade_id}: {e}")
                continue
    
    async def _execute_exit(self, trade_id: int, exit_info: Dict[str, Any]) -> bool:
        """执行平仓操作（带严格ID验证和重试机制）"""

        # 1. 检查是否已在处理中（防止重复处理）
        if trade_id in self._processing_locks:
            logger.warning(f"Trade {trade_id} is already being processed, skipping")
            return False

        # 2. 加锁
        self._processing_locks.add(trade_id)

        try:
            return await self._do_execute_exit(trade_id, exit_info)
        finally:
            # 3. 解锁
            self._processing_locks.discard(trade_id)

    async def _do_execute_exit(self, trade_id: int, exit_info: Dict[str, Any]) -> bool:
        """实际执行平仓操作的内部方法"""
        # Use structured logging with context and correlation ID
        with structured_logger.operation_context(
            OperationType.TRADE_EXECUTION,
            trade_id=trade_id,
            symbol=exit_info.get("trade_data", {}).get("symbol", "BTCUSDT")
        ) as ctx:
            
            trade_data = exit_info.get("trade_data", {})

            # 4. 严格的交易ID验证
            expected_trade_id = trade_data.get("id")
            if expected_trade_id != trade_id:
                structured_logger.error("Trade ID mismatch", extra={
                    "expected_trade_id": expected_trade_id,
                    "actual_trade_id": trade_id,
                    "trade_data": trade_data
                })
                return False

            # 5. 验证交易是否仍在数据库中且为PENDING状态
            with db.get_session() as session:
                from quant.database_manager import TradeHistory
                trade = session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()

                if not trade:
                    structured_logger.error("Trade not found in database")
                    return False

                if trade.status != "PENDING":
                    # This is the fix: Log as an ERROR for clarity, but return True.
                    # Returning True signals that the exit operation is "complete" for this manager's purpose,
                    # allowing the trade to be removed from the pending queue and preventing endless retry loops.
                    structured_logger.error("Trade not in PENDING status", extra={"current_status": trade.status})
                    return True

                # 验证交易数据一致性
                if abs(trade.entry_price - trade_data.get("entry_price", 0)) > 0.01:
                    structured_logger.error("Entry price mismatch", extra={
                        "db_price": trade.entry_price,
                        "memory_price": trade_data.get("entry_price")
                    })
                    return False

            # 7. 基础参数与校验
            symbol = trade_data.get("symbol", "BTCUSDT")
            direction = trade_data.get("direction")
            try:
                size_usdt = float(trade_data.get("suggested_bet", 0))
                entry_price = float(trade_data.get("entry_price", 0))
            except Exception:
                size_usdt = 0.0
                entry_price = 0.0

            if not direction or size_usdt <= 0 or entry_price <= 0:
                structured_logger.error("Invalid trade parameters", extra={
                    "direction": direction,
                    "size_usdt": size_usdt,
                    "entry_price": entry_price
                })
                return False

            close_side = "SELL" if direction == "LONG" else "BUY"
            position_side = "LONG" if direction == "LONG" else "SHORT"

            # Use retry logger for better retry management
            retry_logger = get_retry_logger(structured_logger, f"exit_trade_{trade_id}")
            retry_key = f"trade_{trade_id}_exit"
            correlation_id = retry_logger.log_retry_start(retry_key, self.exit_max_retries)
            
            last_error_detail: Optional[str] = None

            for attempt in range(1, self.exit_max_retries + 1):
                try:
                    # Log attempt with appropriate level
                    retry_logger.log_attempt(retry_key, correlation_id)

                    # 1) 获取当前价格
                    try:
                        current_price = await binance_client.get_current_price()
                    except Exception as e:
                        last_error_detail = f"PRICE_FETCH_ERROR: {e}"
                        retry_logger.log_attempt(retry_key, correlation_id, str(e))
                        # 退避后重试
                        if attempt < self.exit_max_retries:
                            await asyncio.sleep(self.exit_retry_backoff_seconds * attempt)
                            continue
                        else:
                            break

                    if not current_price or current_price <= 0:
                        last_error_detail = "INVALID_PRICE"
                        error_msg = f"Invalid current price: {current_price}"
                        retry_logger.log_attempt(retry_key, correlation_id, error_msg)
                        if attempt < self.exit_max_retries:
                            await asyncio.sleep(self.exit_retry_backoff_seconds * attempt)
                            continue
                        else:
                            break

                    structured_logger.info("Executing market close order", extra={
                        "close_side": close_side,
                        "size_usdt": size_usdt,
                        "current_price": current_price,
                        "position_side": position_side
                    })

                    # 2) 调用交易所API平仓
                    try:
                        close_order_resp = await binance_client.place_market_order_futures(
                            symbol=symbol,
                            side=close_side,
                            notional_usdt=size_usdt,
                            position_side=position_side,
                            reduce_only=True,
                        )
                        structured_logger.info("Close order placed successfully", extra={
                            "order_response": close_order_resp
                        })
                    except Exception as e:
                        last_error_detail = f"ORDER_PLACEMENT_ERROR: {e}"
                        retry_logger.log_attempt(retry_key, correlation_id, str(e))
                        if attempt < self.exit_max_retries:
                            await asyncio.sleep(self.exit_retry_backoff_seconds * attempt)
                            continue
                        else:
                            break

                    # 3) 计算盈亏
                    if direction == "LONG":
                        pnl = (current_price - entry_price) * (size_usdt / entry_price)
                        pnl_pct = (current_price - entry_price) / entry_price
                    else:
                        pnl = (entry_price - current_price) * (size_usdt / entry_price)
                        pnl_pct = (entry_price - current_price) / entry_price

                    # 4) 更新数据库（注意：时间用ISO字符串）
                    exit_ts = datetime.utcnow().isoformat()
                    settlement_result = {
                        "trade_id": trade_id,
                        "symbol": symbol,
                        "direction": direction,
                        "entry_price": entry_price,
                        "suggested_bet": size_usdt,
                        "exit_price": current_price,
                        "exit_timestamp": exit_ts,
                        "pnl": pnl,
                        "pnl_percentage": pnl_pct,
                        "status": "WIN" if pnl > 0 else "LOSS",
                        "exit_reason": "KLINE_END_EXIT",
                        "settlement_method": "simple_exit_manager",
                        "close_order_response": close_order_resp,
                        "price_source": "binance_client.get_current_price",
                    }

                    try:
                        updated = db.update_trade_result(trade_id, settlement_result)
                        if not updated:
                            error_msg = "Database update returned False"
                            last_error_detail = "DB_UPDATE_FAILED"
                            retry_logger.log_attempt(retry_key, correlation_id, error_msg)
                            if attempt < self.exit_max_retries:
                                await asyncio.sleep(self.exit_retry_backoff_seconds * attempt)
                                continue
                            else:
                                break
                    except Exception as e:
                        last_error_detail = f"DB_UPDATE_EXCEPTION: {e}"
                        retry_logger.log_attempt(retry_key, correlation_id, str(e))
                        if attempt < self.exit_max_retries:
                            await asyncio.sleep(self.exit_retry_backoff_seconds * attempt)
                            continue
                        else:
                            break

                    # 5) 通知（成功后再通知）
                    try:
                        notification_data = {
                            **settlement_result,
                            "hold_time_minutes": (datetime.utcnow() - exit_info["signal_time"]).total_seconds() / 60,
                            "actual_settlement_time": exit_ts,
                            "original_signal_id": notification_manager.get_signal_kline_identifier(
                                trade_data.get("signal_timestamp", exit_ts)
                            ),
                        }
                        notification_manager.send_enhanced_settlement_notification(notification_data)
                        notification_manager.send_trade_execution(
                            {
                                "action": "CLOSE",
                                "symbol": symbol,
                                "direction": direction,
                                "size_usdt": size_usdt,
                                "price": current_price,
                                "reason": "KLINE_END_EXIT",
                                "trade_id": trade_id,
                                "timestamp": exit_ts,
                                "extra": {"attempt": attempt},
                            }
                        )
                    except Exception as e:
                        # 通知失败不影响结算成功
                        structured_logger.warning("Settlement notification failed", extra={"error": str(e)})

                    # Log successful completion
                    retry_logger.log_success(retry_key, correlation_id)
                    structured_logger.info("Trade settlement completed successfully", extra={
                        "pnl": pnl,
                        "pnl_percentage": pnl_pct,
                        "attempts_used": attempt,
                        "exit_price": current_price,
                        "status": "WIN" if pnl > 0 else "LOSS"
                    })
                    return True

                except Exception as e:
                    last_error_detail = f"UNEXPECTED_ERROR: {e}"
                    retry_logger.log_attempt(retry_key, correlation_id, str(e))
                    if attempt < self.exit_max_retries:
                        await asyncio.sleep(self.exit_retry_backoff_seconds * attempt)
                        continue
                    else:
                        break

            # Log final failure
            retry_logger.log_final_failure(retry_key, correlation_id)
            structured_logger.error("All exit attempts failed", extra={
                "attempts_made": self.exit_max_retries,
                "last_error": last_error_detail
            })
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        current_time = datetime.utcnow()
        
        status = {
            "is_running": self._check_task is not None and not self._check_task.done(),
            "pending_exits": len(self._pending_exits),
            "positions": []
        }
        
        for trade_id, exit_info in self._pending_exits.items():
            time_to_exit = (exit_info["planned_exit_time"] - current_time).total_seconds() / 60
            status["positions"].append({
                "trade_id": trade_id,
                "direction": exit_info["trade_data"].get("direction"),
                "entry_price": exit_info["trade_data"].get("entry_price"),
                "planned_exit_time": exit_info["planned_exit_time"].isoformat(),
                "time_to_exit_minutes": round(time_to_exit, 1),
                "status": exit_info["status"]
            })
        
        return status
    
    async def force_exit_all(self):
        """强制平仓所有持仓"""
        logger.warning("Force exiting all positions")
        
        tasks = []
        for trade_id, exit_info in list(self._pending_exits.items()):
            if exit_info["status"] == "PENDING":
                self._pending_exits[trade_id]["status"] = "PROCESSING"
                tasks.append(self._execute_exit(trade_id, exit_info))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Force exit failed: {result}")
            
            # 清理所有记录
            self._pending_exits.clear()
        
        logger.info("Force exit completed")

    def validate_exit_queue_integrity(self) -> bool:
        """验证退出队列的完整性"""
        issues = []

        for trade_id, exit_info in self._pending_exits.items():
            try:
                # 检查数据库中的状态
                with db.get_session() as session:
                    from quant.database_manager import TradeHistory
                    trade = session.query(TradeHistory).filter(TradeHistory.id == trade_id).first()

                    if not trade:
                        issues.append(f"Trade {trade_id} in queue but not in database")
                    elif trade.status != "PENDING":
                        issues.append(f"Trade {trade_id} in queue but status is {trade.status}")
                    else:
                        # 检查数据一致性
                        trade_data = exit_info.get("trade_data", {})
                        if abs(trade.entry_price - trade_data.get("entry_price", 0)) > 0.01:
                            issues.append(f"Trade {trade_id} price mismatch: DB={trade.entry_price}, Memory={trade_data.get('entry_price')}")

                        if trade.direction != trade_data.get("direction"):
                            issues.append(f"Trade {trade_id} direction mismatch: DB={trade.direction}, Memory={trade_data.get('direction')}")

            except Exception as e:
                issues.append(f"Error checking trade {trade_id}: {e}")

        if issues:
            logger.error(f"Exit queue integrity issues found: {issues}")
            return False

        logger.info(f"Exit queue integrity check passed for {len(self._pending_exits)} trades")
        return True

    def get_queue_status(self) -> dict[str, Any]:
        """获取队列状态信息"""
        current_time = datetime.utcnow()

        status = {
            "total_trades": len(self._pending_exits),
            "processing_locks": len(self._processing_locks),
            "locked_trades": list(self._processing_locks),
            "trades_by_status": {},
            "overdue_trades": [],
            "upcoming_exits": []
        }

        for trade_id, exit_info in self._pending_exits.items():
            trade_status = exit_info.get("status", "UNKNOWN")
            status["trades_by_status"][trade_status] = status["trades_by_status"].get(trade_status, 0) + 1

            planned_exit = exit_info.get("planned_exit_time")
            if planned_exit:
                time_to_exit = (planned_exit - current_time).total_seconds() / 60

                if time_to_exit < 0:
                    status["overdue_trades"].append({
                        "trade_id": trade_id,
                        "overdue_minutes": abs(time_to_exit)
                    })
                elif time_to_exit <= 5:
                    status["upcoming_exits"].append({
                        "trade_id": trade_id,
                        "minutes_to_exit": time_to_exit
                    })

        return status


# 全局实例
simple_exit_manager = SimpleExitManager()