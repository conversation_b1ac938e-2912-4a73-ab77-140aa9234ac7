"""
Limit Order Executor - 限价单执行管理器

负责限价单的下单、监控、撤销和重试逻辑
"""

from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, List
import asyncio
import math

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.utils.logger import get_logger
from quant.config_manager import config

logger = get_logger(__name__)


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "PENDING"          # 待下单
    PLACED = "PLACED"            # 已下单
    PARTIAL_FILLED = "PARTIAL"   # 部分成交
    FILLED = "FILLED"            # 完全成交
    CANCELLED = "CANCELLED"      # 已撤销
    EXPIRED = "EXPIRED"          # 已过期
    FAILED = "FAILED"            # 失败


class PriceStrategy(Enum):
    """价格策略枚举"""
    AGGRESSIVE = "AGGRESSIVE"    # 激进：更接近市场价
    BALANCED = "BALANCED"        # 平衡：中等偏移
    PASSIVE = "PASSIVE"          # 被动：较大偏移


@dataclass
class LimitOrderConfig:
    """限价单配置"""
    # 价格偏移策略
    price_strategy: PriceStrategy = PriceStrategy.BALANCED
    
    # 价格偏移量（基点，1bp = 0.01%）
    buy_offset_bps: float = 5.0    # 买单低于市价5bp
    sell_offset_bps: float = 5.0   # 卖单高于市价5bp
    
    # 订单超时配置（秒）
    initial_timeout: int = 30       # 首次下单超时
    retry_timeout: int = 20         # 重试订单超时
    
    # 最大重试次数
    max_retries: int = 3
    
    # 是否启用部分成交
    allow_partial_fill: bool = True
    
    # 紧急模式阈值（超过此时间使用市价单）
    emergency_threshold_seconds: int = 90
    
    # 订单簿深度使用
    use_orderbook: bool = True
    orderbook_level: int = 3        # 使用订单簿前N档
    
    # 滑点保护
    max_slippage_bps: float = 20.0  # 最大允许滑点20bp


@dataclass
class OrderContext:
    """订单上下文"""
    order_id: Optional[str] = None
    client_order_id: Optional[str] = None
    symbol: str = "BTCUSDT"
    side: str = "BUY"
    position_side: Optional[str] = None
    quantity: float = 0.0
    limit_price: float = 0.0
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    created_at: datetime = None
    updated_at: datetime = None
    retry_count: int = 0
    error_message: Optional[str] = None


class LimitOrderExecutor:
    """限价单执行器"""
    
    def __init__(self):
        # 加载配置
        maker_config = config.get("MAKER_ORDER", {}) or {}
        
        # 解析价格策略
        strategy_str = maker_config.get("price_strategy", "BALANCED")
        try:
            price_strategy = PriceStrategy(strategy_str)
        except ValueError:
            price_strategy = PriceStrategy.BALANCED
            
        self.config = LimitOrderConfig(
            price_strategy=price_strategy,
            buy_offset_bps=float(maker_config.get("buy_offset_bps", 5.0)),
            sell_offset_bps=float(maker_config.get("sell_offset_bps", 5.0)),
            initial_timeout=int(maker_config.get("initial_timeout", 30)),
            retry_timeout=int(maker_config.get("retry_timeout", 20)),
            max_retries=int(maker_config.get("max_retries", 3)),
            allow_partial_fill=bool(maker_config.get("allow_partial_fill", True)),
            emergency_threshold_seconds=int(maker_config.get("emergency_threshold", 90)),
            use_orderbook=bool(maker_config.get("use_orderbook", True)),
            orderbook_level=int(maker_config.get("orderbook_level", 3)),
            max_slippage_bps=float(maker_config.get("max_slippage_bps", 20.0))
        )
        
        # 活跃订单追踪
        self._active_orders: Dict[str, OrderContext] = {}
        
        # 监控任务
        self._monitor_task: Optional[asyncio.Task] = None
        
        # 性能统计
        self.metrics = {
            "total_limit_orders": 0,
            "filled_orders": 0,
            "market_fallback_count": 0,
            "total_fill_time": 0.0,
            "total_fees_saved": 0.0
        }
        
        logger.info(f"LimitOrderExecutor initialized with config: {self.config}")
    
    async def start(self):
        """启动限价单执行器"""
        if self._monitor_task and not self._monitor_task.done():
            logger.warning("Limit order executor already running")
            return
        
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("Limit order executor started")
    
    async def stop(self):
        """停止限价单执行器"""
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            self._monitor_task = None
        
        # 撤销所有活跃订单
        await self._cancel_all_active_orders()
        logger.info("Limit order executor stopped")
    
    async def execute_limit_order(
        self,
        symbol: str,
        side: str,
        quantity: float,
        position_side: Optional[str] = None,
        urgency: str = "NORMAL"
    ) -> OrderContext:
        """
        执行限价单
        
        Args:
            symbol: 交易对
            side: BUY/SELL
            quantity: 数量
            position_side: LONG/SHORT（合约）
            urgency: NORMAL/HIGH/EMERGENCY
        
        Returns:
            OrderContext: 订单上下文
        """
        # 创建订单上下文
        context = OrderContext(
            symbol=symbol,
            side=side,
            position_side=position_side,
            quantity=quantity,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.metrics["total_limit_orders"] += 1
        
        try:
            # 紧急模式直接使用市价单
            if urgency == "EMERGENCY":
                return await self._execute_market_order(context)
            
            # 计算限价
            limit_price = await self._calculate_limit_price(context)
            context.limit_price = limit_price
            
            # 下限价单
            await self._place_limit_order(context)
            
            # 加入监控
            if context.order_id:
                self._active_orders[context.order_id] = context
            
            # 等待成交或超时
            timeout = self.config.initial_timeout if urgency == "NORMAL" else self.config.initial_timeout // 2
            fill_start = datetime.utcnow()
            await self._wait_for_fill(context, timeout)
            
            # 处理结果
            if context.status == OrderStatus.FILLED:
                fill_time = (datetime.utcnow() - fill_start).total_seconds()
                self.metrics["filled_orders"] += 1
                self.metrics["total_fill_time"] += fill_time
                logger.info(f"Limit order filled in {fill_time:.1f}s: {context.order_id}")
                return context
            elif context.status == OrderStatus.PARTIAL_FILLED and self.config.allow_partial_fill:
                logger.info(f"Limit order partial filled: {context.order_id}")
                return context
            else:
                # 超时或失败，执行重试或降级
                return await self._handle_order_failure(context, urgency)
            
        except Exception as e:
            logger.error(f"Error executing limit order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            return context
    
    async def _calculate_limit_price(self, context: OrderContext) -> float:
        """
        计算限价单价格
        
        使用多种策略：
        1. 基于当前市价的偏移
        2. 基于订单簿的最优价格
        3. 基于近期成交价的VWAP
        """
        try:
            # 获取当前市价
            current_price = await binance_client.get_current_price(context.symbol)
            
            # 策略1：基础偏移
            if context.side == "BUY":
                offset_bps = self.config.buy_offset_bps
                base_price = current_price * (1 - offset_bps / 10000)
            else:
                offset_bps = self.config.sell_offset_bps
                base_price = current_price * (1 + offset_bps / 10000)
            
            # 策略2：订单簿优化
            if self.config.use_orderbook:
                orderbook_price = await self._get_orderbook_price(context)
                if orderbook_price:
                    # 加权平均
                    base_price = base_price * 0.7 + orderbook_price * 0.3
            
            # 策略3：根据价格策略调整
            if self.config.price_strategy == PriceStrategy.AGGRESSIVE:
                # 更接近市价
                base_price = current_price * 0.3 + base_price * 0.7
            elif self.config.price_strategy == PriceStrategy.PASSIVE:
                # 更远离市价
                if context.side == "BUY":
                    base_price *= 0.995
                else:
                    base_price *= 1.005
            
            # 价格精度处理（Binance要求）
            tick_size = 0.01  # BTCUSDT的价格精度
            base_price = math.floor(base_price / tick_size) * tick_size
            
            logger.info(f"Calculated limit price: {base_price} (current: {current_price}, side: {context.side})")
            return base_price
            
        except Exception as e:
            logger.error(f"Error calculating limit price: {e}")
            raise
    
    async def _get_orderbook_price(self, context: OrderContext) -> Optional[float]:
        """基于订单簿获取最优价格"""
        try:
            # 获取订单簿数据
            orderbook = await binance_client.get_orderbook(context.symbol, limit=10)
            
            if context.side == "BUY":
                # 买单看卖盘
                asks = orderbook.get("asks", [])
                if len(asks) >= self.config.orderbook_level:
                    # 使用第N档价格
                    return float(asks[self.config.orderbook_level - 1][0])
            else:
                # 卖单看买盘
                bids = orderbook.get("bids", [])
                if len(bids) >= self.config.orderbook_level:
                    return float(bids[self.config.orderbook_level - 1][0])
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting orderbook price: {e}")
            return None
    
    async def _place_limit_order(self, context: OrderContext) -> None:
        """下限价单到交易所"""
        try:
            # 生成客户端订单ID
            context.client_order_id = f"MAKER_{datetime.utcnow().strftime('%Y%m%d%H%M%S%f')}"
            
            # 构建订单参数 (不需要 'type' 参数，因为函数已经是 place_limit_order)
            order_params = {
                "symbol": context.symbol,
                "side": context.side,
                "quantity": context.quantity,
                "price": context.limit_price,
                "time_in_force": "GTC",  # Good Till Cancel
                "client_order_id": context.client_order_id
            }
            
            if context.position_side:
                order_params["position_side"] = context.position_side
            
            # 下单
            response = await binance_client.place_limit_order_futures(**order_params)
            
            if response and not response.get("error"):
                context.order_id = str(response.get("orderId"))
                context.status = OrderStatus.PLACED
                logger.info(f"Limit order placed: {context.order_id} at {context.limit_price}")
            else:
                raise Exception(f"Order placement failed: {response}")
            
        except Exception as e:
            logger.error(f"Error placing limit order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            raise
    
    async def _wait_for_fill(self, context: OrderContext, timeout_seconds: int) -> None:
        """等待订单成交"""
        start_time = datetime.utcnow()
        check_interval = 1  # 每秒检查一次
        
        while (datetime.utcnow() - start_time).total_seconds() < timeout_seconds:
            try:
                # 查询订单状态
                order_info = await binance_client.get_order(
                    symbol=context.symbol,
                    orderId=context.order_id
                )
                
                if order_info:
                    status = order_info.get("status")
                    
                    if status == "FILLED":
                        context.status = OrderStatus.FILLED
                        context.filled_quantity = float(order_info.get("executedQty", 0))
                        context.avg_fill_price = float(order_info.get("avgPrice", 0))
                        return
                    
                    elif status == "PARTIALLY_FILLED":
                        context.status = OrderStatus.PARTIAL_FILLED
                        context.filled_quantity = float(order_info.get("executedQty", 0))
                        
                        if self.config.allow_partial_fill:
                            # 部分成交也可以接受
                            if context.filled_quantity > 0:
                                context.avg_fill_price = float(order_info.get("avgPrice", 0))
                                return
                    
                    elif status in ["CANCELED", "EXPIRED", "REJECTED"]:
                        context.status = OrderStatus.CANCELLED
                        return
                
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"Error checking order status: {e}")
                await asyncio.sleep(check_interval)
        
        # 超时
        context.status = OrderStatus.EXPIRED
    
    async def _handle_order_failure(self, context: OrderContext, urgency: str) -> OrderContext:
        """处理订单失败（超时/拒绝）"""
        try:
            # 先尝试撤销原订单
            if context.order_id and context.status in [OrderStatus.PLACED, OrderStatus.PARTIAL_FILLED]:
                await self._cancel_order(context)
            
            # 检查是否达到重试上限
            if context.retry_count >= self.config.max_retries:
                logger.warning(f"Max retries reached, falling back to market order")
                self.metrics["market_fallback_count"] += 1
                return await self._execute_market_order(context)
            
            # 检查是否超过紧急阈值
            elapsed = (datetime.utcnow() - context.created_at).total_seconds()
            if elapsed > self.config.emergency_threshold_seconds:
                logger.warning(f"Emergency threshold reached, using market order")
                self.metrics["market_fallback_count"] += 1
                return await self._execute_market_order(context)
            
            # 重试逻辑
            context.retry_count += 1
            logger.info(f"Retrying limit order (attempt {context.retry_count})")
            
            # 调整价格策略（更激进）
            if context.side == "BUY":
                context.limit_price *= 1.002  # 提高买价
            else:
                context.limit_price *= 0.998  # 降低卖价
            
            # 重新下单
            await self._place_limit_order(context)
            
            if context.order_id:
                self._active_orders[context.order_id] = context
            
            # 等待成交（更短的超时）
            await self._wait_for_fill(context, self.config.retry_timeout)
            
            if context.status == OrderStatus.FILLED:
                self.metrics["filled_orders"] += 1
                return context
            else:
                # 递归重试
                return await self._handle_order_failure(context, urgency)
            
        except Exception as e:
            logger.error(f"Error handling order failure: {e}")
            # 最终兜底：市价单
            self.metrics["market_fallback_count"] += 1
            return await self._execute_market_order(context)
    
    async def _execute_market_order(self, context: OrderContext) -> OrderContext:
        """执行市价单（兜底方案）"""
        try:
            logger.warning(f"Falling back to market order for {context.symbol}")
            
            # 计算USDT名义价值
            current_price = await binance_client.get_current_price(context.symbol)
            notional_usdt = context.quantity * current_price
            
            # 执行市价单
            response = await binance_client.place_market_order_futures(
                symbol=context.symbol,
                side=context.side,
                notional_usdt=notional_usdt,
                position_side=context.position_side
            )
            
            if response and not response.get("error"):
                context.status = OrderStatus.FILLED
                context.filled_quantity = context.quantity
                context.avg_fill_price = current_price
                logger.info(f"Market order executed as fallback")
            else:
                context.status = OrderStatus.FAILED
                context.error_message = f"Market order failed: {response}"
            
            return context
            
        except Exception as e:
            logger.error(f"Error executing market order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            return context
    
    async def _cancel_order(self, context: OrderContext) -> bool:
        """撤销订单"""
        try:
            if not context.order_id:
                return False
            
            response = await binance_client.cancel_order(
                symbol=context.symbol,
                orderId=context.order_id
            )
            
            if response and response.get("status") == "CANCELED":
                context.status = OrderStatus.CANCELLED
                logger.info(f"Order cancelled: {context.order_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
    
    async def _cancel_all_active_orders(self) -> None:
        """撤销所有活跃订单"""
        for order_id, context in list(self._active_orders.items()):
            try:
                await self._cancel_order(context)
            except Exception as e:
                logger.error(f"Error cancelling order {order_id}: {e}")
        
        self._active_orders.clear()
    
    async def _monitor_loop(self) -> None:
        """监控循环"""
        logger.info("Starting limit order monitor loop")
        
        while True:
            try:
                await self._check_active_orders()
                await asyncio.sleep(5)  # 每5秒检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitor loop: {e}")
                await asyncio.sleep(5)
    
    async def _check_active_orders(self) -> None:
        """检查活跃订单状态"""
        for order_id, context in list(self._active_orders.items()):
            try:
                # 查询订单状态
                order_info = await binance_client.get_order(
                    symbol=context.symbol,
                    orderId=order_id
                )
                
                if order_info:
                    status = order_info.get("status")
                    
                    if status == "FILLED":
                        context.status = OrderStatus.FILLED
                        context.filled_quantity = float(order_info.get("executedQty", 0))
                        context.avg_fill_price = float(order_info.get("avgPrice", 0))
                        del self._active_orders[order_id]
                        self.metrics["filled_orders"] += 1
                        logger.info(f"Order {order_id} filled")
                    
                    elif status in ["CANCELED", "EXPIRED", "REJECTED"]:
                        context.status = OrderStatus.CANCELLED
                        del self._active_orders[order_id]
                        logger.info(f"Order {order_id} cancelled/expired")
                    
                    context.updated_at = datetime.utcnow()
                
            except Exception as e:
                logger.error(f"Error checking order {order_id}: {e}")
    
    def get_active_orders(self) -> List[OrderContext]:
        """获取所有活跃订单"""
        return list(self._active_orders.values())
    
    def get_order_status(self, order_id: str) -> Optional[OrderContext]:
        """获取订单状态"""
        return self._active_orders.get(order_id)
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        total = max(self.metrics["total_limit_orders"], 1)
        filled = self.metrics["filled_orders"]
        
        return {
            "total_limit_orders": total,
            "filled_orders": filled,
            "fill_rate": filled / total,
            "avg_fill_time": self.metrics["total_fill_time"] / max(filled, 1),
            "fees_saved": self.metrics["total_fees_saved"],
            "fallback_to_market": self.metrics["market_fallback_count"],
            "fallback_rate": self.metrics["market_fallback_count"] / total
        }
    
    def calculate_saved_fees(self, notional_usdt: float) -> float:
        """计算节省的手续费"""
        # Maker费率 0.02% (实际可能是0%)
        maker_fee = notional_usdt * 0.0002
        # Taker费率 0.04%
        taker_fee = notional_usdt * 0.0004
        # 节省的费用
        saved = taker_fee - maker_fee
        self.metrics["total_fees_saved"] += saved
        return round(saved, 4)


# 全局实例
limit_order_executor = LimitOrderExecutor()
