"""
Auto Trader Module

Listens to generated 30-minute signals and executes corresponding contract orders.
Implements 10-minute profit/loss handling with optional extension to the end of
the current 30-minute K-line (max extra 10 minutes), position sizing via
signal.suggested_bet, risk controls, and emergency stop.
"""

from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Optional
import json
import asyncio

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.notification_manager import notification_manager
from quant.utils.logger import get_logger
from quant.config_manager import config
from quant.strategies.limit_order_executor import LimitOrderExecutor, OrderStatus


logger = get_logger(__name__)


@dataclass
class ExecutionResult:
    success: bool
    trade_id: Optional[int]
    message: str
    details: dict[str, Any]


class AutoTrader:
    """Automatic order executor for generated signals."""

    def __init__(self):
        at_cfg = config.get("AUTO_TRADER", {}) or {}
        self.enabled: bool = at_cfg.get("enabled", True)
        self.emergency_stop: bool = at_cfg.get("emergency_stop", False)
        self.max_position_minutes: int = at_cfg.get("max_position_minutes", 30)
        # Import here to avoid circular dependency
        from quant.symbol_manager import symbol_manager
        self.symbol: str = at_cfg.get("symbol", symbol_manager.get_current_symbol())
        self.min_order_usdt: float = at_cfg.get("min_order_usdt", 5.0)
        self.max_order_usdt: float = at_cfg.get("max_order_usdt", 100.0)
        
        # Order mode configuration
        self.order_mode: str = at_cfg.get("order_mode", "MARKET")  # MARKET, LIMIT, or SMART
        self.use_limit_for_open: bool = at_cfg.get("use_limit_for_open", False)
        self.use_limit_for_close: bool = at_cfg.get("use_limit_for_close", False)
        self.urgency_threshold: float = at_cfg.get("urgency_threshold", 0.85)
        
        # Initialize limit order executor if needed
        self.limit_executor = None
        if self.order_mode in ["LIMIT", "SMART"] or self.use_limit_for_open or self.use_limit_for_close:
            self.limit_executor = LimitOrderExecutor()
            logger.info(f"LimitOrderExecutor initialized with order_mode={self.order_mode}, "
                       f"use_limit_for_open={self.use_limit_for_open}, "
                       f"use_limit_for_close={self.use_limit_for_close}")

    async def handle_new_signal(self, signal: dict[str, Any]) -> ExecutionResult:
        """Handle a freshly generated tradable signal and place an order.

        Uses signal['suggested_bet'] as notional size (USDT).
        """
        try:
            # 增加详细日志
            logger.info(f"AutoTrader received signal: direction={signal.get('direction')}, "
                       f"confidence={signal.get('confidence_score')}, "
                       f"suggested_bet={signal.get('suggested_bet')}")
            
            if not self.enabled or self.emergency_stop:
                msg = "AutoTrader disabled or emergency stop active; skipping order"
                logger.warning(msg)
                return ExecutionResult(False, None, msg, {})

            if signal.get("analysis_only") or signal.get("trading_suspended"):
                msg = f"Signal not tradable: analysis_only={signal.get('analysis_only')}, trading_suspended={signal.get('trading_suspended')}"
                logger.info(msg)
                return ExecutionResult(False, None, msg, {})

            symbol = signal.get("symbol", self.symbol)
            size_usdt = float(signal.get("suggested_bet", 0))
            if size_usdt <= 0:
                return ExecutionResult(False, None, "Zero size; skip", {})

            # Enforce size bounds
            original_size = size_usdt
            size_usdt = max(self.min_order_usdt, min(self.max_order_usdt, size_usdt))
            if size_usdt != original_size:
                logger.info(f"Adjusted position size from {original_size} to {size_usdt} USDT")

            # Fetch trade record to annotate execution - 增加重试机制
            trade = None
            retry_count = 3
            for i in range(retry_count):
                trade = db.get_trade_by_signal_timestamp(symbol, signal.get("signal_timestamp"))
                if trade:
                    break
                if i < retry_count - 1:
                    logger.warning(f"Trade record not found, retrying... ({i+1}/{retry_count})")
                    await asyncio.sleep(0.5)  # 等待0.5秒后重试
            
            if not trade:
                # 如果找不到trade记录，尝试创建一个临时记录以继续交易
                logger.error(f"Trade record not found after {retry_count} retries, creating temporary record")
                # 创建临时交易记录
                temp_trade_id = db.save_trade_signal(signal)
                trade = {"id": temp_trade_id}
                logger.info(f"Created temporary trade record with id={temp_trade_id}")

            # Determine execution price (use provided entry_price or current price)
            try:
                exec_price = float(signal.get("entry_price"))
            except Exception:
                exec_price = await binance_client.get_current_price()
                logger.info(f"Using current market price: ${exec_price:,.2f}")

            # Determine whether to use limit or market order
            side = "BUY" if signal.get("direction") == "LONG" else "SELL"
            position_side = "LONG" if signal.get("direction") == "LONG" else "SHORT"
            
            # Check if we should use limit orders for opening positions
            use_limit = False
            confidence = float(signal.get("confidence_score", 0.0))
            
            if self.limit_executor:
                if self.order_mode == "LIMIT":
                    use_limit = True
                elif self.order_mode == "SMART":
                    # Use limit order if confidence is below urgency threshold
                    use_limit = confidence < self.urgency_threshold
                elif self.use_limit_for_open:
                    use_limit = True
            
            logger.info(f"Placing {'LIMIT' if use_limit else 'MARKET'} order: {side} {size_usdt} USDT at price ${exec_price:,.2f} "
                       f"(confidence={confidence:.2f}, urgency_threshold={self.urgency_threshold})")
            
            try:
                if use_limit and self.limit_executor:
                    # Use limit order executor
                    logger.info(f"Using LimitOrderExecutor for trade {trade['id']}")
                    
                    # Calculate quantity from USDT notional
                    current_price = await binance_client.get_current_price(symbol)
                    quantity = size_usdt / current_price
                    
                    # Determine urgency level
                    if confidence >= self.urgency_threshold:
                        urgency = "HIGH"
                    else:
                        urgency = "NORMAL"
                    
                    order_context = await self.limit_executor.execute_limit_order(
                        symbol=symbol,
                        side=side,
                        quantity=quantity,
                        position_side=position_side,
                        urgency=urgency
                    )
                    
                    if order_context.status == OrderStatus.FILLED:
                        order_resp = {"status": "FILLED", "orderId": order_context.order_id}
                        exec_price = order_context.avg_fill_price or exec_price
                        logger.info(f"Limit order executed successfully for trade {trade['id']}: "
                                   f"{side} {size_usdt} USDT at avg price ${exec_price:,.2f}")
                    else:
                        # Fallback to market order if limit order failed
                        logger.warning(f"Limit order failed: status={order_context.status}, falling back to market order")
                        order_resp = await binance_client.place_market_order_futures(
                            symbol=symbol,
                            side=side,
                            notional_usdt=size_usdt,
                            position_side=position_side,
                            reduce_only=False,
                        )
                else:
                    # Use market order
                    order_resp = await binance_client.place_market_order_futures(
                        symbol=symbol,
                        side=side,
                        notional_usdt=size_usdt,
                        position_side=position_side,
                        reduce_only=False,
                    )
                    logger.info(f"Executed market order for trade {trade['id']}: {side} {size_usdt} USDT")
                
                # Use actual fill price if available
                if not order_resp.get("error") and order_resp.get("fills"):
                    # Calculate average fill price from fills
                    total_qty = sum(float(fill["qty"]) for fill in order_resp["fills"])
                    total_value = sum(float(fill["price"]) * float(fill["qty"]) for fill in order_resp["fills"])
                    if total_qty > 0:
                        exec_price = total_value / total_qty
                        logger.info(f"Used actual fill price for entry: ${exec_price:,.2f}")
                        
            except Exception as e:
                logger.error(f"Failed to execute open position order: {e}")
                order_resp = {"error": str(e), "simulated": True}

            execution_info = {
                "action": "OPEN",
                "symbol": symbol,
                "direction": signal.get("direction"),
                "size_usdt": round(size_usdt, 2),
                "price": exec_price,
                "timestamp": signal.get("signal_timestamp", datetime.utcnow().isoformat() + "Z"),
                "reason": "auto_trade_on_signal",
                "exchange_response": order_resp,
            }

            # Update DB decision_details with execution marker
            db.update_trade_decision_details(
                trade_id=trade["id"],
                updates={
                    "order_execution": execution_info,
                    "position_management": {
                        "max_position_minutes": self.max_position_minutes,
                        "extension": {"enabled": True, "extension_until": None},
                        "manual_override": False,
                    },
                },
            )

            # Notify execution
            notification_manager.send_trade_execution({
                **execution_info,
                "trade_id": trade["id"],
                "extra": {
                    "confidence": signal.get("confidence_score"),
                    "suggested_bet": signal.get("suggested_bet"),
                    "trigger": signal.get("trigger_pattern"),
                },
            })

            return ExecutionResult(True, trade["id"], "Order marked as opened", execution_info)

        except Exception as e:
            logger.error(f"Error handling new signal: {e}")
            return ExecutionResult(False, None, str(e), {})

    def decide_extension(self, trade: dict[str, Any]) -> dict[str, Any]:
        """Decide whether to extend a losing position at 10 minutes.

        Returns dict: {"extend": bool, "extension_until": iso_str | None, "reason": str}
        """
        try:
            # Load extension rules from config
            at_cfg = config.get("AUTO_TRADER", {}) or {}
            ext_rules = at_cfg.get("EXTENSION_RULES", {}) or {}
            adverse_stop = float(ext_rules.get("adverse_pct_stop", 0.008))  # 0.8%
            disable_on_high_vol = bool(ext_rules.get("disable_on_high_vol", False))
            high_vol_threshold = float(ext_rules.get("high_vol_threshold_pct", 0.015))  # 1.5%

            # Basic heuristic using stored decision details and a quick market check
            direction = trade.get("direction")
            entry_price = float(trade.get("entry_price", 0))
            current_price = trade.get("current_price")

            # Estimate current price via last known entry or a lightweight check
            # For robustness, do not make network call here; settlement will use real-time price
            # We inspect decision_details for recent price if present
            try:
                details = json.loads(trade.get("decision_details") or "{}")
            except Exception:
                details = {}

            last_known_price = None
            if isinstance(details, dict):
                last_known_price = (
                    details.get("current_price")
                    or details.get("entry_window", {}).get("last_close")
                )

            # If unavailable, assume adverse move persists
            adverse = False
            if last_known_price is not None:
                try:
                    last_known_price = float(last_known_price)
                    adverse = (
                        last_known_price < entry_price if direction == "LONG" else last_known_price > entry_price
                    )
                except Exception:
                    adverse = True
            else:
                adverse = True

            # Confidence-based hint
            confidence = float(trade.get("confidence_score", 0.0)) if "confidence_score" in trade else 0.0
            low_confidence = confidence < (config.get("RISK_MANAGEMENT", {}).get("confidence_threshold", 0.6))

            # Do not extend if this trade was a promotion-near-threshold low-size entry
            try:
                if isinstance(details, dict):
                    promotion = (details.get("entry_window", {}) or {}).get("promotion", {}) or {}
                    if promotion.get("applied", False):
                        return {"extend": False, "extension_until": None, "reason": "promotion_no_extension"}
            except Exception:
                pass

            # Price-based adverse stop threshold: do NOT extend if beyond threshold
            try:
                ref_price = None
                if isinstance(current_price, (int, float)) and current_price:
                    ref_price = float(current_price)
                elif last_known_price is not None:
                    ref_price = float(last_known_price)
                if ref_price and entry_price > 0:
                    adverse_pct = 0.0
                    if direction == "LONG":
                        adverse_pct = max(0.0, (entry_price - ref_price) / entry_price)
                    else:
                        adverse_pct = max(0.0, (ref_price - entry_price) / entry_price)
                    if adverse_pct >= adverse_stop:
                        return {"extend": False, "extension_until": None, "reason": f"adverse_pct_stop:{adverse_pct:.4f}"}
            except Exception:
                pass

            # High volatility disable: read vol_pct from decision_details.entry_window if available
            try:
                if disable_on_high_vol and isinstance(details, dict):
                    ew = details.get("entry_window", {}) or {}
                    vol_pct = float(ew.get("vol_pct", 0.0))
                    if vol_pct and vol_pct >= high_vol_threshold:
                        return {"extend": False, "extension_until": None, "reason": f"high_vol_disable:{vol_pct:.4f}"}
            except Exception:
                pass

            # Decide
            if low_confidence and adverse:
                return {"extend": False, "extension_until": None, "reason": "low_confidence_adverse_move_stop"}

            # Extend until current 30m K-line end, but not beyond 30 minutes total
            signal_ts = trade.get("signal_timestamp")
            if isinstance(signal_ts, str):
                signal_dt = datetime.fromisoformat(signal_ts.replace("Z", "+00:00"))
            else:
                signal_dt = signal_ts

            kline_end = self._get_current_kline_end(signal_dt)
            max_end = signal_dt + timedelta(minutes=self.max_position_minutes)
            # Hard cap: extension not exceeding +10 minutes over the 10-minute baseline (i.e., 20 minutes total)
            twenty_min_cap = signal_dt + timedelta(minutes=20)
            extension_until_dt = min(kline_end, max_end, twenty_min_cap)

            return {
                "extend": True,
                "extension_until": extension_until_dt.isoformat(),
                "reason": "extend_to_kline_end",
            }

        except Exception as e:
            logger.error(f"Error deciding extension: {e}")
            return {"extend": False, "extension_until": None, "reason": "decision_error"}

    def _get_current_kline_end(self, signal_time: datetime) -> datetime:
        """Get the end timestamp of the current 30m K-line for the given signal time."""
        minute = (signal_time.minute // 30) * 30
        start = signal_time.replace(minute=minute, second=0, microsecond=0)
        end = start + timedelta(minutes=30)
        return end

    def set_emergency_stop(self, enabled: bool):
        self.emergency_stop = enabled
        logger.warning(f"Emergency stop set to {enabled}")


# Global instance
auto_trader = AutoTrader()
