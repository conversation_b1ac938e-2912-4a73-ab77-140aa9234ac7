"""
OKX Limit Order Executor - OKX专用限价单执行器

Provides OKX-specific limit order execution with proper handling of
exchange-specific parameters and optimized maker order strategies.
"""

import asyncio
import math
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Dict, Any, Optional, List

from quant.exchange.okx_exchange import OkxExchange, OkxOrderAdapter
from quant.strategies.limit_order_executor import (
    LimitOrderExecutor,
    OrderContext,
    OrderStatus,
    PriceStrategy,
    LimitOrderConfig
)
from quant.utils.logger import get_logger
from quant.config_manager import config

logger = get_logger(__name__)


@dataclass
class OkxOrderContext(OrderContext):
    """Extended order context for OKX-specific fields"""
    inst_id: Optional[str] = None  # OKX instrument ID (e.g., BTC-USDT-SWAP)
    trade_mode: str = "isolated"   # isolated/cross/cash
    reduce_only: bool = False       # Whether this is a reduce-only order
    target_currency: str = "base_ccy"  # base_ccy or quote_ccy


class OkxLimitOrderExecutor(LimitOrderExecutor):
    """OKX-specific limit order executor with enhanced features"""
    
    def __init__(self):
        """Initialize OKX limit order executor"""
        super().__init__()
        
        # Initialize OKX exchange
        self.exchange = OkxExchange()
        self.order_adapter = OkxOrderAdapter()
        
        # Load OKX-specific configuration
        self._load_okx_config()
        
        # OKX-specific metrics
        self.okx_metrics = {
            "post_only_success": 0,
            "post_only_rejected": 0,
            "partial_fills": 0,
            "funding_fee_saved": 0.0
        }
        
        logger.info("OKX Limit Order Executor initialized")
    
    def _load_okx_config(self):
        """Load OKX-specific configuration"""
        okx_config = config.get("EXCHANGES", {}).get("okx", {})
        maker_config = okx_config.get("MAKER_ORDER", {})
        
        # Override base config with OKX-specific settings
        if maker_config:
            self.config.price_strategy = PriceStrategy(
                maker_config.get("price_strategy", "AGGRESSIVE")
            )
            self.config.buy_offset_bps = float(maker_config.get("buy_offset_bps", 8.0))
            self.config.sell_offset_bps = float(maker_config.get("sell_offset_bps", 8.0))
            self.config.initial_timeout = int(maker_config.get("initial_timeout", 25))
            self.config.retry_timeout = int(maker_config.get("retry_timeout", 15))
            self.config.max_retries = int(maker_config.get("max_retries", 3))
            
        # OKX-specific settings
        self.trade_mode = maker_config.get("trade_mode", "isolated")
        self.use_post_only = maker_config.get("use_post_only", True)
        self.reduce_only_on_close = maker_config.get("reduce_only_on_close", True)
        
        logger.info(f"OKX config loaded: trade_mode={self.trade_mode}, "
                   f"post_only={self.use_post_only}")
    
    async def execute_limit_order(
        self,
        symbol: str,
        side: str,
        quantity: float,
        position_side: Optional[str] = None,
        urgency: str = "NORMAL",
        reduce_only: bool = False
    ) -> OrderContext:
        """
        Execute a limit order on OKX exchange
        
        Args:
            symbol: Trading symbol (e.g., BTC/USDT)
            side: BUY or SELL
            quantity: Order quantity
            position_side: LONG or SHORT for futures
            urgency: NORMAL/HIGH/EMERGENCY
            reduce_only: Whether this is a reduce-only order
        
        Returns:
            OkxOrderContext with execution results
        """
        # Create OKX-specific order context
        context = OkxOrderContext(
            symbol=symbol,
            side=side,
            position_side=position_side,
            quantity=quantity,
            inst_id=self.order_adapter.convert_symbol(symbol),
            trade_mode=self.trade_mode,
            reduce_only=reduce_only,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.metrics["total_limit_orders"] += 1
        
        try:
            # Emergency mode uses market order
            if urgency == "EMERGENCY":
                return await self._execute_okx_market_order(context)
            
            # Calculate optimal limit price for OKX
            limit_price = await self._calculate_okx_limit_price(context)
            context.limit_price = limit_price
            
            # Place limit order on OKX
            await self._place_okx_limit_order(context)
            
            # Add to active monitoring
            if context.order_id:
                self._active_orders[context.order_id] = context
            
            # Wait for fill with OKX-specific timeout
            timeout = self._get_okx_timeout(urgency)
            fill_start = datetime.utcnow()
            await self._wait_for_okx_fill(context, timeout)
            
            # Handle result
            if context.status == OrderStatus.FILLED:
                fill_time = (datetime.utcnow() - fill_start).total_seconds()
                self.metrics["filled_orders"] += 1
                self.metrics["total_fill_time"] += fill_time
                self.okx_metrics["post_only_success"] += 1
                logger.info(f"OKX limit order filled in {fill_time:.1f}s: {context.order_id}")
                return context
                
            elif context.status == OrderStatus.PARTIAL_FILLED and self.config.allow_partial_fill:
                self.okx_metrics["partial_fills"] += 1
                logger.info(f"OKX limit order partial filled: {context.order_id}")
                return context
                
            else:
                # Handle timeout or rejection
                return await self._handle_okx_order_failure(context, urgency)
            
        except Exception as e:
            logger.error(f"Error executing OKX limit order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            return context
    
    async def _calculate_okx_limit_price(self, context: OkxOrderContext) -> float:
        """
        Calculate optimal limit price for OKX
        
        Uses OKX-specific orderbook and pricing strategies
        """
        try:
            # Get current price from OKX
            current_price = await self.exchange.get_current_price(context.symbol)
            
            if current_price == 0:
                raise ValueError(f"Could not get price for {context.symbol}")
            
            # Base offset calculation
            if context.side == "BUY":
                offset_bps = self.config.buy_offset_bps
                # For OKX, place buy orders slightly below market
                base_price = current_price * (1 - offset_bps / 10000)
            else:
                offset_bps = self.config.sell_offset_bps
                # For OKX, place sell orders slightly above market
                base_price = current_price * (1 + offset_bps / 10000)
            
            # Get OKX orderbook for fine-tuning
            if self.config.use_orderbook:
                orderbook_price = await self._get_okx_orderbook_price(context)
                if orderbook_price:
                    # Weighted average with orderbook
                    base_price = base_price * 0.6 + orderbook_price * 0.4
            
            # Apply OKX-specific price strategy
            if self.config.price_strategy == PriceStrategy.AGGRESSIVE:
                # More aggressive for OKX to ensure maker status
                if context.side == "BUY":
                    base_price = min(base_price, current_price * 0.9998)
                else:
                    base_price = max(base_price, current_price * 1.0002)
            
            # Price precision for OKX (get from symbol config)
            symbol_config = config.get("EXCHANGES", {}).get("okx", {}).get("symbols", {}).get(context.symbol.replace("/", ""), {})
            price_precision = symbol_config.get("price_precision", 0.1)
            
            # Round to OKX precision
            base_price = math.floor(base_price / price_precision) * price_precision
            
            logger.info(f"OKX limit price calculated: {base_price} "
                       f"(current: {current_price}, side: {context.side})")
            
            return base_price
            
        except Exception as e:
            logger.error(f"Error calculating OKX limit price: {e}")
            raise
    
    async def _get_okx_orderbook_price(self, context: OkxOrderContext) -> Optional[float]:
        """Get optimal price from OKX orderbook"""
        try:
            orderbook = await self.exchange.get_orderbook(context.symbol)
            
            if not orderbook or "data" not in orderbook:
                return None
            
            data = orderbook["data"][0]
            
            if context.side == "BUY":
                # For buy orders, look at asks (sell side)
                asks = data.get("asks", [])
                if len(asks) >= self.config.orderbook_level:
                    # Use price between best ask and level N
                    best_ask = float(asks[0][0])
                    level_ask = float(asks[self.config.orderbook_level - 1][0])
                    return (best_ask + level_ask) / 2
            else:
                # For sell orders, look at bids (buy side)
                bids = data.get("bids", [])
                if len(bids) >= self.config.orderbook_level:
                    # Use price between best bid and level N
                    best_bid = float(bids[0][0])
                    level_bid = float(bids[self.config.orderbook_level - 1][0])
                    return (best_bid + level_bid) / 2
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting OKX orderbook price: {e}")
            return None
    
    async def _place_okx_limit_order(self, context: OkxOrderContext) -> None:
        """Place limit order on OKX exchange"""
        try:
            # Generate client order ID
            context.client_order_id = f"OKX_MAKER_{datetime.utcnow().strftime('%Y%m%d%H%M%S%f')}"
            
            # Use OKX exchange to place order
            response = await self.exchange.place_limit_order(context)
            
            if response and response.get("success"):
                context.order_id = response.get("order_id")
                context.status = OrderStatus.PLACED
                logger.info(f"OKX limit order placed: {context.order_id} at {context.limit_price}")
            else:
                error_msg = response.get("error", "Unknown error")
                
                # Check if rejected due to post-only
                if "post-only" in error_msg.lower():
                    self.okx_metrics["post_only_rejected"] += 1
                    logger.warning(f"OKX post-only order rejected, will retry with adjusted price")
                    # Adjust price and retry
                    await self._adjust_price_for_post_only(context)
                    return await self._place_okx_limit_order(context)
                
                raise Exception(f"OKX order placement failed: {error_msg}")
            
        except Exception as e:
            logger.error(f"Error placing OKX limit order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            raise
    
    async def _adjust_price_for_post_only(self, context: OkxOrderContext) -> None:
        """Adjust price to ensure post-only order acceptance"""
        adjustment = 0.0001  # 1 basis point adjustment
        
        if context.side == "BUY":
            # Lower buy price to ensure maker
            context.limit_price *= (1 - adjustment)
        else:
            # Raise sell price to ensure maker
            context.limit_price *= (1 + adjustment)
        
        logger.info(f"Adjusted OKX price for post-only: {context.limit_price}")
    
    async def _wait_for_okx_fill(self, context: OkxOrderContext, timeout_seconds: int) -> None:
        """Wait for OKX order to fill with enhanced monitoring"""
        start_time = datetime.utcnow()
        check_interval = 0.5  # Faster checking for OKX
        
        while (datetime.utcnow() - start_time).total_seconds() < timeout_seconds:
            try:
                # Query order status from OKX
                order_info = await self.exchange.get_order(
                    order_id=context.order_id,
                    symbol=context.symbol
                )
                
                if order_info and "data" in order_info:
                    data = order_info["data"][0] if order_info["data"] else {}
                    status = data.get("state")
                    
                    if status == "filled":
                        context.status = OrderStatus.FILLED
                        context.filled_quantity = float(data.get("accFillSz", 0))
                        context.avg_fill_price = float(data.get("avgPx", 0))
                        
                        # Calculate saved fees (maker vs taker)
                        self._calculate_okx_fee_savings(context)
                        return
                    
                    elif status == "partially_filled":
                        context.status = OrderStatus.PARTIAL_FILLED
                        context.filled_quantity = float(data.get("accFillSz", 0))
                        
                        if self.config.allow_partial_fill and context.filled_quantity > 0:
                            context.avg_fill_price = float(data.get("avgPx", 0))
                            return
                    
                    elif status in ["canceled", "live_canceled"]:
                        context.status = OrderStatus.CANCELLED
                        return
                
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"Error checking OKX order status: {e}")
                await asyncio.sleep(check_interval)
        
        # Timeout
        context.status = OrderStatus.EXPIRED
    
    async def _handle_okx_order_failure(
        self,
        context: OkxOrderContext,
        urgency: str
    ) -> OkxOrderContext:
        """Handle OKX order failure with exchange-specific logic"""
        try:
            # Cancel the failed order if still open
            if context.order_id and context.status in [OrderStatus.PLACED, OrderStatus.PARTIAL_FILLED]:
                await self.exchange.cancel_order(context.order_id, context.symbol)
            
            # Check retry limits
            if context.retry_count >= self.config.max_retries:
                logger.warning(f"Max retries reached for OKX order, using market order")
                self.metrics["market_fallback_count"] += 1
                return await self._execute_okx_market_order(context)
            
            # Check emergency threshold
            elapsed = (datetime.utcnow() - context.created_at).total_seconds()
            if elapsed > self.config.emergency_threshold_seconds:
                logger.warning(f"Emergency threshold reached for OKX order")
                self.metrics["market_fallback_count"] += 1
                return await self._execute_okx_market_order(context)
            
            # Retry with adjusted parameters
            context.retry_count += 1
            logger.info(f"Retrying OKX limit order (attempt {context.retry_count})")
            
            # More aggressive price adjustment for OKX
            if context.side == "BUY":
                context.limit_price *= 1.003  # Increase buy price more
            else:
                context.limit_price *= 0.997  # Decrease sell price more
            
            # Re-place order
            await self._place_okx_limit_order(context)
            
            if context.order_id:
                self._active_orders[context.order_id] = context
            
            # Wait with shorter timeout
            await self._wait_for_okx_fill(context, self.config.retry_timeout)
            
            if context.status == OrderStatus.FILLED:
                self.metrics["filled_orders"] += 1
                return context
            else:
                # Recursive retry
                return await self._handle_okx_order_failure(context, urgency)
            
        except Exception as e:
            logger.error(f"Error handling OKX order failure: {e}")
            self.metrics["market_fallback_count"] += 1
            return await self._execute_okx_market_order(context)
    
    async def _execute_okx_market_order(self, context: OkxOrderContext) -> OkxOrderContext:
        """Execute market order on OKX as fallback"""
        try:
            logger.warning(f"Executing OKX market order as fallback for {context.symbol}")
            
            # Use OKX exchange for market order
            response = await self.exchange.place_market_order(context)
            
            if response and response.get("success"):
                context.status = OrderStatus.FILLED
                context.filled_quantity = context.quantity
                # Get actual fill price from response if available
                context.avg_fill_price = response.get("avg_price", 
                                                      await self.exchange.get_current_price(context.symbol))
                logger.info(f"OKX market order executed successfully")
            else:
                context.status = OrderStatus.FAILED
                context.error_message = f"Market order failed: {response.get('error')}"
            
            return context
            
        except Exception as e:
            logger.error(f"Error executing OKX market order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            return context
    
    def _get_okx_timeout(self, urgency: str) -> int:
        """Get timeout based on urgency and OKX settings"""
        if urgency == "HIGH":
            return self.config.initial_timeout // 2
        elif urgency == "EMERGENCY":
            return 5  # Very short timeout for emergency
        else:
            return self.config.initial_timeout
    
    def _calculate_okx_fee_savings(self, context: OkxOrderContext) -> None:
        """Calculate fee savings from maker order on OKX"""
        if context.filled_quantity > 0 and context.avg_fill_price > 0:
            notional = context.filled_quantity * context.avg_fill_price
            
            # OKX fee structure (example rates)
            maker_fee_rate = 0.0002  # 0.02%
            taker_fee_rate = 0.0005  # 0.05%
            
            maker_fee = notional * maker_fee_rate
            taker_fee = notional * taker_fee_rate
            saved = taker_fee - maker_fee
            
            self.metrics["total_fees_saved"] += saved
            self.okx_metrics["funding_fee_saved"] += saved * 0.1  # Estimate funding benefit
            
            logger.info(f"OKX fee saved: {saved:.4f} USDT on {notional:.2f} USDT notional")
    
    def get_okx_metrics(self) -> Dict[str, Any]:
        """Get OKX-specific metrics"""
        base_metrics = self.get_metrics()
        
        # Add OKX-specific metrics
        base_metrics.update({
            "okx_post_only_success": self.okx_metrics["post_only_success"],
            "okx_post_only_rejected": self.okx_metrics["post_only_rejected"],
            "okx_partial_fills": self.okx_metrics["partial_fills"],
            "okx_funding_saved": self.okx_metrics["funding_fee_saved"],
            "okx_post_only_success_rate": (
                self.okx_metrics["post_only_success"] / 
                max(self.okx_metrics["post_only_success"] + self.okx_metrics["post_only_rejected"], 1)
            )
        })
        
        return base_metrics
    
    async def cancel_all_okx_orders(self, symbol: Optional[str] = None) -> None:
        """Cancel all active OKX orders"""
        logger.info(f"Cancelling all OKX orders{f' for {symbol}' if symbol else ''}")
        
        for order_id, context in list(self._active_orders.items()):
            if symbol and context.symbol != symbol:
                continue
            
            try:
                success = await self.exchange.cancel_order(order_id, context.symbol)
                if success:
                    logger.info(f"Cancelled OKX order {order_id}")
                    del self._active_orders[order_id]
            except Exception as e:
                logger.error(f"Error cancelling OKX order {order_id}: {e}")
    
    async def get_okx_positions(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current positions from OKX"""
        try:
            return await self.exchange.get_position(symbol)
        except Exception as e:
            logger.error(f"Error getting OKX positions: {e}")
            return None


# Global instance for OKX
# okx_limit_order_executor = OkxLimitOrderExecutor()  # Commented out to avoid initialization issues during testing
