#!/usr/bin/env python3
"""
Trading Signal System - Main Application Entry Point

Binance BTC/USDT Event Contract Trading Signal Decision System
Author: Trading System Team
Date: 2025-08-03
"""

import asyncio
import signal
import sys
from datetime import datetime
from pathlib import Path
import time
from dataclasses import asdict

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# 提前过滤 APScheduler 引入时的 pkg_resources 弃用告警
import warnings
warnings.filterwarnings(
    "ignore",
    message=r"pkg_resources is deprecated as an API.*",
    category=UserWarning,
    module=r"apscheduler(\.|$)"
)

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.error_handler import error_handler
from quant.notification_manager import notification_manager
from quant.real_time_data_manager import real_time_data_manager
from quant.recovery_manager import recovery_manager, RecoveryPriority
from quant.risk_manager import risk_manager
from quant.settlement_checker import settlement_checker
from quant.strategies.auto_trader import auto_trader
from quant.strategies.simple_exit_manager import simple_exit_manager

# Import our modules
from quant.simple_analysis_engine import analysis_engine
from quant.system_monitor import system_monitor
from quant.trading_alert_manager import trading_alert_manager
from quant.advanced_analytics import advanced_analytics
from quant.performance_optimizer import performance_optimizer
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class TradingSystem:
    """Main trading system application."""

    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.running = False
        self.shutdown_event = asyncio.Event()

        # Configuration
        self.analysis_interval = 30  # minutes
        self.settlement_interval = 5  # minutes
        self.reconciliation_interval = 60  # minutes

    async def initialize(self):
        """Initialize the trading system."""
        try:
            logger.info("Initializing Trading Signal System...")

            # Initialize database
            db.init_database()
            logger.info("Database initialized")

            # Initialize Binance client
            await binance_client.initialize()
            logger.info("Binance client initialized")

            # Initialize real-time data manager
            await real_time_data_manager.initialize()
            logger.info("Real-time data manager initialized")

            # Initialize recovery manager
            await recovery_manager.start()
            logger.info("Recovery manager initialized")
            
            # 启动简化的平仓管理器
            await simple_exit_manager.start()
            logger.info("Simple exit manager initialized")

            # Setup error handling
            error_handler.setup_global_exception_handler()
            error_handler.add_shutdown_hook(self._cleanup)

            # Register signal handlers
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)

            logger.info("Trading system initialization completed")

        except Exception as e:
            logger.error(f"Failed to initialize trading system: {e}")
            raise

    def setup_scheduler(self):
        """Set up the task scheduler."""
        logger.info("Setting up task scheduler...")

        # Real-time data stream management task (every minute)
        self.scheduler.add_job(
            self.data_stream_management_task,
            trigger=IntervalTrigger(minutes=1),
            id="data_stream_management",
            name="Data Stream Management",
            replace_existing=True,
        )

        # 30-minute market analysis task
        try:
            from quant.config_manager import config
            sched_cfg = config.get("SCHEDULING", {}) or {}
            minutes = sched_cfg.get("analysis_minutes", [9, 39])
            tz = sched_cfg.get("timezone", "Asia/Shanghai")
            # 添加两条定时规则，分别在每小时的指定分钟触发
            for idx, m in enumerate(minutes):
                self.scheduler.add_job(
                    self.market_analysis_and_trade_task,
                    trigger=CronTrigger(minute=int(m), second=0, timezone=tz),
                    id=f"market_analysis_{idx}",
                    name=f"Market Analysis {m}",
                    replace_existing=True,
                )
            logger.info(f"Market analysis scheduled at minutes {minutes} in {tz}")
        except Exception as e:
            logger.warning(f"Cron scheduling failed, fallback to interval: {e}")
            self.scheduler.add_job(
                self.market_analysis_and_trade_task,
                trigger=IntervalTrigger(minutes=self.analysis_interval),
                id="market_analysis",
                name="Market Analysis",
                replace_existing=True,
            )

        # 优化结算检查频率：从5分钟改为30秒，确保10分钟窗口精准触发
        self.scheduler.add_job(
            self.settlement_check_task,
            trigger=IntervalTrigger(seconds=30),  # 30秒一次，减少触发抖动
            id="settlement_check",
            name="Settlement Check",
            replace_existing=True,
        )

        # 60-minute reconciliation task
        self.scheduler.add_job(
            self.reconciliation_task,
            trigger=IntervalTrigger(minutes=self.reconciliation_interval),
            id="reconciliation",
            name="Reconciliation",
            replace_existing=True,
        )

        # System metrics monitoring task (every 5 minutes)
        self.scheduler.add_job(
            self.system_metrics_task,
            trigger=IntervalTrigger(minutes=5),
            id="system_metrics",
            name="System Metrics",
            replace_existing=True,
        )

        # Comprehensive health monitoring task (every 15 minutes)
        self.scheduler.add_job(
            self.health_monitoring_task,
            trigger=IntervalTrigger(minutes=15),
            id="health_monitoring",
            name="Health Monitoring",
            replace_existing=True,
        )

        # Daily report task (at midnight)
        self.scheduler.add_job(
            self.daily_report_task,
            trigger="cron",
            hour=0,
            minute=0,
            id="daily_report",
            name="Daily Report",
            replace_existing=True,
        )

        # Risk monitoring task (every 15 minutes)
        self.scheduler.add_job(
            self.risk_monitoring_task,
            trigger=IntervalTrigger(minutes=15),
            id="risk_monitoring",
            name="Risk Monitoring",
            replace_existing=True,
        )

        # Process monitoring task (every 30 seconds)
        self.scheduler.add_job(
            self.process_monitoring_task,
            trigger=IntervalTrigger(seconds=30),
            id="process_monitoring",
            name="Process Monitoring",
            replace_existing=True,
        )

        # Comprehensive performance reporting task (every 6 hours)
        self.scheduler.add_job(
            self.comprehensive_performance_report_task,
            trigger=IntervalTrigger(hours=6),
            id="performance_report",
            name="Performance Report",
            replace_existing=True,
        )

        # Weekly extension decision stats report (every Monday 09:00 CST)
        try:
            self.scheduler.add_job(
                self.weekly_extension_stats_task,
                trigger=CronTrigger(day_of_week='mon', hour=9, minute=0, timezone='Asia/Shanghai'),
                id="weekly_extension_stats",
                name="Weekly Extension Stats",
                replace_existing=True,
            )
        except Exception as e:
            logger.warning(f"Failed to schedule weekly extension stats: {e}")

        # 添加结算积压监控任务（每2分钟检查一次）
        self.scheduler.add_job(
            self.settlement_backlog_check_task,
            trigger=IntervalTrigger(minutes=2),
            id="settlement_backlog_check",
            name="Settlement Backlog Check",
            replace_existing=True,
        )

        logger.info("Task scheduler configured")

    async def data_stream_management_task(self):
        """Manage real-time data streams."""
        try:
            logger.debug("Starting data stream management task...")

            # Check and start data streams if needed
            # Import here to avoid circular dependency
            from quant.symbol_manager import symbol_manager
            symbol = symbol_manager.get_current_symbol()

            # Start K-line streams for multiple timeframes
            intervals = ["1m", "5m", "15m", "30m"]
            for interval in intervals:
                try:
                    await real_time_data_manager.start_kline_stream(symbol, interval)
                except Exception as e:
                    logger.error(f"Error starting {interval} stream: {e}")

            # Log stream status
            status = real_time_data_manager.get_stream_status()
            logger.debug(f"Data stream status: {status}")

            logger.debug("Data stream management task completed")

        except Exception as e:
            logger.error(f"Error in data stream management task: {e}")

    async def market_analysis_task(self):
        """DEPRECATED: kept for compatibility; use market_analysis_and_trade_task instead."""
        await self.market_analysis_and_trade_task()

    async def market_analysis_and_trade_task(self):
        """Execute market analysis, generate signals, and auto-execute orders."""
        try:
            start_time = time.time()
            logger.info("=" * 60)
            logger.info("Starting 30-minute market analysis and trading task...")
            
            # 1. Market analysis and signal generation
            signal = await analysis_engine.analyze_market()
            
            if signal:
                # Calculate position size
                position_sizing = risk_manager.calculate_position_size(signal)
                signal["suggested_bet"] = position_sizing.position_size_usdt
                signal["risk_level"] = position_sizing.risk_level.value
                signal["risk_notes"] = position_sizing.notes
                
                # Check if trading should be suspended
                if position_sizing.position_size_usdt == 0:
                    signal["trading_suspended"] = True
                    signal["suspension_reason"] = "Risk management blocked trade"
                    logger.warning(f"Trading suspended: {'; '.join(position_sizing.notes)}")
                
                # Store signal with additional metadata
                signal["original_signal_id"] = f"{signal.get('signal_timestamp', '')}_{signal.get('direction', '')}"
                signal["analysis_latency_ms"] = int((time.time() - start_time) * 1000)
                
                # Save to database (only save if not analysis_only)
                if not signal.get("analysis_only"):
                    trade_id = db.save_trade_signal(signal)
                    signal["trade_id"] = trade_id
                    # 记录交易信号日志
                    from quant.utils.logger import trade_logger
                    trade_logger.log_signal(signal)
                    logger.info(f"Tradable signal saved to database with ID: {trade_id}")
                else:
                    logger.info("Analysis-only signal generated (not persisted to database)")
                
                # Add current price info for notification
                current_price = await binance_client.get_current_price()
                if current_price:
                    signal["current_price"] = current_price
                    signal["price_change"] = current_price - signal["entry_price"]
                    signal["price_change_pct"] = (signal["price_change"] / signal["entry_price"]) * 100
                
                # Log signal data before sending
                logger.info(f"Signal data before notification: {signal}")
                
                # Send enhanced notification
                notification_start = time.time()
                notification_manager.send_enhanced_signal_notification(signal)
                notification_latency = time.time() - notification_start
                
                if signal.get("trading_suspended"):
                    logger.info(
                        f"Signal generated (ANALYSIS ONLY): {signal['direction']} - Trading suspended: {signal['suspension_reason']}"
                    )
                else:
                    logger.info(
                        f"Signal generated and sent: {signal['direction']} (Size: ${signal.get('suggested_bet', 'N/A')})"
                    )
                    # Log risk assessment
                    if signal.get("risk_notes"):
                        logger.info(f"Risk assessment: {'; '.join(signal['risk_notes'])}")

                    # Auto-trading: execute order for tradable signals
                    try:
                        exec_result = await auto_trader.handle_new_signal(signal)
                        if exec_result.success:
                            logger.info(f"Auto trade executed: trade_id={exec_result.trade_id}")
                            
                            # 使用简化的平仓管理器
                            trade_data = {
                                "id": exec_result.trade_id,
                                "entry_price": signal["entry_price"],
                                "direction": signal["direction"],
                                # Import here to avoid circular dependency
                                from quant.symbol_manager import symbol_manager
                                "symbol": signal.get("symbol", symbol_manager.get_current_symbol()),
                                "suggested_bet": signal.get("suggested_bet", 0),
                                "signal_timestamp": signal.get("signal_timestamp")
                            }
                            
                            # 添加到简化平仓管理器
                            simple_exit_manager.add_position(exec_result.trade_id, trade_data)
                            logger.info(f"Position added to simple exit manager for automatic close")
                        else:
                            logger.warning(f"Auto trade skipped: {exec_result.message}")
                    except Exception as e:
                        logger.error(f"Auto trading error: {e}")
                
                # Check for trading alerts
                total_latency = time.time() - start_time
                trading_metrics = {
                    "signal_generation_time": total_latency,
                    "notification_latency": notification_latency,
                    "last_data_update": time.time(),  # Should be updated by data manager
                    "websocket_status": real_time_data_manager.get_stream_status(),
                    "settlement_status": {"pending_settlements": len(db.get_pending_trades())}
                }
                
                # Check and handle trading alerts
                alerts = trading_alert_manager.check_trading_alerts(trading_metrics)
                for alert in alerts:
                    if alert is None:
                        continue
                    # Send notification for critical trading alerts
                    if alert.severity == "trading_critical":
                        notification_manager.send_system_health_alert({
                            "alert_type": "TRADING_CRITICAL",
                            "health_report": {"trading_alert": asdict(alert)},
                            "message": alert.message
                        })
            else:
                logger.info("No signal generated in this analysis cycle")

            logger.info("Market analysis task completed")

        except Exception as e:
            logger.error(f"Error in market analysis task: {e}")
            # Send error notification
            error_data = {
                "error_type": "AnalysisError",
                "error_message": str(e),
                "task": "market_analysis",
            }
            notification_manager.send_error_notification(error_data)

    async def _get_market_data(self) -> dict[str, any]:
        """Get current market data for risk calculations."""
        try:
            # Get current price and basic market data
            current_price = await binance_client.get_current_price()
            logger.info(f"Current price from Binance: ${current_price:,.2f}")

            # Calculate volatility using recent price data
            klines = await binance_client.get_klines(interval="1m", limit=20)

            if klines and len(klines) >= 10:
                prices = [float(kline[4]) for kline in klines[-10:]]  # Close prices
                returns = [
                    (prices[i] - prices[i - 1]) / prices[i - 1]
                    for i in range(1, len(prices))
                ]
                # Fix volatility calculation: use standard deviation, not mean
                if returns:
                    mean_return = sum(returns) / len(returns)
                    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
                    volatility = (variance ** 0.5) * (60 ** 0.5)  # Annualized to hourly
                    # Ensure minimum volatility for trading
                    volatility = max(volatility, 0.05)  # Minimum 5% volatility
                else:
                    volatility = 0.1  # Default volatility
            else:
                volatility = 0.1  # Default volatility

            market_data = {
                "current_price": current_price,
                "volatility": volatility,
                "timestamp": datetime.now().isoformat(),
                "market_cap": "N/A",
                "volume_24h": "N/A"
            }
            
            logger.info(f"Market data prepared: {market_data}")
            return market_data
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return {
                "current_price": 50000.0,
                "volatility": 0.2,
                "timestamp": datetime.now().isoformat(),
                "market_cap": "N/A",
                "volume_24h": "N/A"
            }

    async def settlement_check_task(self):
        """Check and settle pending trades with optimized concurrency."""
        try:
            logger.info("Starting optimized settlement check task...")

            # 使用并发结算处理，提高处理效率
            settled_trades = await settlement_checker.check_pending_settlements_concurrent(
                max_concurrent=20  # 提升并发以快速消化积压
            )

            if settled_trades:
                logger.info(f"Settled {len(settled_trades)} trades concurrently")

                # Update risk manager with trade results
                for trade in settled_trades:
                    if trade.get("pnl") is not None:
                        risk_manager.update_trade_result(
                            trade["pnl"],
                            datetime.fromisoformat(trade["exit_timestamp"]).date(),
                        )
                        
                # 记录结算处理的K线序号信息
                for trade in settled_trades:
                    signal_id = trade.get("original_signal_id", "Unknown")
                    logger.info(f"Processed settlement for signal {signal_id}")
            else:
                logger.debug("No trades to settle")

            logger.info("Optimized settlement check task completed")

        except Exception as e:
            logger.error(f"Error in optimized settlement check task: {e}")
            # Send error notification
            error_data = {
                "error_type": "SettlementError",
                "error_message": str(e),
                "task": "settlement_check",
            }
            notification_manager.send_error_notification(error_data)

    async def reconciliation_task(self):
        """Run reconciliation task."""
        try:
            logger.info("Starting reconciliation task...")

            await settlement_checker.reconciliation_task()

            logger.info("Reconciliation task completed")

        except Exception as e:
            logger.error(f"Error in reconciliation task: {e}")

    async def system_metrics_task(self):
        """Monitor system metrics and performance."""
        try:
            logger.debug("Starting system metrics task...")

            # Get real-time data manager metrics
            metrics = real_time_data_manager.get_metrics()

            # Log key metrics
            logger.info(
                f"System Metrics - "
                f"Messages: {metrics['messages_processed']} processed, "
                f"{metrics['messages_failed']} failed, "
                f"Storage ops: {metrics['storage_operations']}, "
                f"Active streams: {metrics['active_streams']}, "
                f"Buffer size: {metrics['buffer_size']}, "
                f"Uptime: {metrics['uptime_seconds']:.1f}s"
            )

            # Alert on potential issues
            if metrics["messages_failed"] > 0:
                failure_rate = metrics["messages_failed"] / max(
                    metrics["messages_processed"], 1
                )
                if failure_rate > 0.05:  # 5% failure rate threshold
                    logger.warning(f"High message failure rate: {failure_rate:.2%}")

            if metrics["buffer_size"] > 500:
                logger.warning(f"Large buffer size: {metrics['buffer_size']} items")

            logger.debug("System metrics task completed")

        except Exception as e:
            logger.error(f"Error in system metrics task: {e}")

    async def daily_report_task(self):
        """Generate and send daily report."""
        try:
            logger.info("Starting daily report task...")

            report = await settlement_checker.run_daily_settlement_report()

            # Send daily report notification
            if report:
                message = f"""
### 📊 每日交易报告

**日期**: {report['date']}
**总交易数**: {report['total_trades']}
**盈利交易**: {report['winning_trades']}
**亏损交易**: {report['losing_trades']}
**胜率**: {report['win_rate']}%

报告生成时间: {report['generated_at']}
"""
                # This would be sent to DingTalk in production
                logger.info(f"Daily report: {message}")

            logger.info("Daily report task completed")

        except Exception as e:
            logger.error(f"Error in daily report task: {e}")

    async def risk_monitoring_task(self):
        """Monitor risk metrics and send alerts."""
        try:
            logger.debug("Starting risk monitoring task...")

            # Get risk report
            risk_report = risk_manager.get_risk_report()

            # Log key risk metrics
            logger.info(
                f"Risk Status: {risk_report['risk_status']}, "
                f"Balance: ${risk_report['account_state']['balance_usdt']:.2f}, "
                f"Daily P&L: ${risk_report['account_state']['daily_pnl']:.2f}, "
                f"Consecutive Losses: {risk_report['account_state']['consecutive_losses']}"
            )

            # Check for risk alerts
            if risk_report["risk_status"] == "suspended":
                logger.warning(
                    f"Trading suspended: {risk_report['account_state']['suspension_reason']}"
                )
                # Send risk alert notification
                notification_manager.send_risk_alert(
                    {
                        "alert_type": "TRADING_SUSPENDED",
                        "reason": risk_report["account_state"]["suspension_reason"],
                        "risk_report": risk_report,
                    }
                )
            elif risk_report["risk_status"] == "warning":
                logger.warning("Risk status is WARNING - monitoring closely")
                # Send warning notification
                notification_manager.send_risk_alert(
                    {
                        "alert_type": "RISK_WARNING",
                        "reason": "Risk parameters approaching limits",
                        "risk_report": risk_report,
                    }
                )

            logger.debug("Risk monitoring task completed")

        except Exception as e:
            logger.error(f"Error in risk monitoring task: {e}")

    async def health_monitoring_task(self):
        """Run focused trading-critical health monitoring."""
        try:
            logger.debug("Starting trading-critical health monitoring...")

            # Collect trading-specific metrics
            trading_metrics = {
                "last_data_update": time.time(),  # Should be updated by data manager
                "websocket_status": real_time_data_manager.get_stream_status(),
                "settlement_status": {"pending_settlements": len(db.get_pending_trades())},
                "system_metrics": asdict(system_monitor.collect_system_metrics())
            }
            
            # Check trading alerts (primary focus)
            trading_alerts = trading_alert_manager.check_trading_alerts(trading_metrics)
            
            # Check system alerts (secondary focus, higher thresholds)
            system_alerts = trading_alert_manager.check_system_alerts(trading_metrics)
            
            all_alerts = trading_alerts + system_alerts
            
            # Log alert summary
            if all_alerts:
                # Filter out None alerts
                valid_alerts = [a for a in all_alerts if a is not None]
                
                critical_count = len([a for a in valid_alerts if a.severity == "trading_critical"])
                warning_count = len([a for a in valid_alerts if a.severity == "trading_warning"])
                system_count = len([a for a in valid_alerts if a.severity == "system_info"])
                
                logger.info(
                    f"Trading Health Status - Critical: {critical_count}, "
                    f"Warning: {warning_count}, System: {system_count}"
                )
                
                # Send notifications for critical trading alerts only
                for alert in valid_alerts:
                    if alert.severity == "trading_critical":
                        notification_manager.send_system_health_alert({
                            "alert_type": "TRADING_CRITICAL",
                            "health_report": {"trading_alert": asdict(alert)},
                            "message": alert.message
                        })
            else:
                logger.debug("No trading-critical alerts detected")

            # Clean up old suppression rules
            trading_alert_manager.cleanup_old_suppressions()

            logger.debug("Health monitoring task completed")

        except Exception as e:
            logger.error(f"Error in health monitoring task: {e}")

    async def process_monitoring_task(self):
        """Monitor system processes and trigger recovery if needed."""
        try:
            logger.debug("Starting process monitoring task...")

            # Get recovery status
            recovery_status = recovery_manager.get_recovery_status()

            # Log key metrics
            logger.info(
                f"Process Monitor - "
                f"Active operations: {recovery_status['active_operations']}, "
                f"Pending operations: {recovery_status['pending_operations']}, "
                f"Total history: {recovery_status['total_history']}"
            )

            # Check for stuck operations
            for operation in recovery_status['active_operation_details']:
                if operation['retry_count'] >= 2:
                    logger.warning(
                        f"Operation {operation['operation_id']} has high retry count: "
                        f"{operation['retry_count']} attempts"
                    )

            # Monitor WebSocket connections
            ws_status = real_time_data_manager.get_stream_status()
            if ws_status['failed_connections'] > 0:
                logger.warning(f"WebSocket connection issues detected: {ws_status['failed_connections']} failures")

            # Monitor database health
            db_status = db.get_database_status()
            if db_status['status'] != 'healthy':
                logger.warning(f"Database health issues detected: {db_status.get('error', 'Unknown error')}")

            # Check for graceful degradation needs
            system_metrics = system_monitor.collect_system_metrics()
            if system_metrics.cpu_percent > 90 or system_metrics.memory_percent > 90:
                logger.warning("System under high stress - considering graceful degradation")
                await self._handle_graceful_degradation()

            logger.debug("Process monitoring task completed")

        except Exception as e:
            logger.error(f"Error in process monitoring task: {e}")

    async def _handle_graceful_degradation(self):
        """Handle graceful degradation during system stress."""
        try:
            # Trigger system recovery operation
            await recovery_manager.trigger_recovery(
                component="system",
                operation_type="graceful_degradation",
                priority=RecoveryPriority.HIGH,
                data={
                    "reason": "system_stress",
                    "timestamp": datetime.now().isoformat()
                }
            )

            # Reduce system load by temporarily disabling non-critical features
            # This is where you would implement specific degradation logic
            logger.info("Graceful degradation activated - reducing system load")

        except Exception as e:
            logger.error(f"Error handling graceful degradation: {e}")

    async def comprehensive_performance_report_task(self):
        """Generate comprehensive performance report with advanced analytics."""
        try:
            logger.info("Starting comprehensive performance report task...")

            # Generate comprehensive report
            report = advanced_analytics.get_comprehensive_performance_report(days=30)
            
            if "error" not in report:
                # Log key metrics
                performance_summary = report.get("performance_summary", {})
                risk_metrics = report.get("risk_metrics", {})
                
                logger.info(
                    f"Performance Report - "
                    f"Trades: {performance_summary.get('total_trades', 0)}, "
                    f"Win Rate: {performance_summary.get('win_rate', 0)}%, "
                    f"Total P&L: ${performance_summary.get('total_pnl', 0):.2f}, "
                    f"Sharpe Ratio: {risk_metrics.get('sharpe_ratio', 0):.3f}, "
                    f"Max Drawdown: {risk_metrics.get('max_drawdown', 0):.2%}"
                )
                
                # Log trading recommendations
                recommendations = report.get("trading_recommendations", [])
                if recommendations:
                    logger.info("Trading Recommendations:")
                    for rec in recommendations:
                        logger.info(f"  - {rec}")
                
                # Log cache performance
                cache_stats = report.get("cache_stats", {}).get("cache_performance", {})
                if cache_stats:
                    logger.info(
                        f"Cache Performance - "
                        f"Hit Rate: {cache_stats.get('hit_rate', 0):.1%}, "
                        f"Entries: {cache_stats.get('cache_entries', 0)}, "
                        f"Memory: {cache_stats.get('memory_usage_mb', 0):.1f}MB"
                    )
                
                # Send performance report notification
                notification_manager.send_performance_alert({
                    "alert_type": "PERFORMANCE_REPORT",
                    "report_summary": {
                        "total_trades": performance_summary.get('total_trades', 0),
                        "win_rate": performance_summary.get('win_rate', 0),
                        "total_pnl": performance_summary.get('total_pnl', 0),
                        "sharpe_ratio": risk_metrics.get('sharpe_ratio', 0),
                        "max_drawdown": risk_metrics.get('max_drawdown', 0)
                    },
                    "recommendations": recommendations,
                    "report_timestamp": report.get("report_timestamp")
                })
                
                logger.info("Comprehensive performance report completed")
            else:
                logger.error(f"Error generating performance report: {report['error']}")
                
        except Exception as e:
            logger.error(f"Error in comprehensive performance report task: {e}")
            # Send error notification
            notification_manager.send_error_notification({
                "error_type": "PerformanceReportError",
                "error_message": str(e),
                "task": "performance_report"
            })

    async def weekly_extension_stats_task(self):
        """Aggregate extension decision stats and send a DingTalk report weekly."""
        try:
            import asyncio, json, sys
            from pathlib import Path
            script = Path("tests/analyze_extension_stats.py")
            if not script.exists():
                logger.warning("Extension stats script not found, skipping.")
                return
            # Run the analyzer as a subprocess to avoid DB contention
            proc = await asyncio.create_subprocess_exec(
                sys.executable, str(script),
                stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE,
            )
            stdout, stderr = await proc.communicate()
            if stderr:
                logger.warning(f"Extension stats stderr: {stderr.decode().strip()}")
            result_text = stdout.decode().strip() or "{}"
            try:
                data = json.loads(result_text)
            except Exception:
                data = {"raw": result_text}

            # Format and send report
            msg_lines = [
                "### 📈 二次延时统计周报",
                f"- 样本数: {data.get('extension_trades', 0)}",
                f"- 亏损笔数: {data.get('loss_trades', 0)}",
                f"- 亏损比例: {data.get('loss_ratio', 0)*100:.2f}%",
            ]
            pnl_stats = data.get("pnl_pct_stats") or {}
            if pnl_stats:
                msg_lines.append("- pnl/投注额 分布(%)：")
                msg_lines.append(f"  - 最差: {pnl_stats.get('min', 0)}")
                msg_lines.append(f"  - 中位: {pnl_stats.get('median', 0)}")
                msg_lines.append(f"  - 平均: {pnl_stats.get('mean', 0)}")
            worst = data.get("worst_case_loss_pct")
            if worst is not None:
                msg_lines.append(f"- 最坏亏损(%)：{worst}")

            notification_manager.send_system_health_alert({
                "alert_type": "WEEKLY_EXTENSION_STATS",
                "message": "\n".join(msg_lines),
                "health_report": {"extension_stats": data}
            })
        except Exception as e:
            logger.error(f"Error in weekly extension stats task: {e}")

    async def settlement_backlog_check_task(self):
        """Check for settlement backlog and alert if necessary."""
        try:
            logger.debug("Starting settlement backlog check...")
            
            backlog_info = await settlement_checker.check_settlement_backlog()
            
            if backlog_info.get("critical_backlog", False):
                logger.warning(f"Critical settlement backlog detected: {backlog_info}")
            elif backlog_info.get("total_pending", 0) > 0:
                logger.info(f"Settlement status: {backlog_info['total_pending']} pending trades, {len(backlog_info.get('overdue_trades', []))} overdue")
            
            logger.debug("Settlement backlog check completed")
            
        except Exception as e:
            logger.error(f"Error in settlement backlog check task: {e}")

    async def run(self):
        """Run the trading system."""
        try:
            await self.initialize()
            self.setup_scheduler()

            logger.info("Starting Trading Signal System...")
            logger.info("Press Ctrl+C to stop the system")

            self.running = True
            self.scheduler.start()

            # Run initial tasks immediately
            await self.market_analysis_and_trade_task()
            await self.settlement_check_task()

            # Keep the system running
            await self.shutdown_event.wait()

        except Exception as e:
            logger.error(f"Error running trading system: {e}")
            raise
        finally:
            await self._cleanup()

    async def _cleanup(self):
        """Clean up resources before shutdown."""
        try:
            logger.info("Starting cleanup process...")

            # Stop real-time data manager
            await real_time_data_manager.stop()

            # Stop recovery manager
            await recovery_manager.stop()
            
            # 停止简化平仓管理器
            await simple_exit_manager.stop()

            # Close Binance client
            await binance_client.close()

            logger.info("Cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def _signal_handler(self, signum, frame):
        """Handle system signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()


def load_config(config_path: str | None = None):
    """Load configuration file."""
    if config_path is None:
        # Check for environment-specific config
        import os

        env = os.getenv("ENVIRONMENT", "development")
        config_path = f"config/config.{env}.json"

    if not Path(config_path).exists():
        logger.warning(
            f"Config file {config_path} not found, using default config/config.json"
        )
        config_path = "config/config.json"

    # Update config manager to use the specified config
    global config
    from quant.config_manager import ConfigManager

    config = ConfigManager(config_path)

    logger.info(f"Loaded configuration from {config_path}")


# Global config instance - will be initialized by load_config
config = None


async def main():
    """Main application entry point."""
    try:
        # Load configuration
        if len(sys.argv) > 1:
            load_config(sys.argv[1])
        else:
            load_config()

        # Create and run trading system
        trading_system = TradingSystem()
        await trading_system.run()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
