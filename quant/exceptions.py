"""
Custom Exception Classes

Defines custom exceptions for the trading system.
"""


class TradingSystemError(Exception):
    """Base exception for trading system errors."""

    pass


class ConfigurationError(TradingSystemError):
    """Configuration-related errors."""

    pass


class DatabaseError(TradingSystemError):
    """Database-related errors."""

    pass


class BinanceAPIError(TradingSystemError):
    """Binance API-related errors."""

    pass


class APIError(TradingSystemError):
    """Generic API-related errors for any exchange."""

    pass


class AnalysisError(TradingSystemError):
    """Analysis engine errors."""

    pass


class NotificationError(TradingSystemError):
    """Notification system errors."""

    pass


class SettlementError(TradingSystemError):
    """Settlement system errors."""

    pass


class DataValidationError(TradingSystemError):
    """Data validation errors."""

    pass


class NetworkError(TradingSystemError):
    """Network-related errors."""

    pass


class SchedulerError(TradingSystemError):
    """Task scheduler errors."""

    pass
