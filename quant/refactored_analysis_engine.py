"""
Refactored Analysis Engine - Simplified and Modular

This is a clean, maintainable version of the analysis engine that addresses
the complexity issues in the original simple_analysis_engine.py.

Key improvements:
- Modular design with clear separation of concerns
- Reduced cyclomatic complexity (<10 for all methods)
- Strategy pattern for different signal generation approaches
- Clear abstractions and interfaces
- Comprehensive error handling and logging
"""

from datetime import datetime
from typing import Any, Dict, Optional

import pandas as pd

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.real_time_data_manager import real_time_data_manager
from quant.utils.logger import get_logger
from quant.utils.structured_logger import get_system_logger, OperationType

# Import our new modular components
from quant.analysis.signal_generators import (
    SignalGeneratorFactory, 
    SignalResult,
    SignalDirection
)
from quant.analysis.position_sizer import create_position_sizer_from_config
from quant.analysis.market_data_analyzer import MarketDataAnalyzer

logger = get_logger(__name__)
structured_logger = get_system_logger("analysis_engine")


class RefactoredAnalysisEngine:
    """
    Refactored analysis engine with improved modularity and maintainability.
    
    This engine uses a strategy pattern to support different signal generation
    approaches while maintaining clean separation of concerns.
    """

    def __init__(self, strategy_name: str = "confidence_scoring", config_path: str = "config/config.json"):
        """
        Initialize the analysis engine with specified strategy.
        
        Args:
            strategy_name: Name of signal generation strategy to use
            config_path: Path to configuration file
        """
        self.symbol = "BTCUSDT"
        self.config_path = config_path
        self.strategy_name = strategy_name
        
        # Load configuration
        self._load_configuration()
        
        # Initialize components
        self._initialize_components()
        
        logger.info(f"RefactoredAnalysisEngine initialized with strategy: {strategy_name}")
    
    def _load_configuration(self):
        """Load and validate configuration."""
        try:
            from quant.config_manager import ConfigManager
            config = ConfigManager(self.config_path)
            
            # Load all relevant configuration sections
            self.risk_config = config.get('RISK_MANAGEMENT', {})
            self.auto_trader_config = config.get('AUTO_TRADER', {})
            self.simple_bet_config = config.get('SIMPLE_BET_CONTROL', {})
            self.confidence_config = config.get('CONFIDENCE_SCORING', {})
            
            # Extract key parameters
            self.confidence_threshold = float(self.confidence_config.get('thresholds', {}).get('minimum', 0.3))
            
            logger.info(f"Configuration loaded from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            # Use safe defaults
            self._use_default_configuration()
    
    def _use_default_configuration(self):
        """Use safe default configuration when loading fails."""
        self.risk_config = {}
        self.auto_trader_config = {'min_order_usdt': 10}
        self.simple_bet_config = {'enabled': True, 'fixed_bet_amount': 150.0}
        self.confidence_config = {'thresholds': {'minimum': 0.3}}
        self.confidence_threshold = 0.3
        
        logger.warning("Using default configuration due to loading error")
    
    def _initialize_components(self):
        """Initialize all engine components."""
        try:
            # Initialize signal generator using factory pattern
            self.signal_generator = SignalGeneratorFactory.create_generator(
                self.strategy_name,
                confidence_threshold=self.confidence_threshold
            )
            
            # Initialize position sizer
            config_dict = {
                'SIMPLE_BET_CONTROL': self.simple_bet_config,
                'RISK_MANAGEMENT': self.risk_config,
                'AUTO_TRADER': self.auto_trader_config
            }
            self.position_sizer = create_position_sizer_from_config(config_dict)
            
            # Initialize market data analyzer
            self.market_analyzer = MarketDataAnalyzer()
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            raise
    
    async def analyze_market(self) -> Optional[Dict[str, Any]]:
        """
        Perform market analysis and generate trading signal.
        
        Returns:
            Signal dictionary if analysis successful, None otherwise
        """
        with structured_logger.operation_context(
            OperationType.MARKET_ANALYSIS,
            symbol=self.symbol
        ) as ctx:
            
            try:
                # Step 1: Get market data
                market_data = await self._get_market_data()
                if market_data is None:
                    structured_logger.warning("No market data available for analysis")
                    return None
                
                structured_logger.info("Market data retrieved", extra={
                    "data_points": len(market_data),
                    "data_summary": self.market_analyzer.get_data_summary(market_data)
                })
                
                # Step 2: Analyze market data quality
                analysis = self.market_analyzer.analyze_market_data(market_data)
                if not analysis['validation']['is_valid']:
                    structured_logger.error("Market data validation failed", extra={
                        "validation_errors": analysis['validation']['errors']
                    })
                    return None
                
                # Step 3: Generate signal
                signal_result = self.signal_generator.generate_signal(market_data)
                if signal_result is None:
                    structured_logger.info("No signal generated by strategy")
                    return None
                
                # Step 4: Calculate position size
                position_size = self.position_sizer.calculate_position_size(
                    confidence=signal_result.confidence,
                    market_data=market_data
                )
                
                # Step 5: Create final signal
                signal_dict = signal_result.to_dict(self.symbol, position_size)
                
                # Step 6: Save to database
                trade_id = db.save_trade_signal(signal_dict)
                signal_dict['trade_id'] = trade_id
                
                structured_logger.info("Signal generated successfully", extra={
                    "trade_id": trade_id,
                    "direction": signal_result.direction.value,
                    "confidence": signal_result.confidence,
                    "position_size": position_size,
                    "strategy": self.strategy_name
                })
                
                return signal_dict
                
            except Exception as e:
                structured_logger.error("Error during market analysis", extra={
                    "error": str(e),
                    "strategy": self.strategy_name
                })
                return None
    
    async def _get_market_data(self) -> Optional[pd.DataFrame]:
        """
        Get market data for analysis with proper error handling.
        
        Returns:
            DataFrame with OHLCV data or None if unavailable
        """
        try:
            # Try to get data from real-time data manager first
            market_data = await real_time_data_manager.get_kline_data(
                symbol=self.symbol,
                interval='30m',
                limit=100
            )
            
            if market_data is not None and not market_data.empty:
                return market_data
            
            # Fallback to direct API call
            logger.warning("Real-time data unavailable, falling back to direct API call")
            klines = await binance_client.get_klines(
                symbol=self.symbol,
                interval='30m',
                limit=100
            )
            
            if not klines:
                logger.error("No kline data available from any source")
                return None
            
            # Convert klines to DataFrame
            return self._klines_to_dataframe(klines)
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return None
    
    def _klines_to_dataframe(self, klines: list) -> pd.DataFrame:
        """
        Convert kline data to pandas DataFrame.
        
        Args:
            klines: List of kline data from Binance API
            
        Returns:
            DataFrame with OHLCV columns
        """
        try:
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert numeric columns
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Convert timestamp to datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Return only OHLCV columns
            return df[numeric_columns]
            
        except Exception as e:
            logger.error(f"Error converting klines to DataFrame: {e}")
            return pd.DataFrame()
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get information about current strategy and configuration."""
        return {
            'strategy_name': self.strategy_name,
            'confidence_threshold': self.confidence_threshold,
            'symbol': self.symbol,
            'available_strategies': SignalGeneratorFactory.get_available_strategies(),
            'position_sizer_mode': getattr(self.position_sizer, 'mode', 'unknown'),
            'components_initialized': hasattr(self, 'signal_generator') and hasattr(self, 'position_sizer')
        }
    
    def switch_strategy(self, new_strategy: str) -> bool:
        """
        Switch to a different signal generation strategy.
        
        Args:
            new_strategy: Name of new strategy to use
            
        Returns:
            True if switch successful, False otherwise
        """
        try:
            new_generator = SignalGeneratorFactory.create_generator(
                new_strategy,
                confidence_threshold=self.confidence_threshold
            )
            
            self.signal_generator = new_generator
            self.strategy_name = new_strategy
            
            logger.info(f"Strategy switched to: {new_strategy}")
            return True
            
        except Exception as e:
            logger.error(f"Error switching strategy to {new_strategy}: {e}")
            return False
    
    def update_performance_stats(self, win_rate: float, avg_win: float, avg_loss: float):
        """
        Update performance statistics for adaptive components.
        
        Args:
            win_rate: Current win rate (0-1)
            avg_win: Average win amount
            avg_loss: Average loss amount
        """
        try:
            self.position_sizer.update_performance_stats(win_rate, avg_win, avg_loss)
            logger.info(f"Performance stats updated: WR={win_rate:.3f}, AvgWin={avg_win:.2f}, AvgLoss={avg_loss:.2f}")
            
        except Exception as e:
            logger.error(f"Error updating performance stats: {e}")


# Global instance for backward compatibility
analysis_engine = RefactoredAnalysisEngine()


# Async function for backward compatibility
async def analyze_market() -> Optional[Dict[str, Any]]:
    """
    Backward compatibility function for existing code.
    
    Returns:
        Signal dictionary if analysis successful, None otherwise
    """
    return await analysis_engine.analyze_market()


def get_engine_status() -> Dict[str, Any]:
    """Get status information about the analysis engine."""
    return analysis_engine.get_strategy_info()


def switch_analysis_strategy(strategy_name: str) -> bool:
    """
    Switch analysis strategy at runtime.
    
    Args:
        strategy_name: Name of strategy to switch to
        
    Returns:
        True if successful, False otherwise
    """
    return analysis_engine.switch_strategy(strategy_name)


# Utility functions for testing and development
def validate_market_data_quality(df: pd.DataFrame) -> Dict[str, Any]:
    """Validate market data quality using the analyzer."""
    analyzer = MarketDataAnalyzer()
    return analyzer.analyze_market_data(df)


def get_available_strategies() -> list:
    """Get list of available signal generation strategies."""
    return SignalGeneratorFactory.get_available_strategies()


if __name__ == "__main__":
    import asyncio
    
    async def test_analysis():
        """Test function for development."""
        print("Testing Refactored Analysis Engine...")
        
        # Test engine initialization
        engine = RefactoredAnalysisEngine("confidence_scoring")
        print(f"Engine info: {engine.get_strategy_info()}")
        
        # Test signal generation
        signal = await engine.analyze_market()
        if signal:
            print(f"Generated signal: {signal}")
        else:
            print("No signal generated")
        
        # Test strategy switching
        if engine.switch_strategy("simple"):
            print("Successfully switched to simple strategy")
            signal2 = await engine.analyze_market()
            print(f"Simple strategy signal: {signal2}")
    
    # Run test
    asyncio.run(test_analysis())