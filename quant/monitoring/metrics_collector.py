#!/usr/bin/env python3
"""
指标收集器

负责收集和聚合各种系统指标：
1. 服务性能指标 (CPU, 内存, 响应时间)
2. 业务指标 (交易信号数量, 成功率, PnL)
3. 系统健康指标 (错误率, 可用性)
4. 自定义指标
"""

import asyncio
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import threading
import json

from quant.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MetricPoint:
    """单个指标点"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}


@dataclass
class ServiceMetrics:
    """服务指标集合"""
    service_name: str
    timestamp: datetime
    cpu_percent: float
    memory_mb: float
    response_time_ms: float
    request_count: int
    error_count: int
    success_rate: float
    custom_metrics: Dict[str, float] = None
    
    def __post_init__(self):
        if self.custom_metrics is None:
            self.custom_metrics = {}


@dataclass
class BusinessMetrics:
    """业务指标"""
    timestamp: datetime
    signals_generated: int
    signals_executed: int
    execution_success_rate: float
    total_pnl: float
    daily_pnl: float
    active_positions: int
    risk_score: float
    api_calls_count: int
    api_success_rate: float


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.metrics_buffer: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.service_metrics: Dict[str, ServiceMetrics] = {}
        self.business_metrics: Optional[BusinessMetrics] = None
        
        # 配置
        self.collection_interval = self.config.get("collection_interval", 30)  # 30秒
        self.retention_hours = self.config.get("retention_hours", 24)  # 24小时
        self.max_buffer_size = self.config.get("max_buffer_size", 1000)
        
        # 运行状态
        self.running = False
        self.collection_task = None
        self.lock = threading.Lock()
        
        # 自定义指标收集器
        self.custom_collectors: Dict[str, Callable] = {}
        
        logger.info(f"MetricsCollector initialized - interval: {self.collection_interval}s, retention: {self.retention_hours}h")
    
    def register_custom_collector(self, name: str, collector_func: Callable):
        """注册自定义指标收集器"""
        self.custom_collectors[name] = collector_func
        logger.info(f"Registered custom collector: {name}")
    
    def collect_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """收集单个指标"""
        metric = MetricPoint(
            name=name,
            value=value,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        
        with self.lock:
            self.metrics_buffer[name].append(metric)
    
    def collect_service_metrics(self, service_name: str, 
                              response_time_ms: float = None,
                              request_count: int = None,
                              error_count: int = None,
                              custom_metrics: Dict[str, float] = None):
        """收集服务指标"""
        try:
            # 获取系统资源指标
            process = psutil.Process()
            cpu_percent = process.cpu_percent()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            # 计算成功率
            success_rate = 0.0
            if request_count and request_count > 0:
                success_rate = ((request_count - (error_count or 0)) / request_count) * 100
            
            metrics = ServiceMetrics(
                service_name=service_name,
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                response_time_ms=response_time_ms or 0.0,
                request_count=request_count or 0,
                error_count=error_count or 0,
                success_rate=success_rate,
                custom_metrics=custom_metrics or {}
            )
            
            with self.lock:
                self.service_metrics[service_name] = metrics
                # 同时添加到缓冲区
                self.metrics_buffer[f"{service_name}_cpu"].append(
                    MetricPoint(f"{service_name}_cpu", cpu_percent, datetime.now(), {"service": service_name})
                )
                self.metrics_buffer[f"{service_name}_memory"].append(
                    MetricPoint(f"{service_name}_memory", memory_mb, datetime.now(), {"service": service_name})
                )
                
        except Exception as e:
            logger.error(f"Error collecting service metrics for {service_name}: {e}")
    
    def collect_business_metrics(self,
                               signals_generated: int = 0,
                               signals_executed: int = 0,
                               total_pnl: float = 0.0,
                               daily_pnl: float = 0.0,
                               active_positions: int = 0,
                               risk_score: float = 0.0,
                               api_calls_count: int = 0,
                               api_success_rate: float = 0.0):
        """收集业务指标"""
        try:
            execution_success_rate = 0.0
            if signals_generated > 0:
                execution_success_rate = (signals_executed / signals_generated) * 100
            
            self.business_metrics = BusinessMetrics(
                timestamp=datetime.now(),
                signals_generated=signals_generated,
                signals_executed=signals_executed,
                execution_success_rate=execution_success_rate,
                total_pnl=total_pnl,
                daily_pnl=daily_pnl,
                active_positions=active_positions,
                risk_score=risk_score,
                api_calls_count=api_calls_count,
                api_success_rate=api_success_rate
            )
            
            # 添加到缓冲区
            business_metrics_data = {
                "signals_generated": signals_generated,
                "signals_executed": signals_executed,
                "execution_success_rate": execution_success_rate,
                "total_pnl": total_pnl,
                "daily_pnl": daily_pnl,
                "active_positions": active_positions,
                "risk_score": risk_score,
                "api_calls_count": api_calls_count,
                "api_success_rate": api_success_rate
            }
            
            with self.lock:
                for name, value in business_metrics_data.items():
                    self.metrics_buffer[f"business_{name}"].append(
                        MetricPoint(f"business_{name}", value, datetime.now(), {"type": "business"})
                    )
                    
        except Exception as e:
            logger.error(f"Error collecting business metrics: {e}")
    
    async def collect_system_metrics(self):
        """收集系统级指标"""
        try:
            # CPU和内存
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            # 收集指标
            self.collect_metric("system_cpu_percent", cpu_percent, {"component": "system"})
            self.collect_metric("system_memory_percent", memory.percent, {"component": "system"})
            self.collect_metric("system_memory_used_gb", memory.used / 1024 / 1024 / 1024, {"component": "system"})
            self.collect_metric("system_disk_percent", disk.percent, {"component": "system"})
            self.collect_metric("system_disk_free_gb", disk.free / 1024 / 1024 / 1024, {"component": "system"})
            
            # 网络统计 (如果可用)
            try:
                net_io = psutil.net_io_counters()
                self.collect_metric("network_bytes_sent", net_io.bytes_sent, {"component": "network"})
                self.collect_metric("network_bytes_recv", net_io.bytes_recv, {"component": "network"})
            except Exception:
                pass  # 某些系统可能不支持网络统计
                
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    async def collect_custom_metrics(self):
        """收集自定义指标"""
        for name, collector_func in self.custom_collectors.items():
            try:
                if asyncio.iscoroutinefunction(collector_func):
                    result = await collector_func()
                else:
                    result = collector_func()
                
                if isinstance(result, dict):
                    for metric_name, value in result.items():
                        self.collect_metric(f"custom_{metric_name}", value, {"collector": name})
                elif isinstance(result, (int, float)):
                    self.collect_metric(f"custom_{name}", result, {"collector": name})
                    
            except Exception as e:
                logger.error(f"Error collecting custom metrics from {name}: {e}")
    
    def get_latest_metrics(self) -> Dict[str, Any]:
        """获取最新的指标"""
        with self.lock:
            result = {
                "timestamp": datetime.now().isoformat(),
                "service_metrics": {},
                "business_metrics": None,
                "recent_metrics": {}
            }
            
            # 服务指标
            for service_name, metrics in self.service_metrics.items():
                result["service_metrics"][service_name] = asdict(metrics)
            
            # 业务指标
            if self.business_metrics:
                result["business_metrics"] = asdict(self.business_metrics)
            
            # 最近的指标点
            for metric_name, points in self.metrics_buffer.items():
                if points:
                    latest_point = points[-1]
                    result["recent_metrics"][metric_name] = {
                        "value": latest_point.value,
                        "timestamp": latest_point.timestamp.isoformat(),
                        "tags": latest_point.tags
                    }
            
            return result
    
    def get_metrics_history(self, metric_name: str, 
                           start_time: datetime = None, 
                           end_time: datetime = None) -> List[Dict[str, Any]]:
        """获取指标历史数据"""
        with self.lock:
            if metric_name not in self.metrics_buffer:
                return []
            
            points = list(self.metrics_buffer[metric_name])
            
            # 时间过滤
            if start_time or end_time:
                filtered_points = []
                for point in points:
                    if start_time and point.timestamp < start_time:
                        continue
                    if end_time and point.timestamp > end_time:
                        continue
                    filtered_points.append(point)
                points = filtered_points
            
            return [
                {
                    "name": point.name,
                    "value": point.value,
                    "timestamp": point.timestamp.isoformat(),
                    "tags": point.tags
                }
                for point in points
            ]
    
    def get_aggregated_metrics(self, metric_name: str, 
                             aggregation: str = "avg",
                             window_minutes: int = 60) -> Optional[float]:
        """获取聚合指标"""
        try:
            with self.lock:
                if metric_name not in self.metrics_buffer:
                    return None
                
                # 获取指定时间窗口内的数据
                cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
                values = [
                    point.value 
                    for point in self.metrics_buffer[metric_name]
                    if point.timestamp >= cutoff_time
                ]
                
                if not values:
                    return None
                
                # 计算聚合值
                if aggregation == "avg":
                    return sum(values) / len(values)
                elif aggregation == "max":
                    return max(values)
                elif aggregation == "min":
                    return min(values)
                elif aggregation == "sum":
                    return sum(values)
                elif aggregation == "count":
                    return len(values)
                else:
                    return sum(values) / len(values)  # 默认平均值
                    
        except Exception as e:
            logger.error(f"Error calculating aggregated metrics for {metric_name}: {e}")
            return None
    
    def cleanup_old_metrics(self):
        """清理过期指标"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
            
            with self.lock:
                for metric_name in list(self.metrics_buffer.keys()):
                    # 移除过期数据
                    points = self.metrics_buffer[metric_name]
                    while points and points[0].timestamp < cutoff_time:
                        points.popleft()
                    
                    # 如果队列为空，移除指标
                    if not points:
                        del self.metrics_buffer[metric_name]
                        
        except Exception as e:
            logger.error(f"Error cleaning up old metrics: {e}")
    
    async def collection_loop(self):
        """指标收集循环"""
        logger.info("Starting metrics collection loop")
        
        while self.running:
            try:
                # 收集系统指标
                await self.collect_system_metrics()
                
                # 收集自定义指标
                await self.collect_custom_metrics()
                
                # 清理过期数据
                self.cleanup_old_metrics()
                
                # 等待下一次收集
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def start(self):
        """启动指标收集"""
        if self.running:
            return
        
        self.running = True
        self.collection_task = asyncio.create_task(self.collection_loop())
        logger.info("MetricsCollector started")
    
    async def stop(self):
        """停止指标收集"""
        if not self.running:
            return
        
        self.running = False
        
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
            
        logger.info("MetricsCollector stopped")
    
    def export_metrics(self) -> Dict[str, Any]:
        """导出所有指标数据"""
        with self.lock:
            return {
                "service_metrics": {
                    name: asdict(metrics) 
                    for name, metrics in self.service_metrics.items()
                },
                "business_metrics": asdict(self.business_metrics) if self.business_metrics else None,
                "metrics_buffer": {
                    name: [
                        {
                            "name": point.name,
                            "value": point.value,
                            "timestamp": point.timestamp.isoformat(),
                            "tags": point.tags
                        }
                        for point in points
                    ]
                    for name, points in self.metrics_buffer.items()
                }
            }