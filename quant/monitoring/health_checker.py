#!/usr/bin/env python3
"""
健康检查器

定期检查系统各组件的健康状态：
1. 服务可用性检查
2. 数据库连接状态
3. 外部API连接状态
4. 系统资源状态
5. 业务流程健康检查
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import psutil
import aiohttp

from quant.utils.logger import get_logger

logger = get_logger(__name__)


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"  
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheck:
    """健康检查结果"""
    component: str
    status: HealthStatus
    message: str
    response_time_ms: float
    timestamp: datetime
    details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


class HealthChecker:
    """健康检查器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 配置
        self.check_interval = self.config.get("check_interval", 60)  # 60秒
        self.timeout = self.config.get("timeout", 30)  # 30秒超时
        self.retry_attempts = self.config.get("retry_attempts", 3)
        
        # 健康检查结果存储
        self.health_results: Dict[str, HealthCheck] = {}
        
        # 自定义检查器
        self.custom_checkers: Dict[str, Callable] = {}
        
        # 运行状态
        self.running = False
        self.check_task = None
        
        # 阈值配置
        self.thresholds = {
            "cpu_critical": self.config.get("cpu_critical", 90),
            "cpu_warning": self.config.get("cpu_warning", 70),
            "memory_critical": self.config.get("memory_critical", 90),
            "memory_warning": self.config.get("memory_warning", 80),
            "disk_critical": self.config.get("disk_critical", 95),
            "disk_warning": self.config.get("disk_warning", 85),
            "response_time_critical": self.config.get("response_time_critical", 10000),  # 10秒
            "response_time_warning": self.config.get("response_time_warning", 5000)     # 5秒
        }
        
        logger.info(f"HealthChecker initialized - interval: {self.check_interval}s")
    
    def register_custom_checker(self, name: str, checker_func: Callable):
        """注册自定义健康检查器"""
        self.custom_checkers[name] = checker_func
        logger.info(f"Registered custom health checker: {name}")
    
    async def check_system_resources(self) -> HealthCheck:
        """检查系统资源状态"""
        start_time = time.time()
        
        try:
            # CPU检查
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            details = {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used_gb": round(memory.used / 1024 / 1024 / 1024, 2),
                "memory_total_gb": round(memory.total / 1024 / 1024 / 1024, 2),
                "disk_percent": disk.percent,
                "disk_free_gb": round(disk.free / 1024 / 1024 / 1024, 2),
                "disk_total_gb": round(disk.total / 1024 / 1024 / 1024, 2)
            }
            
            # 确定健康状态
            status = HealthStatus.HEALTHY
            messages = []
            
            if cpu_percent > self.thresholds["cpu_critical"]:
                status = HealthStatus.CRITICAL
                messages.append(f"CPU usage critical: {cpu_percent:.1f}%")
            elif cpu_percent > self.thresholds["cpu_warning"]:
                status = HealthStatus.WARNING
                messages.append(f"CPU usage high: {cpu_percent:.1f}%")
            
            if memory.percent > self.thresholds["memory_critical"]:
                status = HealthStatus.CRITICAL
                messages.append(f"Memory usage critical: {memory.percent:.1f}%")
            elif memory.percent > self.thresholds["memory_warning"]:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
                messages.append(f"Memory usage high: {memory.percent:.1f}%")
            
            if disk.percent > self.thresholds["disk_critical"]:
                status = HealthStatus.CRITICAL
                messages.append(f"Disk usage critical: {disk.percent:.1f}%")
            elif disk.percent > self.thresholds["disk_warning"]:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
                messages.append(f"Disk usage high: {disk.percent:.1f}%")
            
            message = "; ".join(messages) if messages else "System resources normal"
            response_time = (time.time() - start_time) * 1000
            
            return HealthCheck(
                component="system_resources",
                status=status,
                message=message,
                response_time_ms=response_time,
                timestamp=datetime.now(),
                details=details
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheck(
                component="system_resources",
                status=HealthStatus.CRITICAL,
                message=f"System check failed: {str(e)}",
                response_time_ms=response_time,
                timestamp=datetime.now(),
                details={"error": str(e)}
            )
    
    async def check_database_connection(self) -> HealthCheck:
        """检查数据库连接"""
        start_time = time.time()
        
        try:
            # 这里需要根据实际的数据库管理器进行检查
            from quant.database_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            
            # 简单的数据库查询测试
            await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: db_manager.execute_query("SELECT 1")
            )
            
            response_time = (time.time() - start_time) * 1000
            
            status = HealthStatus.HEALTHY
            if response_time > self.thresholds["response_time_warning"]:
                status = HealthStatus.WARNING
            if response_time > self.thresholds["response_time_critical"]:
                status = HealthStatus.CRITICAL
            
            return HealthCheck(
                component="database",
                status=status,
                message="Database connection healthy" if status == HealthStatus.HEALTHY else f"Database slow response: {response_time:.0f}ms",
                response_time_ms=response_time,
                timestamp=datetime.now(),
                details={"query_response_time_ms": response_time}
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheck(
                component="database",
                status=HealthStatus.CRITICAL,
                message=f"Database connection failed: {str(e)}",
                response_time_ms=response_time,
                timestamp=datetime.now(),
                details={"error": str(e)}
            )
    
    async def check_binance_api(self) -> HealthCheck:
        """检查Binance API连接"""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get('https://api.binance.com/api/v3/ping') as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status == 200:
                        status = HealthStatus.HEALTHY
                        if response_time > self.thresholds["response_time_warning"]:
                            status = HealthStatus.WARNING
                        if response_time > self.thresholds["response_time_critical"]:
                            status = HealthStatus.CRITICAL
                        
                        return HealthCheck(
                            component="binance_api",
                            status=status,
                            message="Binance API healthy" if status == HealthStatus.HEALTHY else f"Binance API slow: {response_time:.0f}ms",
                            response_time_ms=response_time,
                            timestamp=datetime.now(),
                            details={"api_response_time_ms": response_time, "status_code": response.status}
                        )
                    else:
                        return HealthCheck(
                            component="binance_api",
                            status=HealthStatus.CRITICAL,
                            message=f"Binance API error: HTTP {response.status}",
                            response_time_ms=response_time,
                            timestamp=datetime.now(),
                            details={"status_code": response.status}
                        )
                        
        except asyncio.TimeoutError:
            response_time = (time.time() - start_time) * 1000
            return HealthCheck(
                component="binance_api",
                status=HealthStatus.CRITICAL,
                message=f"Binance API timeout after {response_time:.0f}ms",
                response_time_ms=response_time,
                timestamp=datetime.now(),
                details={"error": "timeout"}
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheck(
                component="binance_api",
                status=HealthStatus.CRITICAL,
                message=f"Binance API connection failed: {str(e)}",
                response_time_ms=response_time,
                timestamp=datetime.now(),
                details={"error": str(e)}
            )
    
    async def check_services_health(self, orchestrator=None) -> HealthCheck:
        """检查服务健康状态"""
        start_time = time.time()
        
        try:
            if not orchestrator:
                return HealthCheck(
                    component="services",
                    status=HealthStatus.UNKNOWN,
                    message="No orchestrator provided for service health check",
                    response_time_ms=0,
                    timestamp=datetime.now()
                )
            
            # 获取服务状态
            container_status = orchestrator.service_container.get_container_status()
            service_health = orchestrator.service_container.get_service_health_status()
            
            enabled_services = container_status.get("enabled_services", 0)
            healthy_services = sum(1 for health in service_health.values() 
                                 if health.get("running", False))
            
            response_time = (time.time() - start_time) * 1000
            
            # 确定健康状态
            if healthy_services == enabled_services and enabled_services > 0:
                status = HealthStatus.HEALTHY
                message = f"All {enabled_services} services healthy"
            elif healthy_services > 0:
                status = HealthStatus.WARNING
                message = f"{healthy_services}/{enabled_services} services healthy"
            else:
                status = HealthStatus.CRITICAL
                message = "No services running"
            
            return HealthCheck(
                component="services",
                status=status,
                message=message,
                response_time_ms=response_time,
                timestamp=datetime.now(),
                details={
                    "enabled_services": enabled_services,
                    "healthy_services": healthy_services,
                    "service_details": service_health
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheck(
                component="services",
                status=HealthStatus.CRITICAL,
                message=f"Service health check failed: {str(e)}",
                response_time_ms=response_time,
                timestamp=datetime.now(),
                details={"error": str(e)}
            )
    
    async def run_custom_checks(self) -> List[HealthCheck]:
        """运行自定义健康检查"""
        results = []
        
        for name, checker_func in self.custom_checkers.items():
            start_time = time.time()
            
            try:
                if asyncio.iscoroutinefunction(checker_func):
                    result = await checker_func()
                else:
                    result = checker_func()
                
                # 如果返回的是HealthCheck对象，直接使用
                if isinstance(result, HealthCheck):
                    results.append(result)
                # 如果返回布尔值，转换为HealthCheck
                elif isinstance(result, bool):
                    response_time = (time.time() - start_time) * 1000
                    results.append(HealthCheck(
                        component=f"custom_{name}",
                        status=HealthStatus.HEALTHY if result else HealthStatus.CRITICAL,
                        message=f"Custom check {name}: {'passed' if result else 'failed'}",
                        response_time_ms=response_time,
                        timestamp=datetime.now()
                    ))
                # 如果返回字典，解析为HealthCheck
                elif isinstance(result, dict):
                    response_time = (time.time() - start_time) * 1000
                    results.append(HealthCheck(
                        component=f"custom_{name}",
                        status=HealthStatus(result.get("status", "unknown")),
                        message=result.get("message", "Custom check completed"),
                        response_time_ms=response_time,
                        timestamp=datetime.now(),
                        details=result.get("details", {})
                    ))
                    
            except Exception as e:
                response_time = (time.time() - start_time) * 1000
                results.append(HealthCheck(
                    component=f"custom_{name}",
                    status=HealthStatus.CRITICAL,
                    message=f"Custom check {name} failed: {str(e)}",
                    response_time_ms=response_time,
                    timestamp=datetime.now(),
                    details={"error": str(e)}
                ))
        
        return results
    
    async def perform_health_checks(self, orchestrator=None) -> Dict[str, HealthCheck]:
        """执行所有健康检查"""
        logger.info("Performing health checks...")
        
        # 并发执行所有检查
        check_tasks = [
            self.check_system_resources(),
            self.check_database_connection(),
            self.check_binance_api(),
            self.check_services_health(orchestrator),
            self.run_custom_checks()
        ]
        
        try:
            results = await asyncio.gather(*check_tasks, return_exceptions=True)
            
            # 处理结果
            health_results = {}
            
            # 系统资源检查
            if isinstance(results[0], HealthCheck):
                health_results[results[0].component] = results[0]
            
            # 数据库检查
            if isinstance(results[1], HealthCheck):
                health_results[results[1].component] = results[1]
            
            # Binance API检查
            if isinstance(results[2], HealthCheck):
                health_results[results[2].component] = results[2]
            
            # 服务检查
            if isinstance(results[3], HealthCheck):
                health_results[results[3].component] = results[3]
            
            # 自定义检查
            if isinstance(results[4], list):
                for check in results[4]:
                    if isinstance(check, HealthCheck):
                        health_results[check.component] = check
            
            # 处理异常结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    component_name = f"check_{i}"
                    health_results[component_name] = HealthCheck(
                        component=component_name,
                        status=HealthStatus.CRITICAL,
                        message=f"Health check failed: {str(result)}",
                        response_time_ms=0,
                        timestamp=datetime.now(),
                        details={"error": str(result)}
                    )
            
            # 更新存储的结果
            self.health_results.update(health_results)
            
            return health_results
            
        except Exception as e:
            logger.error(f"Error performing health checks: {e}")
            return {}
    
    async def get_overall_health(self) -> Dict[str, Any]:
        """获取系统整体健康状态"""
        if not self.health_results:
            return {
                "status": "unknown",
                "message": "No health check data available",
                "timestamp": datetime.now().isoformat()
            }
        
        # 计算整体状态
        statuses = [check.status for check in self.health_results.values()]
        
        if HealthStatus.CRITICAL in statuses:
            overall_status = HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            overall_status = HealthStatus.WARNING
        elif HealthStatus.HEALTHY in statuses:
            overall_status = HealthStatus.HEALTHY
        else:
            overall_status = HealthStatus.UNKNOWN
        
        # 统计各状态数量
        status_counts = {}
        for status in HealthStatus:
            status_counts[status.value] = sum(1 for s in statuses if s == status)
        
        # 获取问题组件
        problem_components = [
            check.component 
            for check in self.health_results.values()
            if check.status in [HealthStatus.CRITICAL, HealthStatus.WARNING]
        ]
        
        return {
            "status": overall_status.value,
            "message": f"System {overall_status.value} - {len(problem_components)} components need attention" if problem_components else "All systems healthy",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_components": len(self.health_results),
                "status_counts": status_counts,
                "problem_components": problem_components
            },
            "components": {
                name: {
                    "status": check.status.value,
                    "message": check.message,
                    "response_time_ms": check.response_time_ms,
                    "timestamp": check.timestamp.isoformat(),
                    "details": check.details
                }
                for name, check in self.health_results.items()
            }
        }
    
    async def health_check_loop(self, orchestrator=None):
        """健康检查循环"""
        logger.info("Starting health check loop")
        
        while self.running:
            try:
                await self.perform_health_checks(orchestrator)
                await asyncio.sleep(self.check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def start(self, orchestrator=None):
        """启动健康检查器"""
        if self.running:
            return
        
        self.running = True
        self.check_task = asyncio.create_task(self.health_check_loop(orchestrator))
        logger.info("HealthChecker started")
    
    async def stop(self):
        """停止健康检查器"""
        if not self.running:
            return
        
        self.running = False
        
        if self.check_task:
            self.check_task.cancel()
            try:
                await self.check_task
            except asyncio.CancelledError:
                pass
        
        logger.info("HealthChecker stopped")