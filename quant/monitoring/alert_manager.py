#!/usr/bin/env python3
"""
告警管理器

负责管理和处理系统告警：
1. 告警规则定义和管理
2. 告警触发和通知
3. 告警状态跟踪
4. 告警抑制和分组
5. 多渠道通知支持
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, asdict
from enum import Enum
import json
from collections import defaultdict

from quant.utils.logger import get_logger

logger = get_logger(__name__)


class AlertSeverity(Enum):
    """告警严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """告警状态"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"
    ACKNOWLEDGED = "acknowledged"


@dataclass
class AlertRule:
    """告警规则"""
    id: str
    name: str
    description: str
    metric_name: str
    condition: str  # "gt", "lt", "eq", "ne"
    threshold: float
    severity: AlertSeverity
    duration: int = 60  # 持续时间(秒)
    enabled: bool = True
    tags: Dict[str, str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}


@dataclass
class Alert:
    """告警实例"""
    id: str
    rule_id: str
    rule_name: str
    metric_name: str
    current_value: float
    threshold: float
    severity: AlertSeverity
    status: AlertStatus
    message: str
    started_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    tags: Dict[str, str] = None
    context: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}
        if self.context is None:
            self.context = {}


class AlertManager:
    """告警管理器"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 告警规则
        self.rules: Dict[str, AlertRule] = {}
        
        # 活跃告警
        self.active_alerts: Dict[str, Alert] = {}
        
        # 告警历史
        self.alert_history: List[Alert] = []
        
        # 通知渠道
        self.notifiers: Dict[str, Callable] = {}
        
        # 抑制规则
        self.suppression_rules: List[Dict[str, Any]] = []
        
        # 运行状态
        self.running = False
        self.evaluation_task = None
        self.evaluation_interval = self.config.get("evaluation_interval", 30)  # 30秒
        
        # 配置
        self.max_history_size = self.config.get("max_history_size", 10000)
        self.default_notification_channels = self.config.get("notification_channels", ["log"])
        
        # 告警计数器
        self.alert_counters = defaultdict(int)
        
        # 通知限制
        self.notification_cooldown = self.config.get("notification_cooldown", 300)  # 5分钟
        self.last_notification_times: Dict[str, datetime] = {}
        
        logger.info(f"AlertManager initialized - evaluation interval: {self.evaluation_interval}s")
        
        # 加载默认规则
        self._load_default_rules()
    
    def _load_default_rules(self):
        """加载默认告警规则"""
        default_rules = [
            AlertRule(
                id="high_cpu_usage",
                name="CPU使用率过高",
                description="系统CPU使用率超过阈值",
                metric_name="system_cpu_percent",
                condition="gt",
                threshold=80.0,
                severity=AlertSeverity.HIGH,
                duration=120,
                tags={"component": "system", "type": "performance"}
            ),
            AlertRule(
                id="high_memory_usage", 
                name="内存使用率过高",
                description="系统内存使用率超过阈值",
                metric_name="system_memory_percent",
                condition="gt",
                threshold=85.0,
                severity=AlertSeverity.HIGH,
                duration=120,
                tags={"component": "system", "type": "performance"}
            ),
            AlertRule(
                id="disk_space_low",
                name="磁盘空间不足",
                description="系统磁盘使用率过高",
                metric_name="system_disk_percent",
                condition="gt", 
                threshold=90.0,
                severity=AlertSeverity.CRITICAL,
                duration=60,
                tags={"component": "system", "type": "capacity"}
            ),
            AlertRule(
                id="low_execution_rate",
                name="信号执行成功率过低",
                description="交易信号执行成功率低于阈值",
                metric_name="business_execution_success_rate",
                condition="lt",
                threshold=70.0,
                severity=AlertSeverity.HIGH,
                duration=180,
                tags={"component": "trading", "type": "business"}
            ),
            AlertRule(
                id="api_failure_rate",
                name="API失败率过高",
                description="外部API调用失败率过高",
                metric_name="business_api_success_rate",
                condition="lt",
                threshold=80.0,
                severity=AlertSeverity.MEDIUM,
                duration=300,
                tags={"component": "api", "type": "integration"}
            ),
            AlertRule(
                id="large_loss",
                name="单日亏损过大",
                description="单日PnL亏损超过阈值",
                metric_name="business_daily_pnl",
                condition="lt",
                threshold=-500.0,
                severity=AlertSeverity.CRITICAL,
                duration=60,
                tags={"component": "trading", "type": "risk"}
            )
        ]
        
        for rule in default_rules:
            self.add_rule(rule)

    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.rules[rule.id] = rule
        logger.info(f"Added alert rule: {rule.name} ({rule.id})")

    def remove_rule(self, rule_id: str) -> bool:
        """移除告警规则"""
        if rule_id in self.rules:
            rule = self.rules.pop(rule_id)
            logger.info(f"Removed alert rule: {rule.name} ({rule_id})")
            return True
        return False
    
    def update_rule(self, rule_id: str, updates: Dict[str, Any]) -> bool:
        """更新告警规则"""
        if rule_id not in self.rules:
            return False
        
        rule = self.rules[rule_id]
        for key, value in updates.items():
            if hasattr(rule, key):
                setattr(rule, key, value)
        
        logger.info(f"Updated alert rule: {rule.name} ({rule_id})")
        return True
    
    def register_notifier(self, name: str, notifier_func: Callable):
        """注册通知器"""
        self.notifiers[name] = notifier_func
        logger.info(f"Registered notifier: {name}")
    
    def add_suppression_rule(self, rule: Dict[str, Any]):
        """添加抑制规则"""
        self.suppression_rules.append(rule)
        logger.info(f"Added suppression rule: {rule}")
    
    def evaluate_rule(self, rule: AlertRule, metrics: Dict[str, Any]) -> Optional[Alert]:
        """评估单个规则"""
        try:
            # 获取指标值
            metric_value = self._get_metric_value(rule.metric_name, metrics)
            if metric_value is None:
                return None
            
            # 检查条件
            condition_met = self._evaluate_condition(
                metric_value, rule.condition, rule.threshold
            )
            
            alert_id = f"{rule.id}_{rule.metric_name}"
            
            if condition_met:
                # 检查是否已存在活跃告警
                if alert_id in self.active_alerts:
                    # 更新现有告警
                    alert = self.active_alerts[alert_id]
                    alert.current_value = metric_value
                    return alert
                else:
                    # 创建新告警
                    alert = Alert(
                        id=alert_id,
                        rule_id=rule.id,
                        rule_name=rule.name,
                        metric_name=rule.metric_name,
                        current_value=metric_value,
                        threshold=rule.threshold,
                        severity=rule.severity,
                        status=AlertStatus.ACTIVE,
                        message=self._generate_alert_message(rule, metric_value),
                        started_at=datetime.now(),
                        tags=rule.tags.copy(),
                        context={"rule_description": rule.description}
                    )
                    
                    self.active_alerts[alert_id] = alert
                    self.alert_counters[rule.severity.value] += 1
                    
                    logger.warning(f"Alert triggered: {alert.message}")
                    return alert
            else:
                # 条件不满足，解决现有告警
                if alert_id in self.active_alerts:
                    alert = self.active_alerts.pop(alert_id)
                    alert.status = AlertStatus.RESOLVED
                    alert.resolved_at = datetime.now()
                    
                    # 添加到历史
                    self.alert_history.append(alert)
                    self._cleanup_history()
                    
                    logger.info(f"Alert resolved: {alert.message}")
                    return alert
            
            return None
            
        except Exception as e:
            logger.error(f"Error evaluating rule {rule.id}: {e}")
            return None
    
    def _get_metric_value(self, metric_name: str, metrics: Dict[str, Any]) -> Optional[float]:
        """从指标数据中获取值"""
        # 检查最近指标
        recent_metrics = metrics.get("recent_metrics", {})
        if metric_name in recent_metrics:
            return recent_metrics[metric_name].get("value")
        
        # 检查业务指标
        business_metrics = metrics.get("business_metrics")
        if business_metrics and metric_name.startswith("business_"):
            field_name = metric_name[9:]  # 移除 "business_" 前缀
            return business_metrics.get(field_name)
        
        # 检查服务指标
        service_metrics = metrics.get("service_metrics", {})
        for service_name, service_data in service_metrics.items():
            if f"{service_name}_{metric_name}" in recent_metrics:
                return recent_metrics[f"{service_name}_{metric_name}"].get("value")
        
        return None
    
    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """评估条件"""
        if condition == "gt":
            return value > threshold
        elif condition == "lt":
            return value < threshold
        elif condition == "eq":
            return abs(value - threshold) < 0.001
        elif condition == "ne":
            return abs(value - threshold) >= 0.001
        elif condition == "gte":
            return value >= threshold
        elif condition == "lte":
            return value <= threshold
        else:
            logger.error(f"Unknown condition: {condition}")
            return False
    
    def _generate_alert_message(self, rule: AlertRule, current_value: float) -> str:
        """生成告警消息"""
        condition_text = {
            "gt": "超过",
            "lt": "低于", 
            "gte": "达到或超过",
            "lte": "达到或低于",
            "eq": "等于",
            "ne": "不等于"
        }.get(rule.condition, "触发")
        
        return f"{rule.name}: {rule.metric_name} {condition_text} 阈值 {rule.threshold}，当前值: {current_value:.2f}"
    
    def _is_suppressed(self, alert: Alert) -> bool:
        """检查告警是否被抑制"""
        for suppression_rule in self.suppression_rules:
            if self._match_suppression_rule(alert, suppression_rule):
                return True
        return False
    
    def _match_suppression_rule(self, alert: Alert, suppression_rule: Dict[str, Any]) -> bool:
        """检查告警是否匹配抑制规则"""
        # 简单的标签匹配
        required_tags = suppression_rule.get("tags", {})
        for key, value in required_tags.items():
            if alert.tags.get(key) != value:
                return False
        
        # 严重程度匹配
        if "severity" in suppression_rule:
            if alert.severity.value not in suppression_rule["severity"]:
                return False
        
        # 时间窗口
        if "time_window" in suppression_rule:
            start_time = datetime.fromisoformat(suppression_rule["time_window"]["start"])
            end_time = datetime.fromisoformat(suppression_rule["time_window"]["end"])
            if not (start_time <= alert.started_at <= end_time):
                return False
        
        return True
    
    async def send_notifications(self, alert: Alert):
        """发送通知"""
        if self._should_notify(alert):
            for channel in self.default_notification_channels:
                if channel in self.notifiers:
                    try:
                        await self._send_notification(channel, alert)
                    except Exception as e:
                        logger.error(f"Error sending notification via {channel}: {e}")
                else:
                    # 默认日志通知
                    await self._log_notification(alert)
            
            # 更新通知时间
            self.last_notification_times[alert.id] = datetime.now()
    
    def _should_notify(self, alert: Alert) -> bool:
        """检查是否应该发送通知"""
        # 检查冷却时间
        if alert.id in self.last_notification_times:
            last_notification = self.last_notification_times[alert.id]
            if (datetime.now() - last_notification).seconds < self.notification_cooldown:
                return False
        
        # 检查抑制状态
        if alert.status == AlertStatus.SUPPRESSED:
            return False
        
        return True
    
    async def _send_notification(self, channel: str, alert: Alert):
        """发送通知到指定渠道"""
        notifier = self.notifiers[channel]
        if asyncio.iscoroutinefunction(notifier):
            await notifier(alert)
        else:
            notifier(alert)
    
    async def _log_notification(self, alert: Alert):
        """默认日志通知"""
        severity_emoji = {
            AlertSeverity.LOW: "ℹ️",
            AlertSeverity.MEDIUM: "⚠️", 
            AlertSeverity.HIGH: "🚨",
            AlertSeverity.CRITICAL: "💥"
        }
        
        emoji = severity_emoji.get(alert.severity, "⚠️")
        logger.warning(f"{emoji} ALERT [{alert.severity.value.upper()}]: {alert.message}")
    
    def _cleanup_history(self):
        """清理告警历史"""
        if len(self.alert_history) > self.max_history_size:
            # 保留最新的记录
            self.alert_history = self.alert_history[-self.max_history_size:]
    
    async def evaluate_all_rules(self, metrics: Dict[str, Any]):
        """评估所有规则"""
        for rule in self.rules.values():
            if not rule.enabled:
                continue
            
            alert = self.evaluate_rule(rule, metrics)
            if alert:
                # 检查抑制
                if self._is_suppressed(alert):
                    alert.status = AlertStatus.SUPPRESSED
                
                # 发送通知
                if alert.status == AlertStatus.ACTIVE:
                    await self.send_notifications(alert)
    
    async def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        return [asdict(alert) for alert in self.active_alerts.values()]
    
    async def get_alert_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取告警历史"""
        return [asdict(alert) for alert in self.alert_history[-limit:]]
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str = "system") -> bool:
        """确认告警"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.now()
            alert.acknowledged_by = acknowledged_by
            
            logger.info(f"Alert acknowledged: {alert.message} by {acknowledged_by}")
            return True
        return False
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """获取告警统计"""
        total_rules = len(self.rules)
        enabled_rules = sum(1 for rule in self.rules.values() if rule.enabled)
        active_alerts_count = len(self.active_alerts)
        
        # 按严重程度统计
        severity_counts = defaultdict(int)
        for alert in self.active_alerts.values():
            severity_counts[alert.severity.value] += 1
        
        # 按状态统计
        status_counts = defaultdict(int)
        for alert in self.active_alerts.values():
            status_counts[alert.status.value] += 1
        
        return {
            "total_rules": total_rules,
            "enabled_rules": enabled_rules,
            "active_alerts": active_alerts_count,
            "severity_distribution": dict(severity_counts),
            "status_distribution": dict(status_counts),
            "notification_channels": list(self.notifiers.keys()),
            "suppression_rules_count": len(self.suppression_rules)
        }
    
    async def evaluation_loop(self, metrics_collector):
        """告警评估循环"""
        logger.info("Starting alert evaluation loop")
        
        while self.running:
            try:
                # 获取最新指标
                metrics = metrics_collector.get_latest_metrics()
                
                # 评估所有规则
                await self.evaluate_all_rules(metrics)
                
                # 等待下次评估
                await asyncio.sleep(self.evaluation_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert evaluation loop: {e}")
                await asyncio.sleep(self.evaluation_interval)
    
    async def start(self, metrics_collector):
        """启动告警管理器"""
        if self.running:
            return
        
        self.running = True
        self.evaluation_task = asyncio.create_task(self.evaluation_loop(metrics_collector))
        logger.info("AlertManager started")
    
    async def stop(self):
        """停止告警管理器"""
        if not self.running:
            return
        
        self.running = False
        
        if self.evaluation_task:
            self.evaluation_task.cancel()
            try:
                await self.evaluation_task
            except asyncio.CancelledError:
                pass
        
        logger.info("AlertManager stopped")
