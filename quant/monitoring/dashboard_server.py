#!/usr/bin/env python3
"""
监控仪表板服务器

提供基于Web的实时监控仪表板：
1. 实时系统状态展示
2. 服务健康状态监控
3. 性能指标图表
4. 业务指标统计
5. 告警状态显示
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path
import aiohttp
from aiohttp import web, WSMsgType
import weakref

from quant.utils.logger import get_logger

logger = get_logger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime对象"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, timedelta):
            return obj.total_seconds()
        return super().default(obj)


class DashboardServer:
    """监控仪表板服务器"""
    
    def __init__(self, 
                 metrics_collector,
                 health_checker=None,
                 alert_manager=None,
                 host: str = "localhost",
                 port: int = 8888):
        self.metrics_collector = metrics_collector
        self.health_checker = health_checker
        self.alert_manager = alert_manager
        self.host = host
        self.port = port
        
        # Web应用
        self.app = web.Application()
        self.setup_routes()
        
        # WebSocket连接管理
        self.websockets = set()
        
        # 运行状态
        self.running = False
        self.runner = None
        self.site = None
        
        # 数据更新任务
        self.update_task = None
        self.update_interval = 5  # 5秒更新一次
        
        logger.info(f"DashboardServer initialized on {host}:{port}")
    
    def setup_routes(self):
        """设置路由"""
        # 静态文件
        self.app.router.add_get('/', self.index_handler)
        self.app.router.add_get('/dashboard', self.dashboard_handler)
        
        # API路由
        self.app.router.add_get('/api/metrics', self.metrics_api)
        self.app.router.add_get('/api/metrics/history/{metric_name}', self.metrics_history_api)
        self.app.router.add_get('/api/health', self.health_api)
        self.app.router.add_get('/api/alerts', self.alerts_api)
        self.app.router.add_get('/api/services', self.services_api)
        
        # WebSocket
        self.app.router.add_get('/ws', self.websocket_handler)
        
        # 静态资源 (CSS, JS)
        self.app.router.add_static('/static/', path=str(Path(__file__).parent / 'static'), name='static')
    
    async def index_handler(self, request):
        """主页处理器"""
        return web.Response(text="Trading System Monitoring Dashboard", content_type='text/html')
    
    async def dashboard_handler(self, request):
        """仪表板页面"""
        html_content = self.generate_dashboard_html()
        return web.Response(text=html_content, content_type='text/html')
    
    async def metrics_api(self, request):
        """指标API"""
        try:
            metrics = self.metrics_collector.get_latest_metrics()
            return web.json_response(metrics, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
        except Exception as e:
            logger.error(f"Error in metrics API: {e}")
            return web.json_response({"error": str(e)}, status=500, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
    
    async def metrics_history_api(self, request):
        """指标历史API"""
        try:
            metric_name = request.match_info['metric_name']
            
            # 获取查询参数
            hours = int(request.query.get('hours', 1))
            start_time = datetime.now() - timedelta(hours=hours)
            
            history = self.metrics_collector.get_metrics_history(
                metric_name, start_time=start_time
            )
            
            return web.json_response({
                "metric_name": metric_name,
                "data": history
            }, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
            
        except Exception as e:
            logger.error(f"Error in metrics history API: {e}")
            return web.json_response({"error": str(e)}, status=500, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
    
    async def health_api(self, request):
        """健康状态API"""
        try:
            if self.health_checker:
                health_status = await self.health_checker.get_overall_health()
            else:
                health_status = {"status": "unknown", "message": "Health checker not available"}
            
            return web.json_response(health_status, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
            
        except Exception as e:
            logger.error(f"Error in health API: {e}")
            return web.json_response({"error": str(e)}, status=500, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
    
    async def alerts_api(self, request):
        """告警API"""
        try:
            if self.alert_manager:
                alerts = await self.alert_manager.get_active_alerts()
            else:
                alerts = []
            
            return web.json_response({"alerts": alerts}, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
            
        except Exception as e:
            logger.error(f"Error in alerts API: {e}")
            return web.json_response({"error": str(e)}, status=500, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
    
    async def services_api(self, request):
        """服务状态API"""
        try:
            services_status = {}
            
            # 从指标收集器获取服务状态
            metrics = self.metrics_collector.get_latest_metrics()
            service_metrics = metrics.get("service_metrics", {})
            
            for service_name, service_data in service_metrics.items():
                services_status[service_name] = {
                    "name": service_name,
                    "status": "healthy" if service_data.get("success_rate", 0) > 80 else "unhealthy",
                    "cpu_percent": service_data.get("cpu_percent", 0),
                    "memory_mb": service_data.get("memory_mb", 0),
                    "response_time_ms": service_data.get("response_time_ms", 0),
                    "success_rate": service_data.get("success_rate", 0),
                    "last_updated": service_data.get("timestamp")
                }
            
            return web.json_response({"services": services_status}, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
            
        except Exception as e:
            logger.error(f"Error in services API: {e}")
            return web.json_response({"error": str(e)}, status=500, dumps=lambda x: json.dumps(x, cls=DateTimeEncoder))
    
    async def websocket_handler(self, request):
        """WebSocket处理器"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        # 添加到连接集合
        self.websockets.add(ws)
        logger.info("WebSocket client connected")
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self.handle_websocket_message(ws, data)
                    except json.JSONDecodeError:
                        await ws.send_str(json.dumps({"error": "Invalid JSON"}))
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f"WebSocket error: {ws.exception()}")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            self.websockets.discard(ws)
            logger.info("WebSocket client disconnected")
        
        return ws
    
    async def handle_websocket_message(self, ws, data):
        """处理WebSocket消息"""
        try:
            message_type = data.get("type")
            
            if message_type == "get_metrics":
                metrics = self.metrics_collector.get_latest_metrics()
                await ws.send_str(json.dumps({
                    "type": "metrics_update",
                    "data": metrics
                }, cls=DateTimeEncoder))
            
            elif message_type == "get_health":
                if self.health_checker:
                    health = await self.health_checker.get_overall_health()
                else:
                    health = {"status": "unknown"}
                
                await ws.send_str(json.dumps({
                    "type": "health_update",
                    "data": health
                }, cls=DateTimeEncoder))
            
            elif message_type == "subscribe":
                # 客户端订阅实时更新
                await ws.send_str(json.dumps({
                    "type": "subscription_confirmed",
                    "data": {"status": "subscribed"}
                }, cls=DateTimeEncoder))
        
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await ws.send_str(json.dumps({"error": str(e)}, cls=DateTimeEncoder))
    
    async def broadcast_update(self, data):
        """向所有连接的WebSocket客户端广播更新"""
        if not self.websockets:
            return
        
        message = json.dumps({
            "type": "realtime_update",
            "timestamp": datetime.now().isoformat(),
            "data": data
        }, cls=DateTimeEncoder)
        
        # 创建弱引用列表避免在迭代时修改集合
        websockets_copy = list(self.websockets)
        
        for ws in websockets_copy:
            try:
                if ws.closed:
                    self.websockets.discard(ws)
                else:
                    await ws.send_str(message)
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {e}")
                self.websockets.discard(ws)
    
    async def update_loop(self):
        """数据更新循环"""
        logger.info("Starting dashboard update loop")
        
        while self.running:
            try:
                # 收集最新数据
                metrics = self.metrics_collector.get_latest_metrics()
                
                # 添加健康状态
                if self.health_checker:
                    health = await self.health_checker.get_overall_health()
                    metrics["health_status"] = health
                
                # 添加告警信息
                if self.alert_manager:
                    alerts = await self.alert_manager.get_active_alerts()
                    metrics["active_alerts"] = alerts
                
                # 广播更新
                await self.broadcast_update(metrics)
                
                await asyncio.sleep(self.update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in dashboard update loop: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def start(self):
        """启动仪表板服务器"""
        if self.running:
            return
        
        try:
            self.running = True
            
            # 启动HTTP服务器
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            self.site = web.TCPSite(self.runner, self.host, self.port)
            await self.site.start()
            
            # 启动数据更新循环
            self.update_task = asyncio.create_task(self.update_loop())
            
            logger.info(f"Dashboard server started on http://{self.host}:{self.port}")
            
        except Exception as e:
            logger.error(f"Failed to start dashboard server: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止仪表板服务器"""
        if not self.running:
            return
        
        try:
            self.running = False
            
            # 停止更新循环
            if self.update_task:
                self.update_task.cancel()
                try:
                    await self.update_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭WebSocket连接
            for ws in list(self.websockets):
                await ws.close()
            self.websockets.clear()
            
            # 停止HTTP服务器
            if self.site:
                await self.site.stop()
            
            if self.runner:
                await self.runner.cleanup()
            
            logger.info("Dashboard server stopped")
            
        except Exception as e:
            logger.error(f"Error stopping dashboard server: {e}")
    
    def generate_dashboard_html(self) -> str:
        """生成仪表板HTML页面"""
        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易系统监控仪表板</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .header h1 {{ margin: 0; }}
        .header .status {{
            display: flex;
            align-items: center;
            margin-top: 0.5rem;
        }}
        .status-dot {{
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }}
        .status-dot.healthy {{ background-color: #28a745; }}
        .status-dot.warning {{ background-color: #ffc107; }}
        .status-dot.error {{ background-color: #dc3545; }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }}
        .dashboard-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }}
        .card {{
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }}
        .card h3 {{
            margin-bottom: 1rem;
            color: #495057;
            font-size: 1.1rem;
        }}
        .metric {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }}
        .metric:last-child {{ margin-bottom: 0; }}
        .metric-label {{ color: #6c757d; }}
        .metric-value {{
            font-weight: 600;
            font-size: 1.1rem;
        }}
        .metric-value.healthy {{ color: #28a745; }}
        .metric-value.warning {{ color: #ffc107; }}
        .metric-value.error {{ color: #dc3545; }}
        .chart-container {{
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }}
        .services-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
        }}
        .service-card {{
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #28a745;
        }}
        .service-card.unhealthy {{
            border-left-color: #dc3545;
        }}
        .service-name {{
            font-weight: 600;
            margin-bottom: 0.5rem;
        }}
        .service-metrics {{
            font-size: 0.9rem;
            color: #6c757d;
        }}
        .loading {{
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }}
        .error-message {{
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 交易系统监控仪表板</h1>
        <div class="status">
            <div class="status-dot healthy" id="connection-status"></div>
            <span id="connection-text">正在连接...</span>
            <span style="margin-left: 2rem;">最后更新: <span id="last-update">-</span></span>
        </div>
    </div>

    <div class="container">
        <div id="error-container"></div>
        
        <!-- 系统概览 -->
        <div class="dashboard-grid">
            <div class="card">
                <h3>📊 系统性能</h3>
                <div id="system-metrics">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <div class="card">
                <h3>📈 业务指标</h3>
                <div id="business-metrics">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <div class="card">
                <h3>⚠️ 告警状态</h3>
                <div id="alerts-status">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
        
        <!-- 服务状态 -->
        <div class="chart-container">
            <h3>🔧 服务状态</h3>
            <div class="services-grid" id="services-grid">
                <div class="loading">加载服务状态...</div>
            </div>
        </div>
    </div>

    <script>
        class DashboardClient {{
            constructor() {{
                this.ws = null;
                this.reconnectDelay = 5000;
                this.maxReconnectAttempts = 5;
                this.reconnectAttempts = 0;
                this.lastUpdateTime = null;
                
                this.connect();
                this.startHeartbeat();
            }}
            
            connect() {{
                try {{
                    const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
                    this.ws = new WebSocket(`${{protocol}}//${{location.host}}/ws`);
                    
                    this.ws.onopen = () => {{
                        console.log('WebSocket connected');
                        this.updateConnectionStatus(true);
                        this.reconnectAttempts = 0;
                        
                        // 订阅实时更新
                        this.send({{ type: 'subscribe' }});
                        
                        // 请求初始数据
                        this.requestMetrics();
                        this.requestServices();
                    }};
                    
                    this.ws.onmessage = (event) => {{
                        try {{
                            const data = JSON.parse(event.data);
                            this.handleMessage(data);
                        }} catch (e) {{
                            console.error('Error parsing WebSocket message:', e);
                        }}
                    }};
                    
                    this.ws.onclose = () => {{
                        console.log('WebSocket disconnected');
                        this.updateConnectionStatus(false);
                        this.scheduleReconnect();
                    }};
                    
                    this.ws.onerror = (error) => {{
                        console.error('WebSocket error:', error);
                        this.showError('WebSocket连接错误');
                    }};
                    
                }} catch (e) {{
                    console.error('Error creating WebSocket:', e);
                    this.scheduleReconnect();
                }}
            }}
            
            send(data) {{
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {{
                    this.ws.send(JSON.stringify(data));
                }}
            }}
            
            scheduleReconnect() {{
                if (this.reconnectAttempts < this.maxReconnectAttempts) {{
                    this.reconnectAttempts++;
                    setTimeout(() => {{
                        console.log(`Attempting to reconnect (${{this.reconnectAttempts}}/${{this.maxReconnectAttempts}})...`);
                        this.connect();
                    }}, this.reconnectDelay);
                }} else {{
                    this.showError('无法连接到监控服务器，请刷新页面重试');
                }}
            }}
            
            updateConnectionStatus(connected) {{
                const statusDot = document.getElementById('connection-status');
                const statusText = document.getElementById('connection-text');
                
                if (connected) {{
                    statusDot.className = 'status-dot healthy';
                    statusText.textContent = '已连接';
                }} else {{
                    statusDot.className = 'status-dot error';
                    statusText.textContent = '连接中断';
                }}
            }}
            
            showError(message) {{
                const errorContainer = document.getElementById('error-container');
                errorContainer.innerHTML = `
                    <div class="error-message">
                        ❌ ${{message}}
                    </div>
                `;
            }}
            
            clearError() {{
                document.getElementById('error-container').innerHTML = '';
            }}
            
            handleMessage(data) {{
                switch (data.type) {{
                    case 'metrics_update':
                    case 'realtime_update':
                        this.updateMetrics(data.data);
                        break;
                    case 'subscription_confirmed':
                        console.log('Subscription confirmed');
                        break;
                    case 'error':
                        this.showError(data.error);
                        break;
                    default:
                        console.log('Unknown message type:', data.type);
                }}
            }}
            
            updateMetrics(metrics) {{
                this.lastUpdateTime = new Date();
                document.getElementById('last-update').textContent = 
                    this.lastUpdateTime.toLocaleTimeString();
                
                this.clearError();
                this.updateSystemMetrics(metrics);
                this.updateBusinessMetrics(metrics);
                this.updateAlertsStatus(metrics);
            }}
            
            updateSystemMetrics(metrics) {{
                const container = document.getElementById('system-metrics');
                const recentMetrics = metrics.recent_metrics || {{}};
                
                const systemCpu = recentMetrics.system_cpu_percent?.value || 0;
                const systemMemory = recentMetrics.system_memory_percent?.value || 0;
                const systemDisk = recentMetrics.system_disk_percent?.value || 0;
                
                container.innerHTML = `
                    <div class="metric">
                        <span class="metric-label">CPU使用率</span>
                        <span class="metric-value ${{systemCpu > 80 ? 'error' : systemCpu > 60 ? 'warning' : 'healthy'}}">
                            ${{systemCpu.toFixed(1)}}%
                        </span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">内存使用率</span>
                        <span class="metric-value ${{systemMemory > 85 ? 'error' : systemMemory > 70 ? 'warning' : 'healthy'}}">
                            ${{systemMemory.toFixed(1)}}%
                        </span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">磁盘使用率</span>
                        <span class="metric-value ${{systemDisk > 90 ? 'error' : systemDisk > 80 ? 'warning' : 'healthy'}}">
                            ${{systemDisk.toFixed(1)}}%
                        </span>
                    </div>
                `;
            }}
            
            updateBusinessMetrics(metrics) {{
                const container = document.getElementById('business-metrics');
                const businessMetrics = metrics.business_metrics;
                
                if (businessMetrics) {{
                    const executionRate = businessMetrics.execution_success_rate || 0;
                    const totalPnl = businessMetrics.total_pnl || 0;
                    const activePositions = businessMetrics.active_positions || 0;
                    
                    container.innerHTML = `
                        <div class="metric">
                            <span class="metric-label">信号执行成功率</span>
                            <span class="metric-value ${{executionRate > 90 ? 'healthy' : executionRate > 70 ? 'warning' : 'error'}}">
                                ${{executionRate.toFixed(1)}}%
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">总盈亏</span>
                            <span class="metric-value ${{totalPnl > 0 ? 'healthy' : totalPnl < -100 ? 'error' : 'warning'}}">
                                ${{totalPnl.toFixed(2)}} USDT
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">活跃持仓</span>
                            <span class="metric-value">${{activePositions}}</span>
                        </div>
                    `;
                }} else {{
                    container.innerHTML = '<div class="metric"><span class="metric-label">暂无业务数据</span></div>';
                }}
            }}
            
            updateAlertsStatus(metrics) {{
                const container = document.getElementById('alerts-status');
                const alerts = metrics.active_alerts || [];
                
                if (alerts.length === 0) {{
                    container.innerHTML = `
                        <div class="metric">
                            <span class="metric-label">系统状态</span>
                            <span class="metric-value healthy">正常</span>
                        </div>
                    `;
                }} else {{
                    const highPriority = alerts.filter(a => a.severity === 'high').length;
                    const mediumPriority = alerts.filter(a => a.severity === 'medium').length;
                    
                    container.innerHTML = `
                        <div class="metric">
                            <span class="metric-label">活跃告警</span>
                            <span class="metric-value ${{highPriority > 0 ? 'error' : 'warning'}}">
                                ${{alerts.length}}
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">高优先级</span>
                            <span class="metric-value error">${{highPriority}}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">中优先级</span>
                            <span class="metric-value warning">${{mediumPriority}}</span>
                        </div>
                    `;
                }}
            }}
            
            requestMetrics() {{
                this.send({{ type: 'get_metrics' }});
            }}
            
            requestServices() {{
                // 使用REST API获取服务状态
                fetch('/api/services')
                    .then(response => response.json())
                    .then(data => this.updateServices(data.services))
                    .catch(error => console.error('Error fetching services:', error));
            }}
            
            updateServices(services) {{
                const container = document.getElementById('services-grid');
                
                if (!services || Object.keys(services).length === 0) {{
                    container.innerHTML = '<div class="loading">暂无服务数据</div>';
                    return;
                }}
                
                const servicesHtml = Object.entries(services).map(([name, service]) => `
                    <div class="service-card ${{service.status === 'healthy' ? '' : 'unhealthy'}}">
                        <div class="service-name">
                            ${{service.status === 'healthy' ? '✅' : '❌'}} ${{name}}
                        </div>
                        <div class="service-metrics">
                            <div>CPU: ${{service.cpu_percent?.toFixed(1) || 0}}%</div>
                            <div>内存: ${{service.memory_mb?.toFixed(0) || 0}}MB</div>
                            <div>成功率: ${{service.success_rate?.toFixed(1) || 0}}%</div>
                        </div>
                    </div>
                `).join('');
                
                container.innerHTML = servicesHtml;
            }}
            
            startHeartbeat() {{
                setInterval(() => {{
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {{
                        this.requestMetrics();
                        this.requestServices();
                    }}
                }}, 10000); // 每10秒更新一次
            }}
        }}
        
        // 初始化仪表板
        document.addEventListener('DOMContentLoaded', () => {{
            new DashboardClient();
        }});
    </script>
</body>
</html>
"""