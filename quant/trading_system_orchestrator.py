"""
Trading System Orchestrator

Lightweight orchestrator that replaces the monolithic TradingSystem class.
Focuses solely on service coordination and task scheduling.
"""

import asyncio
import signal
from datetime import datetime
from typing import Any, Dict, Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

from quant.binance_client import binance_client
from quant.config_manager import ConfigManager
from quant.database_manager import db
from quant.error_handler import error_handler
from quant.notification_manager import notification_manager
from quant.real_time_data_manager import real_time_data_manager
from quant.recovery_manager import recovery_manager
from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.utils.logger import get_logger
from quant.services import ServiceContainer


logger = get_logger(__name__)


class TradingSystemOrchestrator:
    """
    Lightweight trading system orchestrator.
    
    Single responsibility: Coordinate services and manage task scheduling.
    All business logic is delegated to specialized services.
    
    Key improvements over original TradingSystem:
    - Single responsibility (orchestration only)
    - Proper dependency injection via ServiceContainer
    - Clear separation of concerns
    - Testable service interactions
    - Graceful error handling and recovery
    """
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.logger = get_logger(f"{__name__}.TradingSystemOrchestrator")
        
        # Core orchestration components
        self.scheduler = AsyncIOScheduler()
        self.service_container = ServiceContainer(config_manager)
        self.shutdown_event = asyncio.Event()
        self.running = False
        
        # Scheduling configuration
        self.analysis_interval = 30  # minutes
        self.settlement_interval_seconds = 30  # seconds (was 5 minutes)
        self.reconciliation_interval = 60  # minutes
        
        # Services (lazy-loaded via container)
        self._market_analysis_service = None
        self._settlement_service = None
        self._risk_management_service = None
        self._health_monitoring_service = None
        self._system_metrics_service = None
    
    @property
    def market_analysis_service(self):
        if self._market_analysis_service is None:
            self._market_analysis_service = self.service_container.get_service("market_analysis")
        return self._market_analysis_service
    
    @property
    def settlement_service(self):
        if self._settlement_service is None:
            self._settlement_service = self.service_container.get_service("settlement")
        return self._settlement_service
    
    @property
    def risk_management_service(self):
        if self._risk_management_service is None:
            self._risk_management_service = self.service_container.get_service("risk_management")
        return self._risk_management_service
    
    @property
    def health_monitoring_service(self):
        if self._health_monitoring_service is None:
            self._health_monitoring_service = self.service_container.get_service("health_monitoring")
        return self._health_monitoring_service
    
    @property
    def system_metrics_service(self):
        if self._system_metrics_service is None:
            self._system_metrics_service = self.service_container.get_service("system_metrics")
        return self._system_metrics_service
    
    async def initialize(self) -> None:
        """Initialize the trading system orchestrator."""
        try:
            self.logger.info("Initializing Trading System Orchestrator...")
            
            # Initialize core infrastructure
            await self._initialize_infrastructure()
            
            # Start all business services
            await self.service_container.start_all_services()
            
            # Setup error handling
            self._setup_error_handling()
            
            # Register signal handlers
            self._setup_signal_handlers()
            
            self.logger.info("Trading system orchestrator initialization completed")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize trading system orchestrator: {e}")
            raise
    
    async def _initialize_infrastructure(self) -> None:
        """Initialize core infrastructure components."""
        # Initialize database
        db.init_database()
        self.logger.info("Database initialized")
        
        # Initialize Binance client
        await binance_client.initialize()
        self.logger.info("Binance client initialized")
        
        # Initialize real-time data manager
        await real_time_data_manager.initialize()
        self.logger.info("Real-time data manager initialized")
        
        # Initialize recovery manager
        await recovery_manager.start()
        self.logger.info("Recovery manager initialized")
        
        # Start simple exit manager
        await simple_exit_manager.start()
        self.logger.info("Simple exit manager initialized")
    
    def _setup_error_handling(self) -> None:
        """Setup global error handling."""
        error_handler.setup_global_exception_handler()
        error_handler.add_shutdown_hook(self._cleanup)
    
    def _setup_signal_handlers(self) -> None:
        """Setup system signal handlers."""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def setup_scheduler(self) -> None:
        """Set up the task scheduler with service-based tasks."""
        self.logger.info("Setting up task scheduler...")
        
        # Data stream management (every minute)
        self.scheduler.add_job(
            self._data_stream_management_task,
            trigger=IntervalTrigger(minutes=1),
            id="data_stream_management",
            name="Data Stream Management",
            replace_existing=True,
        )
        
        # Market analysis and trading (configurable schedule)
        self._setup_market_analysis_schedule()
        
        # Settlement processing (every 30 seconds for efficiency)
        self.scheduler.add_job(
            self._settlement_task,
            trigger=IntervalTrigger(seconds=self.settlement_interval_seconds),
            id="settlement_processing",
            name="Settlement Processing",
            replace_existing=True,
        )
        
        # Risk monitoring (every 15 minutes)
        self.scheduler.add_job(
            self._risk_monitoring_task,
            trigger=IntervalTrigger(minutes=15),
            id="risk_monitoring",
            name="Risk Monitoring",
            replace_existing=True,
        )
        
        # System metrics monitoring (every 5 minutes)
        self.scheduler.add_job(
            self._system_metrics_task,
            trigger=IntervalTrigger(minutes=5),
            id="system_metrics",
            name="System Metrics",
            replace_existing=True,
        )
        
        # Health monitoring (every 15 minutes)
        self.scheduler.add_job(
            self._health_monitoring_task,
            trigger=IntervalTrigger(minutes=15),
            id="health_monitoring",
            name="Health Monitoring",
            replace_existing=True,
        )
        
        # Reconciliation (every 60 minutes)
        self.scheduler.add_job(
            self._reconciliation_task,
            trigger=IntervalTrigger(minutes=self.reconciliation_interval),
            id="reconciliation",
            name="Reconciliation",
            replace_existing=True,
        )
        
        # Daily reports (at midnight)
        self.scheduler.add_job(
            self._daily_report_task,
            trigger="cron",
            hour=0,
            minute=0,
            id="daily_report",
            name="Daily Report",
            replace_existing=True,
        )
        
        # Performance reports (every 6 hours)
        self.scheduler.add_job(
            self._performance_report_task,
            trigger=IntervalTrigger(hours=6),
            id="performance_report",
            name="Performance Report",
            replace_existing=True,
        )
        
        # Settlement backlog monitoring (every 2 minutes)
        self.scheduler.add_job(
            self._settlement_backlog_task,
            trigger=IntervalTrigger(minutes=2),
            id="settlement_backlog",
            name="Settlement Backlog Check",
            replace_existing=True,
        )
        
        self.logger.info("Task scheduler configured")
    
    def _setup_market_analysis_schedule(self) -> None:
        """Setup market analysis scheduling with configuration support."""
        try:
            sched_cfg = self.config.get("SCHEDULING", {}) or {}
            minutes = sched_cfg.get("analysis_minutes", [9, 39])
            tz = sched_cfg.get("timezone", "Asia/Shanghai")
            
            # Add scheduled analysis at specific minutes
            for idx, minute in enumerate(minutes):
                self.scheduler.add_job(
                    self._market_analysis_task,
                    trigger=CronTrigger(minute=int(minute), second=0, timezone=tz),
                    id=f"market_analysis_{idx}",
                    name=f"Market Analysis {minute}min",
                    replace_existing=True,
                )
            
            self.logger.info(f"Market analysis scheduled at minutes {minutes} in {tz}")
            
        except Exception as e:
            self.logger.warning(f"Cron scheduling failed, using fallback interval: {e}")
            # Fallback to interval-based scheduling
            self.scheduler.add_job(
                self._market_analysis_task,
                trigger=IntervalTrigger(minutes=self.analysis_interval),
                id="market_analysis_fallback",
                name="Market Analysis (Fallback)",
                replace_existing=True,
            )
    
    # Scheduled Task Methods (Delegate to Services)
    
    async def _market_analysis_task(self) -> None:
        """Market analysis task - delegates to MarketAnalysisService."""
        if self.market_analysis_service:
            await self.market_analysis_service.analyze_market_and_trade()
    
    async def _settlement_task(self) -> None:
        """Settlement task - delegates to SettlementService."""
        if self.settlement_service:
            await self.settlement_service.process_pending_settlements()
    
    async def _risk_monitoring_task(self) -> None:
        """Risk monitoring task - delegates to RiskManagementService."""
        if self.risk_management_service:
            await self.risk_management_service.monitor_risk_metrics()
    
    async def _system_metrics_task(self) -> None:
        """System metrics task - delegates to SystemMetricsService."""
        if self.system_metrics_service:
            await self.system_metrics_service.collect_system_metrics()
    
    async def _health_monitoring_task(self) -> None:
        """Health monitoring task - delegates to HealthMonitoringService."""
        if self.health_monitoring_service:
            await self.health_monitoring_service.run_health_monitoring()
    
    async def _data_stream_management_task(self) -> None:
        """Data stream management task - delegates to SystemMetricsService."""
        if self.system_metrics_service:
            await self.system_metrics_service.manage_data_streams()
    
    async def _reconciliation_task(self) -> None:
        """Reconciliation task - delegates to SettlementService."""
        if self.settlement_service:
            await self.settlement_service.run_reconciliation()
    
    async def _daily_report_task(self) -> None:
        """Daily report task - delegates to SettlementService."""
        if self.settlement_service:
            await self.settlement_service.generate_daily_report()
    
    async def _performance_report_task(self) -> None:
        """Performance report task - delegates to SystemMetricsService."""
        if self.system_metrics_service:
            await self.system_metrics_service.generate_performance_report()
    
    async def _settlement_backlog_task(self) -> None:
        """Settlement backlog monitoring task - delegates to SettlementService."""
        if self.settlement_service:
            backlog_info = await self.settlement_service.check_settlement_backlog()
            if backlog_info and backlog_info.get("critical_backlog"):
                self.logger.warning(f"Critical settlement backlog detected: {backlog_info}")
    
    async def run(self) -> None:
        """Run the trading system orchestrator."""
        try:
            await self.initialize()
            self.setup_scheduler()
            
            self.logger.info("Starting Trading System Orchestrator...")
            self.logger.info("Press Ctrl+C to stop the system")
            
            self.running = True
            self.scheduler.start()
            
            # Run initial tasks immediately
            await self._market_analysis_task()
            await self._settlement_task()
            
            # Keep the system running
            await self.shutdown_event.wait()
            
        except Exception as e:
            self.logger.error(f"Error running trading system orchestrator: {e}")
            raise
        finally:
            await self._cleanup()
    
    async def _cleanup(self) -> None:
        """Clean up resources before shutdown."""
        try:
            self.logger.info("Starting cleanup process...")
            
            # Stop scheduler
            if self.scheduler.running:
                self.scheduler.shutdown(wait=False)
            
            # Stop all services
            await self.service_container.stop_all_services()
            
            # Stop infrastructure
            await real_time_data_manager.stop()
            await recovery_manager.stop()
            await simple_exit_manager.stop()
            await binance_client.close()
            
            self.logger.info("Cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def _signal_handler(self, signum, frame) -> None:
        """Handle system signals."""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        return {
            "orchestrator": {
                "running": self.running,
                "scheduler_running": self.scheduler.running if hasattr(self.scheduler, 'running') else False,
                "shutdown_requested": self.shutdown_event.is_set()
            },
            "services": self.service_container.get_service_health_status(),
            "container": self.service_container.get_container_status(),
            "timestamp": datetime.utcnow().isoformat()
        }