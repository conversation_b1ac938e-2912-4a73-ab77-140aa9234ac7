"""
Enhanced Structured Logger with Context and Correlation IDs

Provides advanced logging capabilities with:
- Correlation IDs for request tracking
- Context management for operations 
- Retry-aware log levels
- Structured log aggregation
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, Optional, List
from contextlib import contextmanager
from dataclasses import dataclass, asdict
from enum import Enum

from quant.utils.logger import get_logger


class LogLevel(Enum):
    """Enhanced log levels for different scenarios."""
    TRACE = "TRACE"
    DEBUG = "DEBUG" 
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class OperationType(Enum):
    """Types of operations for context logging."""
    TRADE_EXECUTION = "trade_execution"
    MARKET_ANALYSIS = "market_analysis"
    DATA_FETCH = "data_fetch"
    SETTLEMENT = "settlement"
    RETRY_OPERATION = "retry_operation"
    SYSTEM_STARTUP = "system_startup"
    ERROR_RECOVERY = "error_recovery"


@dataclass
class LogContext:
    """Structured context for logging operations."""
    correlation_id: str
    operation_type: OperationType
    trade_id: Optional[int] = None
    symbol: Optional[str] = None
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {k: v.value if isinstance(v, Enum) else v 
                for k, v in asdict(self).items() if v is not None}


@dataclass 
class RetryContext:
    """Context for retry operations."""
    attempt: int
    max_attempts: int
    operation: str
    last_error: Optional[str] = None
    backoff_seconds: Optional[float] = None
    
    @property
    def is_final_attempt(self) -> bool:
        return self.attempt >= self.max_attempts
    
    @property
    def progress_ratio(self) -> float:
        return self.attempt / self.max_attempts if self.max_attempts > 0 else 1.0


class StructuredLogger:
    """Enhanced logger with context management and correlation tracking."""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
        self._context_stack: List[LogContext] = []
        self._session_id = str(uuid.uuid4())[:8]
    
    @contextmanager
    def operation_context(self, 
                         operation_type: OperationType,
                         correlation_id: Optional[str] = None,
                         **kwargs):
        """Context manager for operation logging."""
        ctx = LogContext(
            correlation_id=correlation_id or str(uuid.uuid4())[:12],
            operation_type=operation_type,
            session_id=self._session_id,
            **kwargs
        )
        
        self._context_stack.append(ctx)
        start_time = datetime.utcnow()
        
        try:
            self.info("Operation started", extra={
                "context": ctx.to_dict(),
                "event_type": "operation_start"
            })
            yield ctx
            
        except Exception as e:
            self.error("Operation failed", extra={
                "context": ctx.to_dict(),
                "error": str(e),
                "event_type": "operation_error"
            })
            raise
        
        finally:
            duration = (datetime.utcnow() - start_time).total_seconds()
            self.info("Operation completed", extra={
                "context": ctx.to_dict(),
                "duration_seconds": duration,
                "event_type": "operation_end"
            })
            self._context_stack.pop()
    
    def _get_current_context(self) -> Optional[Dict[str, Any]]:
        """Get current context from stack."""
        if self._context_stack:
            return self._context_stack[-1].to_dict()
        return None
    
    def _log_with_context(self, level: str, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log with current context information."""
        log_data = {
            "session_id": self._session_id,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
        # Add current context
        current_context = self._get_current_context()
        if current_context:
            log_data["context"] = current_context
        
        # Add extra data
        if extra:
            log_data.update(extra)
        
        getattr(self.logger, level.lower())(message, extra=log_data)
    
    def trace(self, message: str, **kwargs):
        """Log trace level message."""
        self._log_with_context("debug", message, kwargs)  # Map to DEBUG since TRACE not in stdlib
    
    def debug(self, message: str, **kwargs):
        """Log debug level message."""
        self._log_with_context("debug", message, kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info level message.""" 
        self._log_with_context("info", message, kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning level message."""
        self._log_with_context("warning", message, kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error level message."""
        self._log_with_context("error", message, kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical level message."""
        self._log_with_context("critical", message, kwargs)


class RetryLogger:
    """Specialized logger for retry operations with intelligent log levels."""
    
    def __init__(self, base_logger: StructuredLogger, operation_name: str):
        self.base_logger = base_logger
        self.operation_name = operation_name
        self.retry_contexts: Dict[str, RetryContext] = {}
    
    def log_retry_start(self, key: str, max_attempts: int) -> str:
        """Start a retry sequence and return correlation ID."""
        correlation_id = str(uuid.uuid4())[:12]
        self.retry_contexts[key] = RetryContext(
            attempt=0,
            max_attempts=max_attempts,
            operation=self.operation_name
        )
        
        with self.base_logger.operation_context(
            OperationType.RETRY_OPERATION,
            correlation_id=correlation_id
        ):
            self.base_logger.info(f"Starting retry sequence for {self.operation_name}", extra={
                "max_attempts": max_attempts,
                "retry_key": key,
                "event_type": "retry_sequence_start"
            })
        
        return correlation_id
    
    def log_attempt(self, key: str, correlation_id: str, error: Optional[str] = None):
        """Log a retry attempt with intelligent log level selection."""
        if key not in self.retry_contexts:
            self.base_logger.warning(f"Retry context not found for key: {key}")
            return
        
        ctx = self.retry_contexts[key]
        ctx.attempt += 1
        ctx.last_error = error
        
        # Intelligent log level selection
        if ctx.attempt == 1:
            # First attempt - always INFO
            log_level = "info"
            message = f"{self.operation_name} attempt {ctx.attempt}/{ctx.max_attempts}"
        elif ctx.attempt < ctx.max_attempts:
            # Intermediate attempts - DEBUG to reduce noise
            log_level = "debug" 
            message = f"{self.operation_name} retry {ctx.attempt}/{ctx.max_attempts}"
        else:
            # Final attempt - WARNING or ERROR
            log_level = "error" if error else "warning"
            message = f"{self.operation_name} final attempt {ctx.attempt}/{ctx.max_attempts}"
        
        extra_data = {
            "retry_context": asdict(ctx),
            "correlation_id": correlation_id,
            "event_type": "retry_attempt",
            "is_final": ctx.is_final_attempt,
            "progress": f"{ctx.progress_ratio:.1%}"
        }
        
        if error:
            extra_data["error"] = error
        
        getattr(self.base_logger, log_level)(message, extra=extra_data)
    
    def log_success(self, key: str, correlation_id: str):
        """Log successful completion of retry sequence."""
        if key in self.retry_contexts:
            ctx = self.retry_contexts[key]
            self.base_logger.info(f"{self.operation_name} succeeded after {ctx.attempt} attempts", extra={
                "retry_context": asdict(ctx),
                "correlation_id": correlation_id,
                "event_type": "retry_success"
            })
            del self.retry_contexts[key]
    
    def log_final_failure(self, key: str, correlation_id: str):
        """Log final failure of retry sequence."""
        if key in self.retry_contexts:
            ctx = self.retry_contexts[key]
            self.base_logger.error(f"{self.operation_name} failed after {ctx.attempt} attempts", extra={
                "retry_context": asdict(ctx),
                "correlation_id": correlation_id, 
                "event_type": "retry_final_failure"
            })
            del self.retry_contexts[key]


class LogAggregator:
    """Utility for aggregating and filtering log data."""
    
    @staticmethod
    def filter_by_operation(logs: List[Dict[str, Any]], operation_type: OperationType) -> List[Dict[str, Any]]:
        """Filter logs by operation type."""
        return [
            log for log in logs 
            if log.get("context", {}).get("operation_type") == operation_type.value
        ]
    
    @staticmethod
    def filter_by_correlation_id(logs: List[Dict[str, Any]], correlation_id: str) -> List[Dict[str, Any]]:
        """Filter logs by correlation ID."""
        return [
            log for log in logs
            if (log.get("context", {}).get("correlation_id") == correlation_id or
                log.get("correlation_id") == correlation_id)
        ]
    
    @staticmethod
    def get_operation_summary(logs: List[Dict[str, Any]], correlation_id: str) -> Dict[str, Any]:
        """Get summary of an operation by correlation ID."""
        operation_logs = LogAggregator.filter_by_correlation_id(logs, correlation_id)
        
        if not operation_logs:
            return {"error": "No logs found for correlation ID"}
        
        start_log = next((log for log in operation_logs if log.get("event_type") == "operation_start"), None)
        end_log = next((log for log in operation_logs if log.get("event_type") == "operation_end"), None)
        error_logs = [log for log in operation_logs if log.get("event_type") == "operation_error"]
        
        summary = {
            "correlation_id": correlation_id,
            "total_logs": len(operation_logs),
            "started": start_log is not None,
            "completed": end_log is not None,
            "errors": len(error_logs),
            "duration_seconds": end_log.get("duration_seconds") if end_log else None
        }
        
        if start_log:
            summary["operation_type"] = start_log.get("context", {}).get("operation_type")
            summary["start_time"] = start_log.get("timestamp")
        
        if error_logs:
            summary["error_details"] = [log.get("error") for log in error_logs]
        
        return summary


# Factory functions for common loggers
def get_trade_logger(name: str) -> StructuredLogger:
    """Get a structured logger for trade operations."""
    return StructuredLogger(f"trade.{name}")

def get_system_logger(name: str) -> StructuredLogger:
    """Get a structured logger for system operations."""
    return StructuredLogger(f"system.{name}")

def get_retry_logger(base_logger: StructuredLogger, operation: str) -> RetryLogger:
    """Get a retry logger for an operation."""
    return RetryLogger(base_logger, operation)


# Global instances
trade_logger = get_trade_logger("global")
system_logger = get_system_logger("global")