#!/usr/bin/env python3
"""
OKX BTCUSDT Deployment Validation Script

Validates the successful deployment of OKX trading system
"""

import sys
import os
from pathlib import Path
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def validate_environment():
    """Validate environment variables"""
    print("🔍 Validating Environment Variables...")
    
    required_vars = [
        "OKX_ACCESS_KEY",
        "OKX_SECRET_KEY", 
        "OKX_PASSPHRASE"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    print("✅ All required environment variables are set")
    return True


def validate_configuration():
    """Validate configuration files"""
    print("\n🔍 Validating Configuration Files...")
    
    config_files = [
        "config/config.development.json",
        "config/config.okx_production.json"
    ]
    
    for config_file in config_files:
        if not Path(config_file).exists():
            print(f"❌ Configuration file missing: {config_file}")
            return False
        
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            print(f"✅ Configuration file valid: {config_file}")
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in {config_file}: {e}")
            return False
    
    return True


def validate_dependencies():
    """Validate Python dependencies"""
    print("\n🔍 Validating Python Dependencies...")
    
    required_modules = [
        "asyncio", 
        "websockets",
        "aiohttp", 
        "pymongo",
        "apscheduler",
        "dotenv"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ Missing Python modules: {missing_modules}")
        return False
    
    print("✅ All required Python modules are available")
    return True


def validate_project_structure():
    """Validate project structure"""
    print("\n🔍 Validating Project Structure...")
    
    required_paths = [
        "main_refactored.py",
        "main.py",  # Symlink
        "quant/",
        "quant/exchange/",
        "quant/strategies/",
        "config/",
        "logs/",
        "data/"
    ]
    
    missing_paths = []
    for path in required_paths:
        if not Path(path).exists():
            missing_paths.append(path)
    
    if missing_paths:
        print(f"❌ Missing project paths: {missing_paths}")
        return False
    
    print("✅ Project structure is complete")
    return True


def test_basic_import():
    """Test basic module imports"""
    print("\n🔍 Testing Basic Module Imports...")
    
    try:
        # Add project path
        sys.path.insert(0, str(Path.cwd()))
        
        # Test core imports
        from quant.config_manager import ConfigManager
        from quant.trading_system_orchestrator import TradingSystemOrchestrator
        print("✅ Core modules import successfully")
        
        # Test OKX specific imports  
        from quant.exchange.okx_exchange_full import OkxExchangeFull
        print("✅ OKX exchange module imports successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False


def main():
    """Main validation function"""
    print("="*60)
    print("🚀 OKX BTCUSDT Trading System Deployment Validation")
    print("="*60)
    
    tests = [
        ("Environment Variables", validate_environment),
        ("Configuration Files", validate_configuration), 
        ("Python Dependencies", validate_dependencies),
        ("Project Structure", validate_project_structure),
        ("Module Imports", test_basic_import)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"❌ {test_name} validation failed")
        except Exception as e:
            print(f"❌ {test_name} validation crashed: {e}")
    
    print("\n" + "="*60)
    print("📊 DEPLOYMENT VALIDATION SUMMARY")
    print("="*60)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 DEPLOYMENT VALIDATION SUCCESSFUL!")
        print("✅ OKX BTCUSDT trading system is ready for operation")
        print("\nNext steps:")
        print("1. Run connection test: python3 main_refactored.py --test-connection")
        print("2. Run dry-run test: python3 main_refactored.py config/config.okx_production.json --dry-run")
        print("3. Start live trading: python3 main_refactored.py config/config.okx_production.json")
        return 0
    else:
        print(f"\n⚠️ DEPLOYMENT VALIDATION FAILED!")
        print(f"❌ {total_tests - passed_tests} validation test(s) failed")
        print("Please fix the issues above before proceeding with deployment")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)