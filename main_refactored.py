#!/usr/bin/env python3
"""
Trading Signal System - Refactored Main Application Entry Point

Binance BTC/USDT Event Contract Trading Signal Decision System
Author: Trading System Team
Date: 2025-08-16

REFACTORED VERSION:
- Lightweight orchestrator pattern
- Service-based architecture 
- Proper dependency injection
- Improved testability and maintainability
"""

# Load environment variables from .env file FIRST
from dotenv import load_dotenv
load_dotenv()

import asyncio
import sys
from pathlib import Path

# Filter APScheduler deprecation warnings
import warnings
warnings.filterwarnings(
    "ignore",
    message=r"pkg_resources is deprecated as an API.*",
    category=UserWarning,
    module=r"apscheduler(\.|$)"
)

from quant.config_manager import ConfigManager
from quant.trading_system_orchestrator import TradingSystemOrchestrator
from quant.utils.logger import get_logger

logger = get_logger(__name__)


def load_config(config_path: str | None = None) -> ConfigManager:
    """
    Load configuration file with environment-specific support.
    
    Args:
        config_path: Path to configuration file, or None for auto-detection
        
    Returns:
        ConfigManager instance
    """
    if config_path is None:
        # Check for environment-specific config
        import os
        env = os.getenv("ENVIRONMENT", "development")
        config_path = f"config/config.{env}.json"

    if not Path(config_path).exists():
        logger.warning(
            f"Config file {config_path} not found, using default config/config.json"
        )
        config_path = "config/config.json"

    config_manager = ConfigManager(config_path)
    logger.info(f"Loaded configuration from {config_path}")
    
    return config_manager


async def main():
    """
    Refactored main application entry point.
    
    Key improvements:
    - Uses lightweight orchestrator instead of monolithic TradingSystem
    - Proper dependency injection via ServiceContainer
    - Clear separation of concerns
    - Better error handling and recovery
    """
    try:
        # Load configuration
        config_path = sys.argv[1] if len(sys.argv) > 1 else None
        config_manager = load_config(config_path)

        # Create and run trading system orchestrator
        orchestrator = TradingSystemOrchestrator(config_manager)
        
        logger.info("=" * 60)
        logger.info("🚀 Starting Refactored Trading Signal System")
        logger.info("Architecture: Service-Based with Dependency Injection")
        logger.info("=" * 60)
        
        await orchestrator.run()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Fatal error in trading system: {e}")
        sys.exit(1)
    finally:
        logger.info("Trading system shutdown completed")


if __name__ == "__main__":
    # Enable asyncio debug mode in development
    import os
    if os.getenv("ENVIRONMENT") == "development":
        asyncio.get_event_loop().set_debug(True)
    
    asyncio.run(main())