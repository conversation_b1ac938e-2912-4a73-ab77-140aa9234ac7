#!/usr/bin/env python3
"""
OKX Trading System - Main Application Entry Point

OKX BTCUSDT Maker Order Trading System
Author: Trading System Team
Date: 2025-08-20

FEATURES:
- OKX exchange integration with maker order support
- Service-based architecture with dependency injection
- Real-time WebSocket data streams
- Advanced risk management
- Dry-run mode for safe testing
"""

# Load environment variables from .env file FIRST
from dotenv import load_dotenv
load_dotenv()

import asyncio
import sys
import argparse
from pathlib import Path

# Filter APScheduler deprecation warnings
import warnings
warnings.filterwarnings(
    "ignore",
    message=r"pkg_resources is deprecated as an API.*",
    category=UserWarning,
    module=r"apscheduler(\.|$)"
)

from quant.config_manager import ConfigManager
from quant.trading_system_orchestrator import TradingSystemOrchestrator
from quant.utils.logger import get_logger

logger = get_logger(__name__)


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="OKX BTCUSDT Trading System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_refactored.py                                    # Use development config
  python main_refactored.py config/config.okx_production.json # Use specific config
  python main_refactored.py --dry-run                         # Dry run mode
  python main_refactored.py config/config.okx_production.json --dry-run
        """
    )
    
    parser.add_argument(
        "config",
        nargs="?",
        default=None,
        help="Path to configuration file (default: auto-detect based on ENVIRONMENT)"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in dry-run mode (no actual trading)"
    )
    
    parser.add_argument(
        "--test-connection",
        action="store_true",
        help="Test exchange connection and exit"
    )
    
    return parser.parse_args()


def load_config(config_path: str | None = None) -> ConfigManager:
    """
    Load configuration file with environment-specific support.
    
    Args:
        config_path: Path to configuration file, or None for auto-detection
        
    Returns:
        ConfigManager instance
    """
    if config_path is None:
        # Check for environment-specific config
        import os
        env = os.getenv("ENVIRONMENT", "development")
        
        # Priority order for config files
        config_candidates = [
            f"config/config.{env}.json",
            f"config.{env}.json",
            "config/config.json",
            "config.json"
        ]
        
        config_path = None
        for candidate in config_candidates:
            if Path(candidate).exists():
                config_path = candidate
                break
        
        if config_path is None:
            raise FileNotFoundError(f"No configuration file found. Tried: {config_candidates}")

    if not Path(config_path).exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    config_manager = ConfigManager(config_path)
    logger.info(f"Loaded configuration from {config_path}")
    
    return config_manager


async def test_connection_only(config_manager):
    """Test exchange connection and exit"""
    from quant.exchange.okx_exchange_full import OkxExchangeFull
    
    try:
        logger.info("Testing OKX exchange connection...")
        exchange = OkxExchangeFull()
        await exchange.initialize()
        
        # Test supported symbols
        supported = exchange.get_supported_symbols()
        logger.info(f"✅ Connection successful! Found {len(supported)} supported symbols")
        
        # Test 24hr ticker for BTCUSDT
        ticker = await exchange.get_24hr_ticker("BTC/USDT")
        if ticker:
            logger.info(f"✅ BTCUSDT ticker: {ticker}")
        
        logger.info("🎉 OKX connection test completed successfully!")
        await exchange.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Connection test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


async def main():
    """
    OKX Trading System main entry point.
    
    Features:
    - Command line argument support
    - Dry-run mode for safe testing
    - Connection testing mode
    - Environment-based configuration
    """
    try:
        # Parse command line arguments
        args = parse_arguments()
        
        # Load configuration
        config_manager = load_config(args.config)
        
        # Handle test connection mode
        if args.test_connection:
            success = await test_connection_only(config_manager)
            sys.exit(0 if success else 1)

        # Create trading system orchestrator
        orchestrator = TradingSystemOrchestrator(config_manager)
        
        # Set dry-run mode if specified
        if args.dry_run:
            logger.info("🔍 Running in DRY-RUN mode - No actual trades will be executed")
            # TODO: Implement dry-run mode in orchestrator
        
        logger.info("=" * 60)
        logger.info("🚀 Starting OKX BTCUSDT Trading System")
        logger.info("Architecture: Service-Based with Dependency Injection")
        logger.info(f"Configuration: {config_manager.config_path}")
        logger.info(f"Exchange: OKX")
        logger.info(f"Symbol: BTCUSDT")
        logger.info(f"Mode: {'DRY-RUN' if args.dry_run else 'LIVE TRADING'}")
        logger.info("=" * 60)
        
        await orchestrator.run()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except FileNotFoundError as e:
        logger.error(f"Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Fatal error in trading system: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        logger.info("Trading system shutdown completed")


if __name__ == "__main__":
    # Enable asyncio debug mode in development
    import os
    if os.getenv("ENVIRONMENT") == "development":
        asyncio.get_event_loop().set_debug(True)
    
    asyncio.run(main())