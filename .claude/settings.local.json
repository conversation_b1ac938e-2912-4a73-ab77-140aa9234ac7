{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "Bash(find:*)", "Bash(tree:*)", "Bash(md-tree explode:*)", "Bash(npm install:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(python:*)", "Bash(pip3 install:*)", "Bash(black:*)", "Bash(ruff check:*)", "<PERSON><PERSON>(timeout:*)", "Bash(DEBUG=true python3 main.py config.development.json)", "Bash(ls:*)", "Bash(DEBUG=true timeout:*)", "<PERSON><PERSON>(python:*)", "Bash(ENVIRONMENT=development python3:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(pkill:*)", "Bash(ENVIRONMENT=development timeout 20 python3 main.py)", "Bash(rm:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(curl -I https://api.binance.com)", "Bash(SSL_CERT_FILE=\"\" python3:*)", "Bash(DEBUG=true python3:*)", "Bash(ENVIRONMENT=development DISABLE_SSL_VERIFY=true python3 main.py)", "Bash(ENVIRONMENT=development DISABLE_SSL_VERIFY=true timeout 30 python3 main.py)", "Bash(DISABLE_SSL_VERIFY=true python3:*)", "Bash(SSL_CERT_FILE=\"/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/certifi/cacert.pem\" python3:*)", "Bash(grep:*)", "<PERSON><PERSON>(echo:*)", "Bash(pip3 freeze:*)", "Bash(./scripts/deploy_refactored.sh:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "Bash(./scripts/health_check.sh:*)", "Bash(./scripts/fix_deployment_issues.sh:*)", "<PERSON><PERSON>(touch:*)", "Bash(./migrate_to_production.sh:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mv:*)", "Bash(pip3 uninstall:*)", "Bash(pip3 list:*)", "Bash(ENVIRONMENT=development DEBUG=true python3 -c \"\nimport asyncio\nasync def test_dev():\n    try:\n        from quant.config_manager import ConfigManager\n        from quant.trading_system_orchestrator import TradingSystemOrchestrator\n        \n        config_manager = ConfigManager(''config.development.json'')\n        orchestrator = TradingSystemOrchestrator(config_manager)\n        print(''✅ 开发环境系统组件初始化成功'')\n        \n        await orchestrator.initialize()\n        print(''✅ 开发环境系统服务初始化成功'')\n        \n        await orchestrator.shutdown()\n        print(''✅ 开发环境系统关闭成功'')\n        \n    except Exception as e:\n        print(f''❌ 开发环境测试失败: {e}'')\n        import traceback\n        traceback.print_exc()\n\nasyncio.run(test_dev())\n\")", "Bash(ENVIRONMENT=development DEBUG=true python3 -c \"\nimport asyncio\nasync def test_dev():\n    try:\n        from quant.config_manager import ConfigManager\n        from quant.trading_system_orchestrator import TradingSystemOrchestrator\n        \n        config_manager = ConfigManager(''config/config.development.json'')\n        orchestrator = TradingSystemOrchestrator(config_manager)\n        print(''✅ 开发环境系统组件初始化成功'')\n        \n        await orchestrator.initialize()\n        print(''✅ 开发环境系统服务初始化成功'')\n        \n        await orchestrator.shutdown()\n        print(''✅ 开发环境系统关闭成功'')\n        \n    except Exception as e:\n        print(f''❌ 开发环境测试失败: {e}'')\n        import traceback\n        traceback.print_exc()\n\nasyncio.run(test_dev())\n\")", "Bash(ENVIRONMENT=development DEBUG=true python3 -c \"\nimport asyncio\nasync def test_dev():\n    try:\n        from quant.config_manager import ConfigManager\n        from quant.trading_system_orchestrator import TradingSystemOrchestrator\n        \n        config_manager = ConfigManager(''config/config.development.json'')\n        orchestrator = TradingSystemOrchestrator(config_manager)\n        print(''✅ 开发环境系统组件初始化成功'')\n        \n        await orchestrator.initialize()\n        print(''✅ 开发环境系统服务初始化成功'')\n        \n        await orchestrator._cleanup()\n        print(''✅ 开发环境系统关闭成功'')\n        \n    except Exception as e:\n        print(f''❌ 开发环境测试失败: {e}'')\n        import traceback\n        traceback.print_exc()\n\nasyncio.run(test_dev())\n\")", "<PERSON><PERSON>(pip3 show:*)", "Bash(ENVIRONMENT=development DEBUG=true python3 -c \"\nimport asyncio\nasync def test_security_fix():\n    try:\n        from quant.config_manager import ConfigManager\n        from quant.trading_system_orchestrator import TradingSystemOrchestrator\n        \n        config_manager = ConfigManager(''config/config.development.json'')\n        orchestrator = TradingSystemOrchestrator(config_manager)\n        print(''✅ 配置修改后系统组件初始化成功'')\n        \n        await orchestrator.initialize()\n        print(''✅ 系统服务初始化成功 (使用环境变量)'')\n        \n        await orchestrator._cleanup()\n        print(''✅ 系统关闭成功'')\n        \n    except Exception as e:\n        print(f''❌ 测试失败: {e}'')\n        import traceback\n        traceback.print_exc()\n\nasyncio.run(test_security_fix())\n\")", "WebFetch(domain:developers.binance.com)", "Bash(ENVIRONMENT=development DEBUG=true python3 tools/validate_symbol_system.py)", "Bash(ENVIRONMENT=development DEBUG=true python3 tools/test_symbol_switch.py)", "Bash(ENVIRONMENT=development DEBUG=true python3 apply_config_changes.py)", "Bash(ENVIRONMENT=development DEBUG=true python3 demo_config_symbol_change.py)", "Bash(ENVIRONMENT=development DEBUG=true python3 tools/diagnose_trading_issue.py)", "Bash(ENVIRONMENT=development DEBUG=true python3 tools/test_database_connection.py)", "Bash(pgrep:*)", "Bash(kill:*)", "Bash(ENVIRONMENT=development DEBUG=true python3 tools/test_full_trading_cycle.py)", "Bash(export)"], "deny": []}}