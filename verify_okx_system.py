#!/usr/bin/env python3
"""
OKX交易系统安全验证脚本
定期检查系统状态、配置和API密钥安全性
"""

import os
import sys
from datetime import datetime

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

sys.path.insert(0, '.')

def log(message):
    """统一的日志输出"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {message}")

def security_check():
    """安全性检查"""
    print("🔐 系统安全性检查")
    print("-" * 40)
    
    # 1. 检查环境变量是否正确设置
    okx_key = os.getenv('OKX_ACCESS_KEY')
    okx_secret = os.getenv('OKX_SECRET_KEY')  
    okx_passphrase = os.getenv('OKX_PASSPHRASE')
    
    security_score = 0
    
    if okx_key:
        log("✅ OKX_ACCESS_KEY 已从环境变量加载")
        security_score += 1
    else:
        log("❌ OKX_ACCESS_KEY 未在环境变量中找到")
    
    if okx_secret:
        log("✅ OKX_SECRET_KEY 已从环境变量加载")
        security_score += 1
    else:
        log("❌ OKX_SECRET_KEY 未在环境变量中找到")
        
    if okx_passphrase:
        log("✅ OKX_PASSPHRASE 已从环境变量加载")
        security_score += 1
    else:
        log("❌ OKX_PASSPHRASE 未在环境变量中找到")
    
    # 2. 检查.env文件权限
    env_file = ".env"
    if os.path.exists(env_file):
        file_stat = os.stat(env_file)
        file_mode = oct(file_stat.st_mode)[-3:]
        
        if file_mode == "600":
            log("✅ .env文件权限安全 (600)")
            security_score += 1
        else:
            log(f"⚠️ .env文件权限: {file_mode} (建议设置为600)")
    else:
        log("❌ .env文件不存在")
    
    # 3. 检查是否有硬编码的API密钥
    try:
        # 检查测试脚本
        for test_file in ["test_complete_okx_system.py", "test_okx_config.py"]:
            if os.path.exists(test_file):
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'os.environ[' in content and ('OKX_ACCESS_KEY' in content or 'OKX_SECRET_KEY' in content):
                        # 检查是否是硬编码
                        if '=' in content and ('2cc32344' in content or 'E06C64E3' in content):
                            log(f"⚠️ {test_file} 可能包含硬编码的API密钥")
                        else:
                            log(f"✅ {test_file} 正确使用环境变量")
                            security_score += 0.5
                    else:
                        log(f"✅ {test_file} 未发现API密钥设置")
                        security_score += 0.5
        
    except Exception as e:
        log(f"⚠️ 检查代码文件时出错: {e}")
    
    print(f"\n安全评分: {security_score}/4.0")
    if security_score >= 3.5:
        log("🎉 系统安全性良好")
        return True
    else:
        log("⚠️ 建议改进系统安全配置")
        return False

def config_check():
    """配置检查"""
    print("\n⚙️ 系统配置检查")
    print("-" * 40)
    
    try:
        from quant.config_manager import config
        from quant.exchange_client import exchange_client
        
        # 检查交易所配置
        exchanges = config.get('EXCHANGES', {})
        auto_trader = config.get('AUTO_TRADER', {})
        
        default_exchange = exchanges.get('default')
        trader_exchange = auto_trader.get('exchange')
        current_exchange = exchange_client.get_exchange_name()
        
        log(f"默认交易所: {default_exchange}")
        log(f"自动交易交易所: {trader_exchange}")
        log(f"当前客户端: {current_exchange}")
        
        if all([default_exchange == 'okx', trader_exchange == 'okx', current_exchange == 'okx']):
            log("✅ OKX配置一致性检查通过")
            return True
        else:
            log("❌ OKX配置存在不一致")
            return False
            
    except Exception as e:
        log(f"❌ 配置检查失败: {e}")
        return False

def api_connectivity_check():
    """API连接性检查"""
    print("\n📡 API连接性检查")
    print("-" * 40)
    
    try:
        import asyncio
        from quant.exchange_client import exchange_client
        
        async def test_connection():
            try:
                # 测试获取市场数据
                klines = await exchange_client.get_klines(symbol='BTCUSDT', interval='1m', limit=1)
                if klines and len(klines) > 0:
                    price = float(klines[-1][4])
                    log(f"✅ OKX API连接正常，BTC价格: ${price:,.2f}")
                    return True
                else:
                    log("❌ OKX API连接失败：无法获取数据")
                    return False
            except Exception as e:
                log(f"❌ OKX API连接失败: {e}")
                return False
        
        return asyncio.run(test_connection())
        
    except Exception as e:
        log(f"❌ API连接测试失败: {e}")
        return False

def system_status_check():
    """系统状态检查"""
    print("\n📊 系统状态检查")
    print("-" * 40)
    
    try:
        # 检查日志文件
        log_files = ["logs/error.log", "logs/info.log", "logs/trading.log"]
        for log_file in log_files:
            if os.path.exists(log_file):
                file_size = os.path.getsize(log_file)
                log(f"✅ {log_file}: {file_size:,} bytes")
            else:
                log(f"ℹ️ {log_file}: 不存在")
        
        # 检查数据库
        db_files = ["data/trading_system.db", "data/timeseries.db"]
        for db_file in db_files:
            if os.path.exists(db_file):
                file_size = os.path.getsize(db_file)
                log(f"✅ {db_file}: {file_size:,} bytes")
            else:
                log(f"ℹ️ {db_file}: 不存在")
        
        return True
        
    except Exception as e:
        log(f"❌ 系统状态检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 OKX交易系统安全验证报告")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    checks = {
        "安全性检查": security_check(),
        "配置检查": config_check(),
        "API连接性检查": api_connectivity_check(),
        "系统状态检查": system_status_check()
    }
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 检查结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(checks)
    
    for check_name, result in checks.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总评: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 系统状态良好，OKX交易系统运行正常!")
        print("🔒 API密钥安全从.env文件加载，未发现硬编码风险")
    elif passed >= total * 0.75:
        print("⚠️ 系统基本正常，但有部分问题需要关注")
    else:
        print("❌ 系统存在重要问题，建议立即检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)