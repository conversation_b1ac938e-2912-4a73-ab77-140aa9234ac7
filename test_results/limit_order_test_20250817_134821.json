{"timestamp": "2025-08-17T13:48:21.774885", "summary": {"total": 13, "passed": 13, "failed": 0, "success_rate": 1.0}, "results": [{"test": "price_calculation", "strategy": "AGGRESSIVE", "success": true, "buy_price": 117993.78, "sell_price": 118075.90000000001, "buy_spread_bps": 4.748841675685259, "sell_spread_bps": 2.2075421703250275}, {"test": "price_calculation", "strategy": "BALANCED", "success": true, "buy_price": 117993.78, "sell_price": 118076.11, "buy_spread_bps": 4.748841675685259, "sell_spread_bps": 2.2253312668618674}, {"test": "price_calculation", "strategy": "PASSIVE", "success": true, "buy_price": 117993.78, "sell_price": 118076.11, "buy_spread_bps": 4.748841675685259, "sell_spread_bps": 2.2253312668618674}, {"test": "limit_order_simulation", "success": true, "buy_order": {"order_id": null, "status": "FAILED", "limit_price": 117993.78}, "sell_order": {"order_id": null, "status": "FAILED", "limit_price": 118076.11}}, {"test": "urgency_mode", "urgency": "NORMAL", "success": true, "status": "FAILED", "execution_time": 0.36523}, {"test": "urgency_mode", "urgency": "HIGH", "success": true, "status": "FAILED", "execution_time": 0.363844}, {"test": "urgency_mode", "urgency": "EMERGENCY", "success": true, "status": "FAILED", "execution_time": 0.548809}, {"test": "fee_calculation", "amount": 10, "taker_fee": 0.004, "maker_fee": 0.002, "saved": 0.002, "success": true}, {"test": "fee_calculation", "amount": 50, "taker_fee": 0.02, "maker_fee": 0.01, "saved": 0.01, "success": true}, {"test": "fee_calculation", "amount": 100, "taker_fee": 0.04, "maker_fee": 0.02, "saved": 0.02, "success": true}, {"test": "fee_calculation", "amount": 500, "taker_fee": 0.2, "maker_fee": 0.1, "saved": 0.1, "success": true}, {"test": "fee_calculation", "amount": 1000, "taker_fee": 0.4, "maker_fee": 0.2, "saved": 0.2, "success": true}, {"test": "metrics", "success": true, "metrics": {"total_limit_orders": 5, "filled_orders": 0, "fill_rate": 0.0, "avg_fill_time": 0.0, "fees_saved": 0.332, "fallback_to_market": 0, "fallback_rate": 0.0}}]}