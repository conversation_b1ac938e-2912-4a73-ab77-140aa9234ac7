#!/usr/bin/env python3
"""
验证信号生成管道完整修复
"""

import asyncio
import sys
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent))

from quant.real_time_data_manager import real_time_data_manager
from quant.binance_client import binance_client
from quant.simple_analysis_engine import analysis_engine
from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def verify_complete_pipeline():
    """验证完整的信号生成管道"""
    print("🔍 开始验证信号生成管道...")
    
    try:
        # 1. 初始化所有组件
        print("\n1️⃣ 初始化组件...")
        db.init_database()
        await binance_client.initialize()
        await real_time_data_manager.initialize()
        print("✅ 所有组件初始化完成")
        
        # 2. 启动数据流
        print("\n2️⃣ 启动数据流...")
        await real_time_data_manager.start_kline_stream("BTCUSDT", "1m")
        await real_time_data_manager.start_kline_stream("BTCUSDT", "5m")
        await real_time_data_manager.start_kline_stream("BTCUSDT", "15m")
        await real_time_data_manager.start_kline_stream("BTCUSDT", "30m")
        print("✅ 所有数据流启动完成")
        
        # 3. 等待数据接收
        print("\n3️⃣ 等待数据接收...")
        for i in range(10):
            await asyncio.sleep(1)
            status = real_time_data_manager.get_stream_status()
            messages = status.get('messages_processed', 0)
            active_streams = status.get('active_streams', 0)
            
            if i % 3 == 2:  # 每3秒报告一次
                print(f"   第{i+1}秒: 活跃流={active_streams}, 处理消息={messages}")
        
        # 4. 检查数据流状态
        print("\n4️⃣ 检查数据流状态...")
        final_status = real_time_data_manager.get_stream_status()
        messages_processed = final_status.get('messages_processed', 0)
        active_streams = final_status.get('active_streams', 0)
        
        print(f"   活跃流数量: {active_streams}")
        print(f"   处理消息数: {messages_processed}")
        
        if messages_processed > 0:
            print("✅ 数据流正常工作")
        else:
            print("❌ 数据流未接收到消息")
            return False
        
        # 5. 测试分析引擎
        print("\n5️⃣ 测试分析引擎...")
        signal = await analysis_engine.analyze_market()
        
        if signal:
            print("✅ 分析引擎成功生成信号:")
            print(f"   方向: {signal['direction']}")
            print(f"   置信度: {signal['confidence_score']:.3f}")
            print(f"   入场价格: ${signal['entry_price']:,.2f}")
        else:
            print("⚠️ 分析引擎未生成信号（可能是市场条件不满足）")
        
        # 6. 检查数据库状态
        print("\n6️⃣ 检查数据库状态...")
        db_status = db.get_database_status()
        print(f"   数据库状态: {db_status['status']}")
        print(f"   响应时间: {db_status.get('response_time_ms', 0):.2f}ms")
        
        # 7. 检查最近交易
        recent_trades = db.get_optimized_trade_history(limit=3)
        print(f"   最近交易数量: {len(recent_trades)}")
        
        print("\n🎉 信号生成管道验证完成！")
        
        # 8. 总结
        print("\n📊 验证结果总结:")
        print(f"   ✅ WebSocket数据流: {messages_processed} 条消息已处理")
        print(f"   ✅ 活跃流数量: {active_streams}")
        print(f"   ✅ 分析引擎: {'正常' if signal else '待市场条件'}")
        print(f"   ✅ 数据库: {db_status['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理
        try:
            await real_time_data_manager.stop()
        except:
            pass


async def main():
    success = await verify_complete_pipeline()
    if success:
        print("\n🚀 系统已修复，信号生成管道正常工作！")
        print("\n建议:")
        print("1. 重启主程序 (python3 main.py)")
        print("2. 观察 System Metrics 中的 messages_processed 是否持续增长")
        print("3. 检查是否开始生成新的交易信号")
    else:
        print("\n⚠️ 仍有问题需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())
