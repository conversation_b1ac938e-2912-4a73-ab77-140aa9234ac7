#!/usr/bin/env python3
"""
信号生成管道诊断脚本
用于诊断为什么信号生成停止工作
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent))

from quant.real_time_data_manager import real_time_data_manager
from quant.binance_client import binance_client
from quant.database_manager import db
from quant.simple_analysis_engine import analysis_engine
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def diagnose_websocket_streams():
    """诊断WebSocket流状态"""
    print("\n=== WebSocket流诊断 ===")
    
    # 获取流状态
    status = real_time_data_manager.get_stream_status()
    print(f"活跃流数量: {status['active_streams']}")
    print(f"消息处理数: {status.get('messages_processed', 0)}")
    print(f"失败消息数: {status.get('messages_failed', 0)}")
    print(f"错误率: {status.get('error_rate', 0):.2%}")
    
    # 检查每个流的状态
    for stream_name, stream_status in status.get('connection_status', {}).items():
        print(f"\n流 {stream_name}:")
        print(f"  连接状态: {'已连接' if stream_status['is_connected'] else '未连接'}")
        print(f"  数据计数: {stream_status['data_count']}")
        print(f"  最后消息时间: {stream_status['last_message_time']}")
        print(f"  是否卡顿: {'是' if stream_status['stale'] else '否'}")
        print(f"  重连次数: {stream_status['reconnect_attempts']}")


async def diagnose_binance_client():
    """诊断Binance客户端"""
    print("\n=== Binance客户端诊断 ===")
    
    try:
        # 检查是否初始化
        if binance_client.client is None:
            print("❌ Binance客户端未初始化或使用Mock模式")
            
            # 检查环境变量
            env_debug = os.getenv("ENVIRONMENT") == "development"
            print(f"ENVIRONMENT环境变量: {os.getenv('ENVIRONMENT', 'None')}")
            print(f"是否开发模式: {env_debug}")
            
            # 尝试获取价格（会使用Mock数据）
            price = await binance_client.get_current_price()
            print(f"当前价格 (可能是Mock): ${price:,.2f}")
        else:
            print("✅ Binance客户端已初始化")
            price = await binance_client.get_current_price()
            print(f"当前BTC价格: ${price:,.2f}")
            
    except Exception as e:
        print(f"❌ Binance客户端错误: {e}")


async def diagnose_analysis_engine():
    """诊断分析引擎"""
    print("\n=== 分析引擎诊断 ===")
    
    try:
        # 尝试执行市场分析
        print("正在执行市场分析...")
        signal = await analysis_engine.analyze_market()
        
        if signal:
            print("✅ 成功生成信号:")
            print(f"  方向: {signal['direction']}")
            print(f"  置信度: {signal['confidence_score']:.3f}")
            print(f"  入场价格: ${signal['entry_price']:,.2f}")
            print(f"  市场状态: {signal['market_state']}")
            print(f"  触发条件: {signal['confirmed_indicators']}")
        else:
            print("⚠️ 未生成信号 - 可能是市场条件不满足")
            
    except Exception as e:
        print(f"❌ 分析引擎错误: {e}")
        import traceback
        traceback.print_exc()


async def diagnose_database():
    """诊断数据库状态"""
    print("\n=== 数据库诊断 ===")
    
    try:
        # 检查数据库健康状态
        db_status = db.get_database_status()
        print(f"数据库状态: {db_status['status']}")
        print(f"响应时间: {db_status.get('response_time_ms', 0):.2f}ms")
        print(f"连接重试次数: {db_status.get('connection_retries', 0)}")
        
        # 检查最近的交易记录
        recent_trades = db.get_optimized_trade_history(limit=5)
        print(f"\n最近5条交易记录:")
        for trade in recent_trades:
            print(f"  ID {trade['id']}: {trade['direction']} @ ${trade['entry_price']:,.2f} "
                  f"({trade['status']}) - {trade['signal_timestamp']}")
            
        # 检查待处理交易
        pending_trades = db.get_pending_trades()
        print(f"\n待处理交易数量: {len(pending_trades)}")
        
    except Exception as e:
        print(f"❌ 数据库错误: {e}")
        import traceback
        traceback.print_exc()


async def diagnose_websocket_url_issue():
    """诊断WebSocket URL生成问题"""
    print("\n=== WebSocket URL诊断 ===")
    
    # 检查URL生成逻辑
    test_streams = ["kline_BTCUSDT_1m", "kline_BTCUSDT_5m", "kline_BTCUSDT_15m", "kline_BTCUSDT_30m"]
    
    for stream_name in test_streams:
        url = real_time_data_manager.ws_manager._get_stream_url(stream_name)
        print(f"{stream_name} -> {url}")
        
        # 检查URL是否正确（应该包含正确的时间间隔）
        if "1m" in stream_name and "@kline_1m" not in url:
            print(f"  ❌ URL错误: {stream_name}应该生成包含正确时间间隔的URL")
        elif "5m" in stream_name and "@kline_1m" in url:
            print(f"  ❌ URL错误: {stream_name}生成了1m的URL而不是5m")
        elif "15m" in stream_name and "@kline_1m" in url:
            print(f"  ❌ URL错误: {stream_name}生成了1m的URL而不是15m")
        elif "30m" in stream_name and "@kline_1m" in url:
            print(f"  ❌ URL错误: {stream_name}生成了1m的URL而不是30m")


async def main():
    """主诊断函数"""
    print("开始信号生成管道诊断...")
    print("=" * 60)
    
    # 初始化必要组件
    try:
        db.init_database()
        await binance_client.initialize()
        await real_time_data_manager.initialize()
        print("✅ 基础组件初始化完成")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 执行各项诊断
    await diagnose_websocket_url_issue()  # 首先检查这个关键问题
    await diagnose_websocket_streams()
    await diagnose_binance_client()
    await diagnose_database()
    await diagnose_analysis_engine()
    
    print("\n" + "=" * 60)
    print("诊断完成")


if __name__ == "__main__":
    asyncio.run(main())
