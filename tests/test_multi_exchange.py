#!/usr/bin/env python3
"""
Test script for multi-exchange limit order functionality
"""

import asyncio
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.order_manager import OrderManager
from quant.strategies.limit_order_executor import OrderContext
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_multi_exchange_orders():
    """Test order placement on both Binance and OKX"""
    print("=" * 60)
    print("MULTI-EXCHANGE ORDER TEST")
    print("=" * 60)

    # Test Binance
    try:
        print("\n--- Testing Binance ---")
        binance_manager = OrderManager("binance")
        binance_context = OrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.001,
            limit_price=20000.0,  # Example price
            position_side="LONG"
        )
        binance_result = await binance_manager.place_order(binance_context, "LIMIT")
        print(f"Binance Response: {binance_result}")
    except Exception as e:
        print(f"Binance Test Failed: {e}")

    # Test OKX
    try:
        print("\n--- Testing OKX ---")
        okx_manager = OrderManager("okx")
        okx_context = OrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=1,
            limit_price=20000.0,
            position_side="LONG"
        )
        okx_result = await okx_manager.place_order(okx_context, "LIMIT")
        print(f"OKX Response: {okx_result}")
    except Exception as e:
        print(f"OKX Test Failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_multi_exchange_orders())

