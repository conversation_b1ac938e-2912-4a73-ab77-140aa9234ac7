#!/usr/bin/env python3
"""
Analyze extension decision statistics from SQLite DB.

Outputs:
- Total extension trades
- Loss count and ratio among extension trades
- P&L summary stats (min, max, mean, percentiles)
- Worst-case loss percent in extension trades
"""
import sqlite3
import json
from pathlib import Path
import statistics

DB_PATH = Path("data/trading_system.db")

def load_extension_trades():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cur = conn.cursor()
    cur.execute(
        """
        SELECT id, decision_details, status, pnl, entry_price, exit_price, suggested_bet, direction, signal_timestamp, exit_timestamp
        FROM trade_history
        WHERE decision_details IS NOT NULL AND decision_details LIKE '%extension_decision%'
        """
    )
    rows = cur.fetchall()
    conn.close()
    trades = []
    for r in rows:
        try:
            d = json.loads(r["decision_details"]) if r["decision_details"] else {}
        except Exception:
            d = {}
        ext = d.get("extension_decision") or {}
        extend = bool(ext.get("extend", False))
        trades.append({
            "id": r["id"],
            "extend": extend,
            "status": r["status"],
            "pnl": r["pnl"],
            "entry_price": r["entry_price"],
            "exit_price": r["exit_price"],
            "suggested_bet": r["suggested_bet"],
            "direction": r["direction"],
            "signal_timestamp": r["signal_timestamp"],
            "exit_timestamp": r["exit_timestamp"],
        })
    return trades

def main():
    if not DB_PATH.exists():
        print("{\"error\": \"DB not found\"}")
        return
    ext_trades = [t for t in load_extension_trades() if t["extend"]]
    total = len(ext_trades)
    losses = [t for t in ext_trades if t["status"] == "LOSS"]
    loss_count = len(losses)
    loss_ratio = (loss_count / total) if total else 0.0

    # Compute loss percent (relative to suggested_bet) and price loss percent if possible
    loss_pcts = []
    for t in ext_trades:
        pnl = t["pnl"] or 0.0
        bet = t["suggested_bet"] or 0.0
        if bet > 0:
            loss_pcts.append(pnl / bet)
    # Negative values represent loss percentage

    def pct(v):
        return round(v * 100, 3)

    report = {
        "extension_trades": total,
        "loss_trades": loss_count,
        "loss_ratio": round(loss_ratio, 4),
        "pnl_pct_stats": None,
        "worst_case_loss_pct": None,
        "notes": "pnl_pct is pnl/suggested_bet; e.g., -0.01 means -1% of stake",
    }
    if loss_pcts:
        sorted_p = sorted(loss_pcts)
        report["pnl_pct_stats"] = {
            "min": pct(min(sorted_p)),
            "p25": pct(sorted_p[max(0, int(0.25*len(sorted_p))-1)]),
            "median": pct(sorted_p[max(0, int(0.5*len(sorted_p))-1)]),
            "p75": pct(sorted_p[max(0, int(0.75*len(sorted_p))-1)]),
            "max": pct(max(sorted_p)),
            "mean": pct(statistics.mean(sorted_p)),
        }
        # worst-case loss percent (most negative)
        report["worst_case_loss_pct"] = pct(min(sorted_p))
    print(json.dumps(report, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    import json
    main()
