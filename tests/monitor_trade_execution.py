#!/usr/bin/env python3
"""
实时交易执行监控脚本

监控配置优化后的交易执行效果，包括：
1. 信号执行率统计
2. 订单大小分布
3. 风险控制效果
4. 系统健康状态
"""

import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List
import sqlite3

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.database_manager import db
from quant.config_manager import config


def get_recent_trade_stats(hours: int = 24) -> Dict[str, Any]:
    """获取最近N小时的交易统计."""
    try:
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 查询最近的交易记录
        with sqlite3.connect('data/trading_system.db') as conn:
            cursor = conn.cursor()
            
            # 总信号数
            cursor.execute("""
                SELECT COUNT(*) FROM trade_history 
                WHERE signal_timestamp >= ?
            """, (cutoff_time,))
            total_signals = cursor.fetchone()[0]
            
            # 有订单执行信息的交易数
            cursor.execute("""
                SELECT COUNT(*) FROM trade_history 
                WHERE signal_timestamp >= ? 
                AND decision_details LIKE '%order_execution%'
            """, (cutoff_time,))
            executed_orders = cursor.fetchone()[0]
            
            # 不同状态的交易数
            cursor.execute("""
                SELECT status, COUNT(*) FROM trade_history 
                WHERE signal_timestamp >= ?
                GROUP BY status
            """, (cutoff_time,))
            status_counts = dict(cursor.fetchall())
            
            # 建议下单金额分布
            cursor.execute("""
                SELECT suggested_bet, COUNT(*) FROM trade_history 
                WHERE signal_timestamp >= ?
                GROUP BY CASE 
                    WHEN suggested_bet = 0 THEN '0'
                    WHEN suggested_bet < 1 THEN '<1'
                    WHEN suggested_bet < 5 THEN '1-5'
                    WHEN suggested_bet < 50 THEN '5-50'
                    ELSE '>50'
                END
            """, (cutoff_time,))
            bet_distribution = dict(cursor.fetchall())
            
            return {
                'total_signals': total_signals,
                'executed_orders': executed_orders,
                'execution_rate': executed_orders / max(total_signals, 1) * 100,
                'status_counts': status_counts,
                'bet_distribution': bet_distribution,
                'period_hours': hours
            }
            
    except Exception as e:
        print(f"Error getting trade stats: {e}")
        return {}


def analyze_config_effectiveness() -> Dict[str, Any]:
    """分析当前配置的有效性."""
    try:
        at_cfg = config.get("AUTO_TRADER", {})
        rm_cfg = config.get("RISK_MANAGEMENT", {})
        
        # 获取当前待处理的交易
        pending_trades = db.get_pending_trades()
        
        min_order = at_cfg.get("min_order_usdt", 100)
        conf_threshold = rm_cfg.get("confidence_threshold", 0.6)
        
        # 分析待处理交易的可执行性
        executable_count = 0
        size_blocked = 0
        conf_blocked = 0
        
        for trade in pending_trades:
            bet = trade.get("suggested_bet", 0)
            conf = trade.get("confidence_score", 0)
            
            size_ok = bet >= min_order
            conf_ok = conf >= conf_threshold
            
            if size_ok and conf_ok:
                executable_count += 1
            elif not size_ok:
                size_blocked += 1
            elif not conf_ok:
                conf_blocked += 1
        
        total_pending = len(pending_trades)
        
        return {
            'total_pending': total_pending,
            'executable_count': executable_count,
            'executable_rate': executable_count / max(total_pending, 1) * 100,
            'size_blocked': size_blocked,
            'conf_blocked': conf_blocked,
            'current_min_order': min_order,
            'current_conf_threshold': conf_threshold
        }
        
    except Exception as e:
        print(f"Error analyzing config effectiveness: {e}")
        return {}


def check_system_health() -> Dict[str, Any]:
    """检查系统健康状态."""
    try:
        # 检查最近的错误日志
        error_count = 0
        warning_count = 0
        
        try:
            with open('logs/error.log', 'r') as f:
                lines = f.readlines()[-100:]  # 最近100行
                for line in lines:
                    if '"level": "ERROR"' in line:
                        error_count += 1
                    elif '"level": "WARNING"' in line:
                        warning_count += 1
        except Exception:
            pass
        
        # 检查数据库连接
        db_healthy = True
        try:
            db.get_pending_trades()
        except Exception:
            db_healthy = False
        
        # 检查配置加载
        config_healthy = True
        try:
            config.get("AUTO_TRADER")
        except Exception:
            config_healthy = False
        
        return {
            'recent_errors': error_count,
            'recent_warnings': warning_count,
            'database_healthy': db_healthy,
            'config_healthy': config_healthy,
            'overall_healthy': db_healthy and config_healthy and error_count < 10
        }
        
    except Exception as e:
        print(f"Error checking system health: {e}")
        return {'overall_healthy': False}


def generate_recommendations(stats: Dict[str, Any], config_analysis: Dict[str, Any]) -> List[str]:
    """基于统计数据生成优化建议."""
    recommendations = []
    
    # 基于执行率的建议
    execution_rate = stats.get('execution_rate', 0)
    if execution_rate < 30:
        recommendations.append("⚠️ 执行率过低，建议进一步降低min_order_usdt")
    elif execution_rate > 80:
        recommendations.append("ℹ️ 执行率很高，可以考虑适当提高风险阈值")
    
    # 基于待处理交易分析的建议
    if config_analysis.get('size_blocked', 0) > config_analysis.get('conf_blocked', 0):
        recommendations.append("💰 主要因订单金额限制被阻止，建议降低min_order_usdt")
    elif config_analysis.get('conf_blocked', 0) > 0:
        recommendations.append("📊 主要因置信度限制被阻止，建议降低confidence_threshold")
    
    # 基于订单金额分布的建议
    bet_dist = stats.get('bet_distribution', {})
    small_orders = bet_dist.get('<1', 0) + bet_dist.get('1-5', 0)
    if small_orders > 0:
        recommendations.append("💡 存在小额信号，当前配置可以处理这些信号")
    
    if not recommendations:
        recommendations.append("✅ 当前配置看起来运行良好")
    
    return recommendations


def main():
    """主监控函数."""
    print("📊 交易执行监控报告")
    print("=" * 50)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 获取最近24小时统计
    print("📈 最近24小时交易统计")
    print("-" * 30)
    stats_24h = get_recent_trade_stats(24)
    
    if stats_24h:
        print(f"总信号数: {stats_24h['total_signals']}")
        print(f"执行订单数: {stats_24h['executed_orders']}")
        print(f"执行率: {stats_24h['execution_rate']:.1f}%")
        print(f"交易状态分布: {stats_24h['status_counts']}")
        print(f"下单金额分布: {stats_24h['bet_distribution']}")
    else:
        print("❌ 无法获取交易统计")
    
    print()
    
    # 分析当前配置有效性
    print("🔍 当前配置有效性分析")
    print("-" * 30)
    config_analysis = analyze_config_effectiveness()
    
    if config_analysis:
        print(f"待处理交易数: {config_analysis['total_pending']}")
        print(f"可执行交易数: {config_analysis['executable_count']}")
        print(f"可执行率: {config_analysis['executable_rate']:.1f}%")
        print(f"金额限制阻止: {config_analysis['size_blocked']}")
        print(f"置信度限制阻止: {config_analysis['conf_blocked']}")
        print(f"当前最小订单: {config_analysis['current_min_order']} USDT")
    else:
        print("❌ 无法分析配置有效性")
    
    print()
    
    # 检查系统健康状态
    print("🏥 系统健康检查")
    print("-" * 30)
    health = check_system_health()
    
    if health.get('overall_healthy'):
        print("✅ 系统运行正常")
    else:
        print("⚠️ 系统存在问题")
    
    print(f"最近错误数: {health.get('recent_errors', 'N/A')}")
    print(f"最近警告数: {health.get('recent_warnings', 'N/A')}")
    print(f"数据库健康: {'✅' if health.get('database_healthy') else '❌'}")
    print(f"配置健康: {'✅' if health.get('config_healthy') else '❌'}")
    
    print()
    
    # 生成优化建议
    print("💡 优化建议")
    print("-" * 30)
    recommendations = generate_recommendations(stats_24h, config_analysis)
    for rec in recommendations:
        print(rec)
    
    print()
    print("监控完成 ✅")


if __name__ == "__main__":
    main()