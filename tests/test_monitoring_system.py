#!/usr/bin/env python3
"""
监控系统集成测试

测试完整的服务级别监控系统：
1. 指标收集器
2. 健康检查器
3. 告警管理器
4. 仪表板服务器
"""

import asyncio
import time
from datetime import datetime

from quant.monitoring import MetricsCollector, DashboardServer, AlertManager, HealthChecker
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def simulate_trading_system():
    """模拟交易系统数据"""
    
    # 初始化监控组件
    config = {
        "collection_interval": 10,  # 10秒收集一次
        "evaluation_interval": 15,  # 15秒评估一次告警
        "retention_hours": 1        # 1小时数据保留
    }
    
    # 创建组件实例
    metrics_collector = MetricsCollector(config)
    health_checker = HealthChecker(config)
    alert_manager = AlertManager(config)
    dashboard_server = DashboardServer(
        metrics_collector, 
        health_checker, 
        alert_manager, 
        port=8888
    )
    
    logger.info("🚀 启动监控系统集成测试...")
    
    try:
        # 启动所有组件
        await metrics_collector.start()
        await health_checker.start()
        await alert_manager.start(metrics_collector)
        await dashboard_server.start()
        
        logger.info("✅ 监控系统所有组件启动完成")
        logger.info("🌐 仪表板访问地址: http://localhost:8888/dashboard")
        
        # 模拟系统运行
        simulation_duration = 120  # 运行2分钟
        start_time = time.time()
        
        logger.info(f"📊 开始模拟交易系统运行 ({simulation_duration}秒)...")
        
        iteration = 0
        while time.time() - start_time < simulation_duration:
            iteration += 1
            
            # 模拟业务指标变化
            signals_generated = 10 + (iteration % 5)
            signals_executed = max(0, signals_generated - (iteration % 3))
            total_pnl = 1000.0 + (iteration * 10) - (iteration % 10 * 50)  # 模拟PnL波动
            daily_pnl = 100.0 - (iteration % 20 * 20)  # 模拟日内盈亏
            active_positions = 3 + (iteration % 4)
            risk_score = 0.3 + (iteration % 10) * 0.02
            api_calls = 100 + iteration
            api_success_rate = 95.0 - (iteration % 15)  # 偶尔降低成功率
            
            # 收集业务指标
            metrics_collector.collect_business_metrics(
                signals_generated=signals_generated,
                signals_executed=signals_executed,
                total_pnl=total_pnl,
                daily_pnl=daily_pnl,
                active_positions=active_positions,
                risk_score=risk_score,
                api_calls_count=api_calls,
                api_success_rate=api_success_rate
            )
            
            # 模拟服务指标
            for service_name in ["market_analysis", "risk_manager", "auto_trader"]:
                response_time = 50 + (iteration % 10) * 10  # 模拟响应时间变化
                request_count = 20 + iteration
                error_count = iteration % 15  # 偶尔出现错误
                
                metrics_collector.collect_service_metrics(
                    service_name=service_name,
                    response_time_ms=response_time,
                    request_count=request_count,
                    error_count=error_count,
                    custom_metrics={
                        "queue_size": iteration % 20,
                        "cache_hit_rate": 85.0 + (iteration % 10)
                    }
                )
            
            # 显示进度
            if iteration % 5 == 0:
                elapsed = time.time() - start_time
                logger.info(f"⏱️  运行进度: {elapsed:.1f}s / {simulation_duration}s")
                
                # 显示最新指标
                latest_metrics = metrics_collector.get_latest_metrics()
                business_metrics = latest_metrics.get("business_metrics")
                if business_metrics:
                    logger.info(f"📈 业务指标 - 信号执行率: {business_metrics['execution_success_rate']:.1f}%, "
                              f"总PnL: {business_metrics['total_pnl']:.2f}, "
                              f"活跃持仓: {business_metrics['active_positions']}")
                
                # 显示告警状态
                active_alerts = await alert_manager.get_active_alerts()
                if active_alerts:
                    logger.warning(f"🚨 当前活跃告警: {len(active_alerts)} 个")
                    for alert in active_alerts[:3]:  # 只显示前3个
                        logger.warning(f"  - {alert['rule_name']}: {alert['message']}")
                else:
                    logger.info("✅ 当前无活跃告警")
            
            await asyncio.sleep(5)  # 每5秒更新一次
        
        logger.info("🏁 模拟运行完成")
        
        # 显示最终统计
        logger.info("📊 最终统计信息:")
        
        # 指标统计
        latest_metrics = metrics_collector.get_latest_metrics()
        logger.info(f"  - 收集到的指标类型: {len(latest_metrics.get('recent_metrics', {}))}")
        logger.info(f"  - 服务指标: {len(latest_metrics.get('service_metrics', {}))}")
        
        # 健康检查统计
        overall_health = await health_checker.get_overall_health()
        logger.info(f"  - 系统整体健康状态: {overall_health.get('status', 'unknown')}")
        logger.info(f"  - 健康检查组件数: {overall_health.get('summary', {}).get('total_components', 0)}")
        
        # 告警统计
        alert_stats = alert_manager.get_alert_statistics()
        logger.info(f"  - 告警规则总数: {alert_stats.get('total_rules', 0)}")
        logger.info(f"  - 活跃告警数量: {alert_stats.get('active_alerts', 0)}")
        logger.info(f"  - 严重程度分布: {alert_stats.get('severity_distribution', {})}")
        
        logger.info("💡 提示: 仪表板服务器继续运行，可通过浏览器访问 http://localhost:8888/dashboard 查看实时监控")
        logger.info("⏰ 系统将继续运行30秒，然后自动停止...")
        
        # 继续运行30秒让用户查看仪表板
        await asyncio.sleep(30)
        
    except KeyboardInterrupt:
        logger.info("⚠️  收到停止信号，正在关闭系统...")
    
    except Exception as e:
        logger.error(f"❌ 系统运行出错: {e}")
        
    finally:
        # 停止所有组件
        logger.info("🔄 正在停止监控系统...")
        
        await dashboard_server.stop()
        await alert_manager.stop()
        await health_checker.stop()
        await metrics_collector.stop()
        
        logger.info("✅ 监控系统已完全停止")


def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("🔧 交易系统监控平台 - 集成测试")
    logger.info("=" * 60)
    
    try:
        asyncio.run(simulate_trading_system())
    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        return 1
    
    logger.info("程序正常结束")
    return 0


if __name__ == "__main__":
    exit(main())