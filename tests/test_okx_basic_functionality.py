#!/usr/bin/env python3
"""
Basic functionality test for OKX BTCUSDT trading system

Tests core components without requiring full environment setup
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))


def test_okx_config():
    """Test OKX configuration loading"""
    print("\n" + "="*60)
    print("Testing OKX Configuration")
    print("="*60)
    
    try:
        config_path = Path("config/config.okx_btcusdt.json")
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Check for required sections
            required_sections = ["EXCHANGES", "TRADING_SYMBOLS", "RISK_MANAGEMENT", "AUTO_TRADER"]
            for section in required_sections:
                if section in config:
                    print(f"✅ {section} section found")
                else:
                    print(f"❌ {section} section missing")
            
            # Check OKX specific settings
            okx_config = config.get("EXCHANGES", {}).get("okx", {})
            if okx_config:
                print(f"✅ OKX exchange configuration found")
                
                # Check maker order settings
                maker_config = okx_config.get("MAKER_ORDER", {})
                if maker_config:
                    print(f"✅ Maker order configuration found:")
                    print(f"   - Enabled: {maker_config.get('enabled', False)}")
                    print(f"   - Trade mode: {maker_config.get('trade_mode', 'N/A')}")
                    print(f"   - Post-only: {maker_config.get('post_only', False)}")
                else:
                    print("❌ Maker order configuration missing")
                
                # Check BTCUSDT symbol config
                btc_config = okx_config.get("symbols", {}).get("BTCUSDT", {})
                if btc_config:
                    print(f"✅ BTCUSDT symbol configuration found")
                else:
                    print("❌ BTCUSDT symbol configuration missing")
            else:
                print("❌ OKX exchange configuration missing")
            
            return True
        else:
            print(f"❌ Configuration file not found: {config_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False


def test_okx_exchange_adapter():
    """Test OKX order adapter"""
    print("\n" + "="*60)
    print("Testing OKX Order Adapter")
    print("="*60)
    
    try:
        from quant.exchange.okx_exchange import OkxOrderAdapter
        
        adapter = OkxOrderAdapter()
        print("✅ OkxOrderAdapter initialized")
        
        # Test symbol conversion
        test_symbols = [
            ("BTC/USDT", "BTC-USDT"),
            ("ETH/USDT", "ETH-USDT"),
        ]
        
        for input_symbol, expected_base in test_symbols:
            result = adapter.convert_symbol(input_symbol)
            if expected_base in result:
                print(f"✅ Symbol conversion: {input_symbol} → {result}")
            else:
                print(f"❌ Symbol conversion failed: {input_symbol} → {result}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import OkxOrderAdapter: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing OkxOrderAdapter: {e}")
        return False


def test_okx_limit_order_executor():
    """Test OKX limit order executor structure"""
    print("\n" + "="*60)
    print("Testing OKX Limit Order Executor")
    print("="*60)
    
    try:
        from quant.strategies.okx_limit_order_executor import (
            OkxOrderContext,
            OrderStatus
        )
        
        # Test order context creation
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.001,
            position_side="long",
            trade_mode="isolated"
        )
        
        print("✅ OkxOrderContext created successfully")
        print(f"   - Symbol: {context.symbol}")
        print(f"   - Side: {context.side}")
        print(f"   - Quantity: {context.quantity}")
        print(f"   - Trade mode: {context.trade_mode}")
        print(f"   - Status: {context.status}")
        
        # Test OrderStatus enum
        statuses = [
            OrderStatus.PENDING,
            OrderStatus.PLACED,
            OrderStatus.FILLED,
            OrderStatus.CANCELLED,
            OrderStatus.FAILED
        ]
        
        print("✅ OrderStatus enum values available:")
        for status in statuses:
            print(f"   - {status.name}: {status.value}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import OKX components: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing OKX executor: {e}")
        return False


def test_okx_exchange_async_methods():
    """Test OKX exchange async method signatures"""
    print("\n" + "="*60)
    print("Testing OKX Exchange Async Methods")
    print("="*60)
    
    try:
        from quant.exchange.okx_exchange import OkxExchange
        import inspect
        
        # Don't actually initialize (would require credentials)
        # Just check the class structure
        
        required_methods = [
            "initialize",
            "place_limit_order",
            "place_market_order",
            "cancel_order",
            "get_order",
            "get_position",
            "get_current_price",
            "get_orderbook"
        ]
        
        for method_name in required_methods:
            if hasattr(OkxExchange, method_name):
                method = getattr(OkxExchange, method_name)
                if inspect.iscoroutinefunction(method):
                    print(f"✅ {method_name} is async method")
                else:
                    print(f"⚠️  {method_name} is not async")
            else:
                print(f"❌ {method_name} method not found")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import OkxExchange: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking OkxExchange: {e}")
        return False


def test_risk_management_config():
    """Test risk management configuration for OKX"""
    print("\n" + "="*60)
    print("Testing Risk Management Configuration")
    print("="*60)
    
    try:
        config_path = Path("config/config.okx_btcusdt.json")
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            risk_config = config.get("RISK_MANAGEMENT", {})
            
            required_params = [
                "MAX_DAILY_LOSS",
                "max_consecutive_losses",
                "min_position_size_usdt",
                "max_position_size_usdt",
                "max_leverage"
            ]
            
            for param in required_params:
                value = risk_config.get(param)
                if value is not None:
                    print(f"✅ {param}: {value}")
                else:
                    print(f"❌ {param}: Not configured")
            
            return True
        else:
            print(f"❌ Configuration file not found")
            return False
            
    except Exception as e:
        print(f"❌ Error checking risk configuration: {e}")
        return False


def main():
    """Run all basic functionality tests"""
    print("\n" + "="*60)
    print("OKX BTCUSDT Basic Functionality Tests")
    print(f"Timestamp: {datetime.utcnow().isoformat()}")
    print("="*60)
    
    tests = [
        ("Configuration", test_okx_config),
        ("Order Adapter", test_okx_exchange_adapter),
        ("Limit Order Executor", test_okx_limit_order_executor),
        ("Exchange Async Methods", test_okx_exchange_async_methods),
        ("Risk Management", test_risk_management_config)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All basic functionality tests passed!")
        return 0
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
