#!/usr/bin/env python3
"""
测试WebSocket修复是否生效
"""

import asyncio
import sys
import logging
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent))

# 启用调试日志
logging.getLogger('quant.real_time_data_manager').setLevel(logging.DEBUG)

from quant.real_time_data_manager import real_time_data_manager
from quant.binance_client import binance_client
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_websocket_streams():
    """测试WebSocket流连接和数据接收"""
    print("开始测试WebSocket流...")
    
    try:
        # 初始化组件
        await binance_client.initialize()
        await real_time_data_manager.initialize()
        
        # 启动一个30分钟的流进行测试
        print("启动30分钟K线流...")
        await real_time_data_manager.start_kline_stream("BTCUSDT", "30m")

        # 等待一段时间接收数据
        print("等待15秒接收数据...")
        for i in range(15):
            await asyncio.sleep(1)
            
            # 每5秒检查一次状态
            if i % 5 == 4:
                status = real_time_data_manager.get_stream_status()
                messages_processed = status.get('messages_processed', 0)
                print(f"第{i+1}秒: 已处理消息数 = {messages_processed}")
                
                # 检查流状态
                for stream_name, stream_status in status.get('connection_status', {}).items():
                    if 'kline_BTCUSDT_30m' in stream_name:
                        print(f"  流状态: 连接={stream_status['is_connected']}, "
                              f"数据计数={stream_status['data_count']}, "
                              f"卡顿={stream_status['stale']}")
        
        # 最终状态检查
        final_status = real_time_data_manager.get_stream_status()
        messages_processed = final_status.get('messages_processed', 0)
        
        print(f"\n测试结果:")
        print(f"总处理消息数: {messages_processed}")
        
        if messages_processed > 0:
            print("✅ WebSocket修复成功！正在接收数据")
        else:
            print("❌ 仍未接收到数据，可能需要进一步调试")
            
        # 显示详细的流状态
        for stream_name, stream_status in final_status.get('connection_status', {}).items():
            print(f"\n流 {stream_name}:")
            print(f"  连接状态: {stream_status['is_connected']}")
            print(f"  数据计数: {stream_status['data_count']}")
            print(f"  最后消息时间: {stream_status['last_message_time']}")
            print(f"  重连次数: {stream_status['reconnect_attempts']}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理
        try:
            await real_time_data_manager.stop()
        except:
            pass


async def main():
    await test_websocket_streams()


if __name__ == "__main__":
    asyncio.run(main())
