#!/usr/bin/env python3
"""
测试动态退出时间逻辑
验证基于盈亏状态的持仓时间调整是否正常工作
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.strategies.simple_exit_manager import SimpleExitManager
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class TestDynamicExitTime:
    """测试动态退出时间功能"""
    
    def __init__(self):
        self.exit_manager = None
        self.mock_binance_client = None
        
    async def setup(self):
        """设置测试环境"""
        # 创建SimpleExitManager实例
        self.exit_manager = SimpleExitManager()
        
        # 验证配置是否正确加载
        logger.info("=== 配置验证 ===")
        logger.info(f"动态退出启用: {self.exit_manager.use_dynamic_exit}")
        logger.info(f"盈利持仓时间: {self.exit_manager.profit_hold_minutes} 分钟")
        logger.info(f"亏损持仓时间: {self.exit_manager.loss_hold_minutes} 分钟")
        logger.info(f"最小持仓时间: {self.exit_manager.min_hold_minutes} 分钟")
        
        # Mock binance_client
        self.mock_binance_client = AsyncMock()
        
    async def test_profit_scenario(self):
        """测试盈利场景：应该持仓20分钟"""
        logger.info("\n=== 测试盈利场景 ===")
        
        # 准备测试数据
        trade_id = 1001
        entry_price = 50000.0
        current_price = 51000.0  # 2%盈利
        
        trade_data = {
            "id": trade_id,
            "entry_price": entry_price,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 100.0,
            "signal_timestamp": datetime.utcnow()
        }
        
        # 添加持仓到管理器
        self.exit_manager.add_position(trade_id, trade_data)
        
        # 获取初始退出时间
        exit_info = self.exit_manager._pending_exits.get(trade_id)
        if exit_info:
            initial_exit_time = exit_info["planned_exit_time"]
            signal_time = exit_info["signal_time"]
            
            logger.info(f"交易ID: {trade_id}")
            logger.info(f"入场价格: ${entry_price:,.2f}")
            logger.info(f"当前价格: ${current_price:,.2f} (盈利 {(current_price - entry_price) / entry_price:.2%})")
            logger.info(f"信号时间: {signal_time.isoformat()}")
            logger.info(f"初始退出时间: {initial_exit_time.isoformat()}")
            
            # Mock get_current_price 返回盈利价格
            with patch('quant.binance_client.binance_client.get_current_price', new_callable=AsyncMock) as mock_price:
                mock_price.return_value = current_price
                
                # 手动触发一次检查
                await self.exit_manager._check_exits()
                
                # 获取更新后的退出时间
                updated_exit_info = self.exit_manager._pending_exits.get(trade_id)
                if updated_exit_info:
                    updated_exit_time = updated_exit_info["planned_exit_time"]
                    expected_exit_time = signal_time + timedelta(minutes=self.exit_manager.profit_hold_minutes)
                    
                    logger.info(f"更新后退出时间: {updated_exit_time.isoformat()}")
                    logger.info(f"预期退出时间: {expected_exit_time.isoformat()}")
                    
                    # 验证退出时间是否为20分钟
                    time_diff = (updated_exit_time - signal_time).total_seconds() / 60
                    logger.info(f"实际持仓时间: {time_diff:.1f} 分钟")
                    
                    if abs(time_diff - 20.0) < 0.1:
                        logger.info("✅ 盈利场景测试通过：持仓时间正确设置为20分钟")
                        return True
                    else:
                        logger.error(f"❌ 盈利场景测试失败：持仓时间应为20分钟，实际为{time_diff:.1f}分钟")
                        return False
        
        logger.error("❌ 无法获取交易信息")
        return False
    
    async def test_loss_scenario(self):
        """测试亏损场景：应该持仓10分钟"""
        logger.info("\n=== 测试亏损场景 ===")
        
        # 准备测试数据
        trade_id = 1002
        entry_price = 50000.0
        current_price = 49000.0  # 2%亏损
        
        trade_data = {
            "id": trade_id,
            "entry_price": entry_price,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 100.0,
            "signal_timestamp": datetime.utcnow()
        }
        
        # 添加持仓到管理器
        self.exit_manager.add_position(trade_id, trade_data)
        
        # 获取初始退出时间
        exit_info = self.exit_manager._pending_exits.get(trade_id)
        if exit_info:
            initial_exit_time = exit_info["planned_exit_time"]
            signal_time = exit_info["signal_time"]
            
            logger.info(f"交易ID: {trade_id}")
            logger.info(f"入场价格: ${entry_price:,.2f}")
            logger.info(f"当前价格: ${current_price:,.2f} (亏损 {(current_price - entry_price) / entry_price:.2%})")
            logger.info(f"信号时间: {signal_time.isoformat()}")
            logger.info(f"初始退出时间: {initial_exit_time.isoformat()}")
            
            # Mock get_current_price 返回亏损价格
            with patch('quant.binance_client.binance_client.get_current_price', new_callable=AsyncMock) as mock_price:
                mock_price.return_value = current_price
                
                # 手动触发一次检查
                await self.exit_manager._check_exits()
                
                # 获取更新后的退出时间
                updated_exit_info = self.exit_manager._pending_exits.get(trade_id)
                if updated_exit_info:
                    updated_exit_time = updated_exit_info["planned_exit_time"]
                    expected_exit_time = signal_time + timedelta(minutes=self.exit_manager.loss_hold_minutes)
                    
                    logger.info(f"更新后退出时间: {updated_exit_time.isoformat()}")
                    logger.info(f"预期退出时间: {expected_exit_time.isoformat()}")
                    
                    # 验证退出时间是否为10分钟
                    time_diff = (updated_exit_time - signal_time).total_seconds() / 60
                    logger.info(f"实际持仓时间: {time_diff:.1f} 分钟")
                    
                    if abs(time_diff - 10.0) < 0.1:
                        logger.info("✅ 亏损场景测试通过：持仓时间正确设置为10分钟")
                        return True
                    else:
                        logger.error(f"❌ 亏损场景测试失败：持仓时间应为10分钟，实际为{time_diff:.1f}分钟")
                        return False
        
        logger.error("❌ 无法获取交易信息")
        return False
    
    async def test_dynamic_adjustment(self):
        """测试动态调整：从亏损变为盈利"""
        logger.info("\n=== 测试动态调整场景 ===")
        
        # 准备测试数据
        trade_id = 1003
        entry_price = 50000.0
        loss_price = 49500.0   # 初始亏损1%
        profit_price = 50500.0  # 后续盈利1%
        
        trade_data = {
            "id": trade_id,
            "entry_price": entry_price,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 100.0,
            "signal_timestamp": datetime.utcnow()
        }
        
        # 添加持仓到管理器
        self.exit_manager.add_position(trade_id, trade_data)
        
        exit_info = self.exit_manager._pending_exits.get(trade_id)
        if exit_info:
            signal_time = exit_info["signal_time"]
            
            logger.info(f"交易ID: {trade_id}")
            logger.info(f"入场价格: ${entry_price:,.2f}")
            
            # 第一次检查：亏损状态
            with patch('quant.binance_client.binance_client.get_current_price', new_callable=AsyncMock) as mock_price:
                mock_price.return_value = loss_price
                logger.info(f"\n第一次检查 - 亏损状态")
                logger.info(f"当前价格: ${loss_price:,.2f} (亏损 {(loss_price - entry_price) / entry_price:.2%})")
                
                await self.exit_manager._check_exits()
                
                exit_info_1 = self.exit_manager._pending_exits.get(trade_id)
                exit_time_1 = exit_info_1["planned_exit_time"]
                time_diff_1 = (exit_time_1 - signal_time).total_seconds() / 60
                logger.info(f"退出时间: {exit_time_1.isoformat()} (持仓 {time_diff_1:.1f} 分钟)")
            
            # 等待超过1分钟，模拟时间流逝
            self.exit_manager._pending_exits[trade_id]["last_pnl_check"] = datetime.utcnow() - timedelta(seconds=61)
            
            # 第二次检查：盈利状态
            with patch('quant.binance_client.binance_client.get_current_price', new_callable=AsyncMock) as mock_price:
                mock_price.return_value = profit_price
                logger.info(f"\n第二次检查 - 盈利状态")
                logger.info(f"当前价格: ${profit_price:,.2f} (盈利 {(profit_price - entry_price) / entry_price:.2%})")
                
                await self.exit_manager._check_exits()
                
                exit_info_2 = self.exit_manager._pending_exits.get(trade_id)
                exit_time_2 = exit_info_2["planned_exit_time"]
                time_diff_2 = (exit_time_2 - signal_time).total_seconds() / 60
                logger.info(f"退出时间: {exit_time_2.isoformat()} (持仓 {time_diff_2:.1f} 分钟)")
            
            # 验证动态调整
            if abs(time_diff_1 - 10.0) < 0.1 and abs(time_diff_2 - 20.0) < 0.1:
                logger.info("✅ 动态调整测试通过：持仓时间从10分钟调整为20分钟")
                return True
            else:
                logger.error(f"❌ 动态调整测试失败：亏损时应为10分钟(实际{time_diff_1:.1f})，盈利时应为20分钟(实际{time_diff_2:.1f})")
                return False
        
        logger.error("❌ 无法获取交易信息")
        return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("=" * 60)
        logger.info("开始测试动态退出时间功能")
        logger.info("=" * 60)
        
        await self.setup()
        
        results = []
        
        # 测试1：盈利场景
        result1 = await self.test_profit_scenario()
        results.append(("盈利场景", result1))
        
        # 清理
        self.exit_manager._pending_exits.clear()
        
        # 测试2：亏损场景
        result2 = await self.test_loss_scenario()
        results.append(("亏损场景", result2))
        
        # 清理
        self.exit_manager._pending_exits.clear()
        
        # 测试3：动态调整
        result3 = await self.test_dynamic_adjustment()
        results.append(("动态调整", result3))
        
        # 输出测试结果汇总
        logger.info("\n" + "=" * 60)
        logger.info("测试结果汇总")
        logger.info("=" * 60)
        
        all_passed = True
        for test_name, passed in results:
            status = "✅ 通过" if passed else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if not passed:
                all_passed = False
        
        if all_passed:
            logger.info("\n🎉 所有测试通过！动态退出时间功能正常工作")
            logger.info("策略优化成功：")
            logger.info("- 信号预测正确时（盈利）：持仓20分钟，让利润充分奔跑")
            logger.info("- 信号预测错误时（亏损）：持仓10分钟，快速止损减少损失")
        else:
            logger.error("\n⚠️ 部分测试失败，请检查实现")
        
        return all_passed


async def main():
    """主函数"""
    tester = TestDynamicExitTime()
    success = await tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
