#!/usr/bin/env python3
"""
Binance USDⓈ-M Futures Auto-Trade Smoke Test

Objectives
- Verify API connectivity
- Place a tiny market order (BTCUSDT) using keys from config/config.json
- Query order/position status
- Exercise error handling paths

Safety
- Default to testnet if environment DEBUG or config DEBUG is true
- Notional set to a very small amount (100 USDT) by default

Run
  python tests/test_binance_auto_trade.py

Note
- This script uses the project runtime (main.py loads ConfigManager). If run standalone,
  it will load config/config.json from project root.
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from datetime import datetime

# Add project root
ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(ROOT))

from quant.config_manager import config as cfg
from quant.binance_client import BinanceClient
from quant.utils.logger import get_logger

logger = get_logger(__name__)

TEST_SYMBOL = "BTCUSDT"
TEST_NOTIONAL_USDT = float(os.getenv("BINANCE_TEST_NOTIONAL", "100"))

async def run():
    report = {
        "start": datetime.utcnow().isoformat() + "Z",
        "steps": [],
        "errors": [],
        "recommendations": []
    }
    client = BinanceClient()

    # Step 1: Load keys
    step = {"step": "load_config", "ok": False}
    try:
        c = cfg.get_platform_config("binance")
        step["has_access_key"] = bool(c.get("access_key"))
        step["has_secret_key"] = bool(c.get("secret_key"))
        step["ok"] = step["has_access_key"] and step["has_secret_key"]
        if not step["ok"]:
            raise RuntimeError("Missing binance API keys in config/config.json -> PLATFORMS.binance")
    except Exception as e:
        report["errors"].append(f"config_error: {e}")
    finally:
        report["steps"].append(step)

    # Step 2: Initialize client
    step = {"step": "initialize_client", "ok": False}
    try:
        await client.initialize()
        step["ok"] = True
    except Exception as e:
        step["error"] = str(e)
        report["errors"].append(f"init_error: {e}")
        report["recommendations"].append("Check network, API keys, and whether futures permissions are enabled.")
    finally:
        report["steps"].append(step)

    # If init failed, try mock path for connectivity checks only
    # Step 3: Connectivity checks
    step = {"step": "connectivity", "ok": False}
    try:
        price = await client.get_current_price()
        step["current_price"] = price
        klines = await client.get_klines(interval="1m", limit=5)
        step["klines_cnt"] = len(klines)
        step["ok"] = True
    except Exception as e:
        step["error"] = str(e)
        report["errors"].append(f"connectivity_error: {e}")
        report["recommendations"].append("Verify Internet access, API service status, and firewall/proxy settings.")
    finally:
        report["steps"].append(step)

    # Step 4: Place a tiny market order on futures (BUY then SELL to neutralize exposure)
    # If client is in mock mode, this will simulate
    order_results = {"buy": None, "sell": None}

    for side in ("BUY", "SELL"):
        step = {"step": f"place_{side.lower()}_order", "ok": False, "notional_usdt": TEST_NOTIONAL_USDT}
        try:
            resp = await client.place_market_order_futures(TEST_SYMBOL, side, TEST_NOTIONAL_USDT)
            step["response"] = resp
            step["simulated"] = bool(resp.get("simulated"))
            # Basic sanity
            if "error" in resp:
                raise RuntimeError(resp["error"])
            step["ok"] = True
            order_results["buy" if side == "BUY" else "sell"] = resp
        except Exception as e:
            step["error"] = str(e)
            report["errors"].append(f"order_error_{side.lower()}: {e}")
            if "Margin is insufficient" in step.get("error", ""):
                report["recommendations"].append("Ensure futures wallet has sufficient USDT balance or use testnet.")
        finally:
            report["steps"].append(step)

    # Step 5: Basic order status check (limited by client abstraction)
    # python-binance AsyncClient.futures_get_order would be ideal; here we record responses
    step = {"step": "order_status_check", "ok": False}
    try:
        step["buy"] = order_results["buy"]
        step["sell"] = order_results["sell"]
        step["ok"] = True
    except Exception as e:
        step["error"] = str(e)
        report["errors"].append(f"status_error: {e}")
    finally:
        report["steps"].append(step)

    # Step 6: Close client
    step = {"step": "close_client", "ok": False}
    try:
        await client.close()
        step["ok"] = True
    except Exception as e:
        step["error"] = str(e)
        report["errors"].append(f"close_error: {e}")
    finally:
        report["steps"].append(step)

    report["end"] = datetime.utcnow().isoformat() + "Z"
    print(json.dumps(report, ensure_ascii=False, indent=2, default=str))

if __name__ == "__main__":
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        print("Interrupted")
