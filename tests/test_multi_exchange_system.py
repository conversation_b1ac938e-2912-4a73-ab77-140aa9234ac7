#!/usr/bin/env python3
"""
Multi-Exchange System Test Suite

Tests the multi-exchange trading system functionality including:
- Exchange factory pattern
- Switching between exchanges
- Simultaneous operations on multiple exchanges
- Error handling and recovery
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any
import json

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.exchange.exchange_factory import ExchangeFactory
from quant.exchange.exchange_interface import (
    ExchangeInterface, OrderRequest, OrderType, OrderSide, PositionSide
)
from quant.utils.logger import get_logger
from quant.config_manager import config

logger = get_logger(__name__)


class MultiExchangeTestSuite:
    """Test suite for multi-exchange functionality"""
    
    def __init__(self):
        self.exchanges: Dict[str, ExchangeInterface] = {}
        self.test_results = []
        
    async def setup(self):
        """Setup test environment"""
        logger.info("=" * 60)
        logger.info("Multi-Exchange System Test Suite")
        logger.info("=" * 60)
        
        # Get configured exchanges
        configured = ExchangeFactory._get_configured_exchanges()
        logger.info(f"Configured exchanges: {configured}")
        
        # Supported exchanges
        supported = ExchangeFactory.get_supported_exchanges()
        logger.info(f"Supported exchanges: {supported}")
        
        return True
    
    async def test_exchange_factory(self):
        """Test exchange factory pattern"""
        logger.info("\n=== Testing Exchange Factory ===")
        
        try:
            # Test getting default exchange
            default_exchange = await ExchangeFactory.get_exchange()
            logger.info(f"✓ Default exchange: {default_exchange.get_exchange_name()}")
            
            # Test getting specific exchange (OKX)
            if ExchangeFactory.is_exchange_supported("okx"):
                okx = await ExchangeFactory.get_exchange("okx")
                logger.info(f"✓ OKX exchange created: {okx.get_exchange_name()}")
                self.exchanges["okx"] = okx
            
            # Test singleton pattern
            okx2 = await ExchangeFactory.get_exchange("okx")
            if okx2 is self.exchanges.get("okx"):
                logger.info("✓ Singleton pattern working (same instance returned)")
            
            # Test force new instance
            okx3 = await ExchangeFactory.get_exchange("okx", force_new=True)
            if okx3 is not self.exchanges.get("okx"):
                logger.info("✓ Force new instance working")
            
            self.test_results.append(("Exchange Factory", True))
            return True
            
        except Exception as e:
            logger.error(f"❌ Exchange factory test failed: {e}")
            self.test_results.append(("Exchange Factory", False))
            return False
    
    async def test_okx_connection(self):
        """Test OKX exchange connection"""
        logger.info("\n=== Testing OKX Connection ===")
        
        try:
            # Get OKX exchange
            okx = await ExchangeFactory.get_exchange("okx")
            
            # Test getting current price
            symbol = "BTCUSDT"
            price = await okx.get_current_price(symbol)
            if price > 0:
                logger.info(f"✓ OKX current price for {symbol}: ${price:,.2f}")
            else:
                logger.warning(f"⚠ Failed to get price for {symbol}")
            
            # Test getting balance
            balances = await okx.get_balance()
            logger.info(f"✓ OKX balance retrieved: {len(balances)} assets")
            for balance in balances[:3]:  # Show first 3
                logger.info(f"  - {balance.asset}: {balance.free:.4f} free, {balance.locked:.4f} locked")
            
            # Test getting positions
            positions = await okx.get_positions()
            logger.info(f"✓ OKX positions retrieved: {len(positions)} positions")
            for pos in positions[:3]:  # Show first 3
                logger.info(f"  - {pos.symbol} {pos.side.value}: {pos.quantity} @ ${pos.entry_price:,.2f}")
            
            # Test getting open orders
            orders = await okx.get_open_orders()
            logger.info(f"✓ OKX open orders: {len(orders)} orders")
            
            self.test_results.append(("OKX Connection", True))
            return True
            
        except Exception as e:
            logger.error(f"❌ OKX connection test failed: {e}")
            self.test_results.append(("OKX Connection", False))
            return False
    
    async def test_market_data(self):
        """Test market data retrieval"""
        logger.info("\n=== Testing Market Data ===")
        
        try:
            okx = await ExchangeFactory.get_exchange("okx")
            symbol = "BTCUSDT"
            
            # Test orderbook
            orderbook = await okx.get_orderbook(symbol, depth=5)
            if orderbook and "bids" in orderbook and "asks" in orderbook:
                logger.info(f"✓ Orderbook for {symbol}:")
                logger.info(f"  Best bid: ${orderbook['bids'][0][0]:,.2f} x {orderbook['bids'][0][1]:.4f}")
                logger.info(f"  Best ask: ${orderbook['asks'][0][0]:,.2f} x {orderbook['asks'][0][1]:.4f}")
                spread = orderbook['asks'][0][0] - orderbook['bids'][0][0]
                logger.info(f"  Spread: ${spread:.2f}")
            
            # Test klines
            klines = await okx.get_klines(symbol, "30m", limit=5)
            if klines:
                logger.info(f"✓ Retrieved {len(klines)} klines")
                latest = klines[-1]
                logger.info(f"  Latest: O=${float(latest[1]):,.2f} H=${float(latest[2]):,.2f} L=${float(latest[3]):,.2f} C=${float(latest[4]):,.2f}")
            
            # Test 24hr ticker
            ticker = await okx.get_24hr_ticker(symbol)
            if ticker:
                logger.info(f"✓ 24hr ticker for {symbol}:")
                logger.info(f"  Price: ${ticker.get('price', 0):,.2f}")
                logger.info(f"  24h change: {ticker.get('price_change_percent', 0):.2f}%")
                logger.info(f"  24h volume: {ticker.get('volume', 0):,.2f}")
            
            self.test_results.append(("Market Data", True))
            return True
            
        except Exception as e:
            logger.error(f"❌ Market data test failed: {e}")
            self.test_results.append(("Market Data", False))
            return False
    
    async def test_order_simulation(self):
        """Test order placement (simulation only)"""
        logger.info("\n=== Testing Order Simulation ===")
        logger.warning("⚠ This is a simulation only - no real orders will be placed")
        
        try:
            okx = await ExchangeFactory.get_exchange("okx")
            
            # Get current price for reference
            symbol = "BTCUSDT"
            current_price = await okx.get_current_price(symbol)
            
            if current_price <= 0:
                logger.warning("Cannot simulate orders without current price")
                return False
            
            # Simulate a limit buy order (10% below market)
            buy_price = current_price * 0.9
            order_request = OrderRequest(
                symbol=symbol,
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                quantity=0.001,  # Small quantity
                price=buy_price,
                position_side=PositionSide.LONG
            )
            
            logger.info(f"✓ Simulated order request:")
            logger.info(f"  Symbol: {order_request.symbol}")
            logger.info(f"  Side: {order_request.side.value}")
            logger.info(f"  Type: {order_request.order_type.value}")
            logger.info(f"  Quantity: {order_request.quantity}")
            logger.info(f"  Price: ${order_request.price:,.2f}")
            
            # Note: Not actually placing the order in test
            logger.info("✓ Order simulation successful (not executed)")
            
            self.test_results.append(("Order Simulation", True))
            return True
            
        except Exception as e:
            logger.error(f"❌ Order simulation failed: {e}")
            self.test_results.append(("Order Simulation", False))
            return False
    
    async def test_multi_exchange_operations(self):
        """Test operations across multiple exchanges"""
        logger.info("\n=== Testing Multi-Exchange Operations ===")
        
        try:
            # Get all configured exchanges
            exchanges = await ExchangeFactory.get_all_configured_exchanges()
            
            if len(exchanges) < 2:
                logger.info("ℹ Only one exchange configured, skipping multi-exchange test")
                self.test_results.append(("Multi-Exchange Ops", "N/A"))
                return True
            
            logger.info(f"✓ Retrieved {len(exchanges)} exchanges")
            
            # Get prices from all exchanges
            symbol = "BTCUSDT"
            prices = {}
            
            for name, exchange in exchanges.items():
                try:
                    price = await exchange.get_current_price(symbol)
                    prices[name] = price
                    logger.info(f"  {name}: ${price:,.2f}")
                except Exception as e:
                    logger.warning(f"  {name}: Failed to get price - {e}")
            
            # Compare prices if we have multiple
            if len(prices) > 1:
                price_list = list(prices.values())
                spread = max(price_list) - min(price_list)
                spread_pct = (spread / min(price_list)) * 100
                logger.info(f"✓ Price spread: ${spread:.2f} ({spread_pct:.3f}%)")
            
            self.test_results.append(("Multi-Exchange Ops", True))
            return True
            
        except Exception as e:
            logger.error(f"❌ Multi-exchange operations failed: {e}")
            self.test_results.append(("Multi-Exchange Ops", False))
            return False
    
    async def test_error_handling(self):
        """Test error handling and recovery"""
        logger.info("\n=== Testing Error Handling ===")
        
        try:
            # Test invalid exchange name
            try:
                await ExchangeFactory.get_exchange("invalid_exchange")
                logger.error("❌ Should have raised error for invalid exchange")
                error_handled = False
            except ValueError as e:
                logger.info(f"✓ Properly handled invalid exchange: {e}")
                error_handled = True
            
            # Test invalid symbol
            okx = await ExchangeFactory.get_exchange("okx")
            price = await okx.get_current_price("INVALID_SYMBOL_XYZ")
            if price == 0:
                logger.info("✓ Properly handled invalid symbol (returned 0)")
            
            # Test connection cleanup
            await ExchangeFactory.close_all()
            logger.info("✓ All connections closed successfully")
            
            self.test_results.append(("Error Handling", error_handled))
            return error_handled
            
        except Exception as e:
            logger.error(f"❌ Error handling test failed: {e}")
            self.test_results.append(("Error Handling", False))
            return False
    
    async def run_all_tests(self):
        """Run all tests"""
        try:
            await self.setup()
            
            # Run tests
            await self.test_exchange_factory()
            await self.test_okx_connection()
            await self.test_market_data()
            await self.test_order_simulation()
            await self.test_multi_exchange_operations()
            await self.test_error_handling()
            
            # Print results summary
            logger.info("\n" + "=" * 60)
            logger.info("Test Results Summary")
            logger.info("=" * 60)
            
            passed = 0
            failed = 0
            skipped = 0
            
            for test_name, result in self.test_results:
                if result == True:
                    status = "✅ PASSED"
                    passed += 1
                elif result == False:
                    status = "❌ FAILED"
                    failed += 1
                else:
                    status = "ℹ️ SKIPPED"
                    skipped += 1
                
                logger.info(f"{test_name:.<30} {status}")
            
            logger.info("-" * 60)
            logger.info(f"Total: {len(self.test_results)} tests")
            logger.info(f"Passed: {passed}, Failed: {failed}, Skipped: {skipped}")
            
            if failed == 0:
                logger.info("\n🎉 All tests passed! Multi-exchange system is working correctly.")
                return True
            else:
                logger.error(f"\n⚠️ {failed} tests failed. Please check the implementation.")
                return False
                
        except Exception as e:
            logger.error(f"Test suite error: {e}")
            return False
        finally:
            # Cleanup
            await ExchangeFactory.close_all()


async def main():
    """Main test entry point"""
    # Check if we should use a specific config
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
        logger.info(f"Using config file: {config_file}")
        # Load specific config if needed
    
    test_suite = MultiExchangeTestSuite()
    success = await test_suite.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
