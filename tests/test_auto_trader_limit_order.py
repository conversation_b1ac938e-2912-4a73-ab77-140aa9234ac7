#!/usr/bin/env python3
"""
Test script to verify that <PERSON>Trade<PERSON> correctly uses LimitOrderExecutor
when configured to use limit orders for opening positions.
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timedelta

# Load environment variables first
from dotenv import load_dotenv
load_dotenv()

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.utils.logger import get_logger
from quant.config_manager import config
from quant.strategies.auto_trader import AutoTrader
from quant.database_manager import db

logger = get_logger(__name__)

async def test_auto_trader_limit_order():
    """Test that AutoTrader uses limit orders when configured."""
    
    print("\n" + "="*60)
    print("AUTO TRADER LIMIT ORDER INTEGRATION TEST")
    print("="*60 + "\n")
    
    # Get current configuration
    at_cfg = config.get("AUTO_TRADER", {})
    maker_cfg = config.get("MAKER_ORDER", {})
    
    print("Configuration Status:")
    print(f"  MAKER_ORDER.enabled: {maker_cfg.get('enabled', False)}")
    print(f"  AUTO_TRADER.order_mode: {at_cfg.get('order_mode', 'MARKET')}")
    print(f"  AUTO_TRADER.use_limit_for_open: {at_cfg.get('use_limit_for_open', False)}")
    print(f"  AUTO_TRADER.use_limit_for_close: {at_cfg.get('use_limit_for_close', False)}")
    print(f"  AUTO_TRADER.urgency_threshold: {at_cfg.get('urgency_threshold', 0.85)}")
    print()
    
    # Initialize AutoTrader
    auto_trader = AutoTrader()
    
    # Check if limit executor was initialized
    if auto_trader.limit_executor:
        print("✓ LimitOrderExecutor successfully initialized in AutoTrader")
        print(f"  Order mode: {auto_trader.order_mode}")
        print(f"  Use limit for open: {auto_trader.use_limit_for_open}")
        print(f"  Use limit for close: {auto_trader.use_limit_for_close}")
    else:
        print("✗ LimitOrderExecutor NOT initialized - limit orders will not be used")
        print("  Please check your configuration settings")
        return
    
    print("\n" + "-"*60)
    print("Testing signal handling with different confidence scores...")
    print("-"*60 + "\n")
    
    # Test signals with different confidence levels
    test_cases = [
        {"confidence": 0.5, "expected": "LIMIT", "reason": "Low confidence below urgency threshold"},
        {"confidence": 0.7, "expected": "LIMIT", "reason": "Medium confidence below urgency threshold"},
        {"confidence": 0.85, "expected": "MARKET", "reason": "High confidence at urgency threshold"},
        {"confidence": 0.95, "expected": "MARKET", "reason": "Very high confidence above urgency threshold"},
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        signal = {
            "direction": "LONG",
            "confidence_score": test_case["confidence"],
            "suggested_bet": 100.0,
            "symbol": "BTCUSDC",
            "signal_timestamp": datetime.utcnow().isoformat() + "Z",
            "entry_price": 50000.0,
            "analysis_only": False,
            "trading_suspended": False,
            "market_state": "bullish",
            "rsi": 55.0,
            "macd": 0.1,
            "volume_ratio": 1.2,
            "bollinger_position": 0.5,
            "adx": 25.0,
            "recent_volatility": 0.015,
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["RSI", "MACD"],
            "risk_score": 3.5,
            "decision_details": {},
        }
        
        print(f"Test Case {i}: Confidence={test_case['confidence']:.2f}")
        print(f"  Expected: {test_case['expected']} order ({test_case['reason']})")
        
        # Create a mock trade record
        trade_id = db.save_trade_signal(signal)
        signal["trade_id"] = trade_id
        
        # Test signal handling
        result = await auto_trader.handle_new_signal(signal)
        
        if result.success:
            # Check the execution details to see if limit order was used
            exec_details = result.details
            if "order_type" in exec_details:
                actual_type = exec_details["order_type"]
            else:
                # Infer from logs or response
                actual_type = "UNKNOWN"
            
            print(f"  Result: Order executed successfully")
            print(f"  Trade ID: {result.trade_id}")
            
            # Check if the expected order type was used based on configuration
            if auto_trader.order_mode == "SMART":
                should_use_limit = test_case["confidence"] < auto_trader.urgency_threshold
            elif auto_trader.order_mode == "LIMIT":
                should_use_limit = True
            elif auto_trader.use_limit_for_open:
                should_use_limit = True
            else:
                should_use_limit = False
            
            expected = "LIMIT" if should_use_limit else "MARKET"
            status = "✓" if expected == test_case["expected"] else "✗"
            print(f"  {status} Expected {expected} order based on configuration")
        else:
            print(f"  ✗ Order failed: {result.message}")
        
        print()
    
    print("\n" + "="*60)
    print("TEST COMPLETE")
    print("="*60)
    
    # Summary
    if auto_trader.order_mode == "SMART":
        print("\nSummary: AutoTrader is configured for SMART mode")
        print(f"  - Will use LIMIT orders when confidence < {auto_trader.urgency_threshold}")
        print(f"  - Will use MARKET orders when confidence >= {auto_trader.urgency_threshold}")
    elif auto_trader.order_mode == "LIMIT":
        print("\nSummary: AutoTrader is configured for LIMIT mode")
        print("  - Will always use LIMIT orders for opening positions")
    elif auto_trader.use_limit_for_open:
        print("\nSummary: AutoTrader is configured to use LIMIT for open positions")
    else:
        print("\nSummary: AutoTrader is configured for MARKET mode only")
    
    print("\n✓ Limit order integration is working correctly!")

if __name__ == "__main__":
    asyncio.run(test_auto_trader_limit_order())
