#!/usr/bin/env python3
"""
OKX BTCUSDT Integration Test Script

Tests the complete OKX BTCUSDT maker order functionality including:
- OKX exchange connection
- BTCUSDT limit order execution
- Order status tracking
- Risk management integration
"""

import asyncio
import sys
import json
from datetime import datetime
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
load_dotenv()

from quant.config_manager import ConfigManager
from quant.exchange.okx_exchange import OkxExchange, OkxOrderAdapter
from quant.strategies.okx_limit_order_executor import (
    OkxLimitOrderExecutor,
    OkxOrderContext,
    OrderStatus
)
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class OkxIntegrationTester:
    """OKX BTCUSDT integration test suite"""
    
    def __init__(self, config_path: str = None):
        """Initialize test environment"""
        # Load configuration
        config_path = config_path or "config/config.okx_btcusdt.json"
        self.config = ConfigManager(config_path)
        
        # Initialize components
        self.exchange = OkxExchange()
        self.order_executor = OkxLimitOrderExecutor()
        self.order_adapter = OkxOrderAdapter()
        
        # Test results
        self.test_results = {
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "failures": [],
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"OKX Integration Tester initialized with config: {config_path}")
    
    async def run_all_tests(self):
        """Run all integration tests"""
        logger.info("=" * 60)
        logger.info("Starting OKX BTCUSDT Integration Tests")
        logger.info("=" * 60)
        
        # Test suite
        test_methods = [
            self.test_exchange_connection,
            self.test_symbol_conversion,
            self.test_get_current_price,
            self.test_get_orderbook,
            self.test_limit_order_simulation,
            self.test_order_cancellation,
            self.test_position_query,
            self.test_risk_parameters,
            self.test_maker_order_config,
            self.test_full_trading_cycle
        ]
        
        for test_method in test_methods:
            await self._run_single_test(test_method)
        
        # Print summary
        self._print_test_summary()
        
        # Save results
        self._save_test_results()
        
        return self.test_results
    
    async def _run_single_test(self, test_method):
        """Run a single test with error handling"""
        test_name = test_method.__name__
        self.test_results["tests_run"] += 1
        
        try:
            logger.info(f"\n▶ Running: {test_name}")
            result = await test_method()
            
            if result:
                self.test_results["tests_passed"] += 1
                logger.info(f"✅ PASSED: {test_name}")
            else:
                self.test_results["tests_failed"] += 1
                self.test_results["failures"].append(test_name)
                logger.error(f"❌ FAILED: {test_name}")
                
        except Exception as e:
            self.test_results["tests_failed"] += 1
            self.test_results["failures"].append(f"{test_name}: {str(e)}")
            logger.error(f"❌ ERROR in {test_name}: {e}")
    
    async def test_exchange_connection(self) -> bool:
        """Test 1: OKX exchange connection"""
        try:
            await self.exchange.initialize()
            
            # Test API connection
            exchange_info = await self.exchange.okx_client.get_exchange_info()
            if exchange_info[1]:  # Check for error
                logger.error(f"Failed to get exchange info: {exchange_info[1]}")
                return False
            
            logger.info("OKX exchange connection successful")
            return True
            
        except Exception as e:
            logger.error(f"Exchange connection test failed: {e}")
            return False
    
    async def test_symbol_conversion(self) -> bool:
        """Test 2: Symbol format conversion"""
        try:
            # Test conversions
            test_cases = [
                ("BTC/USDT", "BTC-USDT-SWAP"),
                ("ETH/USDT", "ETH-USDT-SWAP"),
                ("BTC/USDC", "BTC-USDC-SWAP")
            ]
            
            for input_symbol, expected_output in test_cases:
                output = self.order_adapter.convert_symbol(input_symbol)
                if output != expected_output:
                    logger.error(f"Symbol conversion failed: {input_symbol} -> {output} (expected {expected_output})")
                    return False
                logger.info(f"Symbol conversion OK: {input_symbol} -> {output}")
            
            return True
            
        except Exception as e:
            logger.error(f"Symbol conversion test failed: {e}")
            return False
    
    async def test_get_current_price(self) -> bool:
        """Test 3: Get current BTCUSDT price"""
        try:
            symbol = "BTC/USDT"
            price = await self.exchange.get_current_price(symbol)
            
            if price <= 0:
                logger.error(f"Invalid price returned: {price}")
                return False
            
            logger.info(f"Current {symbol} price: {price:.2f}")
            
            # Sanity check (BTC should be between $10k and $500k)
            if price < 10000 or price > 500000:
                logger.warning(f"Price seems unusual: {price}")
            
            return True
            
        except Exception as e:
            logger.error(f"Get price test failed: {e}")
            return False
    
    async def test_get_orderbook(self) -> bool:
        """Test 4: Get BTCUSDT orderbook"""
        try:
            symbol = "BTC/USDT"
            orderbook = await self.exchange.get_orderbook(symbol)
            
            if not orderbook:
                logger.error("Failed to get orderbook")
                return False
            
            # Check orderbook structure
            if "data" not in orderbook:
                logger.error("Invalid orderbook structure")
                return False
            
            data = orderbook["data"][0]
            asks = data.get("asks", [])
            bids = data.get("bids", [])
            
            if not asks or not bids:
                logger.error("Orderbook is empty")
                return False
            
            # Log best bid/ask
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            spread = best_ask - best_bid
            spread_bps = (spread / best_bid) * 10000
            
            logger.info(f"Orderbook - Bid: {best_bid:.2f}, Ask: {best_ask:.2f}, "
                       f"Spread: {spread:.2f} ({spread_bps:.2f} bps)")
            
            return True
            
        except Exception as e:
            logger.error(f"Orderbook test failed: {e}")
            return False
    
    async def test_limit_order_simulation(self) -> bool:
        """Test 5: Simulate limit order placement (dry run)"""
        try:
            # Create test order context
            context = OkxOrderContext(
                symbol="BTC/USDT",
                side="BUY",
                quantity=0.001,  # Small test quantity
                position_side="long",
                trade_mode="isolated"
            )
            
            # Calculate limit price
            current_price = await self.exchange.get_current_price(context.symbol)
            
            # Place buy order 10 bps below market (should not fill immediately)
            context.limit_price = current_price * 0.999
            context.client_order_id = f"TEST_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
            
            logger.info(f"Simulating limit order: {context.side} {context.quantity} "
                       f"{context.symbol} @ {context.limit_price:.2f}")
            
            # Note: In production test, you would actually place the order
            # For safety, we're just simulating here
            logger.info("Limit order simulation successful (dry run)")
            
            return True
            
        except Exception as e:
            logger.error(f"Limit order simulation failed: {e}")
            return False
    
    async def test_order_cancellation(self) -> bool:
        """Test 6: Test order cancellation flow"""
        try:
            # This would test cancellation in a real scenario
            # For safety, we're simulating
            
            logger.info("Testing order cancellation flow...")
            
            # Simulate cancellation
            test_order_id = "TEST_ORDER_123"
            test_symbol = "BTC/USDT"
            
            logger.info(f"Would cancel order {test_order_id} for {test_symbol}")
            logger.info("Order cancellation test successful (simulated)")
            
            return True
            
        except Exception as e:
            logger.error(f"Order cancellation test failed: {e}")
            return False
    
    async def test_position_query(self) -> bool:
        """Test 7: Query BTCUSDT position"""
        try:
            symbol = "BTC/USDT"
            position = await self.exchange.get_position(symbol)
            
            if position and "data" in position:
                logger.info(f"Position query successful for {symbol}")
                
                # Log position details if any
                if position["data"]:
                    for pos in position["data"]:
                        logger.info(f"Position: {pos.get('posSide')} "
                                   f"Size: {pos.get('pos', 0)} "
                                   f"Avg Price: {pos.get('avgPx', 0)}")
                else:
                    logger.info("No open positions")
            else:
                logger.warning("Position query returned no data")
            
            return True
            
        except Exception as e:
            logger.error(f"Position query test failed: {e}")
            return False
    
    async def test_risk_parameters(self) -> bool:
        """Test 8: Verify risk management parameters"""
        try:
            risk_config = self.config.get("RISK_MANAGEMENT", {})
            
            # Check critical risk parameters
            checks = {
                "MAX_DAILY_LOSS": (risk_config.get("MAX_DAILY_LOSS"), 1000.0),
                "max_consecutive_losses": (risk_config.get("max_consecutive_losses"), 3),
                "min_position_size_usdt": (risk_config.get("min_position_size_usdt"), 10.0),
                "max_position_size_usdt": (risk_config.get("max_position_size_usdt"), 1000.0)
            }
            
            all_good = True
            for param, (value, expected) in checks.items():
                if value is None:
                    logger.error(f"Risk parameter {param} not configured")
                    all_good = False
                else:
                    logger.info(f"Risk parameter {param}: {value} (expected: {expected})")
                    if value != expected:
                        logger.warning(f"Risk parameter {param} differs from expected")
            
            return all_good
            
        except Exception as e:
            logger.error(f"Risk parameters test failed: {e}")
            return False
    
    async def test_maker_order_config(self) -> bool:
        """Test 9: Verify OKX maker order configuration"""
        try:
            okx_config = self.config.get("EXCHANGES", {}).get("okx", {})
            maker_config = okx_config.get("MAKER_ORDER", {})
            
            # Check maker order settings
            required_settings = [
                "enabled",
                "price_strategy",
                "buy_offset_bps",
                "sell_offset_bps",
                "initial_timeout",
                "max_retries",
                "trade_mode"
            ]
            
            all_present = True
            for setting in required_settings:
                value = maker_config.get(setting)
                if value is None:
                    logger.error(f"Maker order setting '{setting}' not configured")
                    all_present = False
                else:
                    logger.info(f"Maker order {setting}: {value}")
            
            # Check BTCUSDT specific config
            btc_config = okx_config.get("symbols", {}).get("BTCUSDT", {})
            if btc_config:
                logger.info(f"BTCUSDT config: {btc_config}")
            else:
                logger.warning("BTCUSDT specific configuration not found")
            
            return all_present
            
        except Exception as e:
            logger.error(f"Maker order config test failed: {e}")
            return False
    
    async def test_full_trading_cycle(self) -> bool:
        """Test 10: Simulate full trading cycle (dry run)"""
        try:
            logger.info("Simulating full trading cycle...")
            
            # 1. Market analysis
            symbol = "BTC/USDT"
            current_price = await self.exchange.get_current_price(symbol)
            logger.info(f"1. Market Analysis - Current price: {current_price:.2f}")
            
            # 2. Signal generation (simulated)
            signal = {
                "type": "LONG",
                "confidence": 0.75,
                "symbol": symbol
            }
            logger.info(f"2. Signal Generated: {signal}")
            
            # 3. Risk assessment
            position_size = 0.001  # Small test size
            risk_check = position_size * current_price < 1000  # Max position check
            logger.info(f"3. Risk Assessment - Position size: {position_size} BTC "
                       f"(${position_size * current_price:.2f}), Risk check: {risk_check}")
            
            # 4. Order placement (simulated)
            limit_price = current_price * 0.999  # Buy 10 bps below market
            logger.info(f"4. Would place limit order: BUY {position_size} @ {limit_price:.2f}")
            
            # 5. Order monitoring (simulated)
            logger.info("5. Would monitor order for fills...")
            
            # 6. Position management (simulated)
            logger.info("6. Would manage position with stop-loss and take-profit")
            
            # 7. Settlement (simulated)
            logger.info("7. Would settle trade after exit")
            
            logger.info("Full trading cycle simulation completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Full trading cycle test failed: {e}")
            return False
    
    def _print_test_summary(self):
        """Print test execution summary"""
        logger.info("\n" + "=" * 60)
        logger.info("TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {self.test_results['tests_run']}")
        logger.info(f"Passed: {self.test_results['tests_passed']} ✅")
        logger.info(f"Failed: {self.test_results['tests_failed']} ❌")
        
        if self.test_results["failures"]:
            logger.error("\nFailed Tests:")
            for failure in self.test_results["failures"]:
                logger.error(f"  - {failure}")
        
        # Calculate pass rate
        if self.test_results["tests_run"] > 0:
            pass_rate = (self.test_results["tests_passed"] / self.test_results["tests_run"]) * 100
            logger.info(f"\nPass Rate: {pass_rate:.1f}%")
            
            if pass_rate >= 80:
                logger.info("🎉 OKX BTCUSDT integration tests PASSED!")
            else:
                logger.error("⚠️ OKX BTCUSDT integration tests need attention")
    
    def _save_test_results(self):
        """Save test results to file"""
        try:
            results_file = f"test_results/okx_integration_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
            Path("test_results").mkdir(exist_ok=True)
            
            with open(results_file, 'w') as f:
                json.dump(self.test_results, f, indent=2)
            
            logger.info(f"Test results saved to: {results_file}")
            
        except Exception as e:
            logger.error(f"Failed to save test results: {e}")


async def main():
    """Main test execution"""
    # Parse command line arguments
    config_path = sys.argv[1] if len(sys.argv) > 1 else None
    
    # Run tests
    tester = OkxIntegrationTester(config_path)
    results = await tester.run_all_tests()
    
    # Exit with appropriate code
    if results["tests_failed"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
