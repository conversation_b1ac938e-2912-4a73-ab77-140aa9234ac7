#!/usr/bin/env python3
"""
追踪平仓调用的测试
找出是谁在立即平仓测试交易
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path
import functools

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.binance_client import binance_client
from quant.utils.logger import get_logger

logger = get_logger(__name__)


def trace_calls(func_name):
    """装饰器：追踪函数调用"""
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger.warning(f"🔍 TRACE: {func_name} called with args={args}, kwargs={kwargs}")
            import traceback
            logger.warning(f"🔍 CALL STACK:\n{''.join(traceback.format_stack())}")
            result = await func(*args, **kwargs)
            logger.warning(f"🔍 TRACE: {func_name} returned {result}")
            return result
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger.warning(f"🔍 TRACE: {func_name} called with args={args}, kwargs={kwargs}")
            import traceback
            logger.warning(f"🔍 CALL STACK:\n{''.join(traceback.format_stack())}")
            result = func(*args, **kwargs)
            logger.warning(f"🔍 TRACE: {func_name} returned {result}")
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


async def test_exit_call_tracing():
    """追踪平仓调用的测试"""
    logger.info("=== 追踪平仓调用测试 ===")
    
    try:
        # 1. 安装追踪装饰器
        logger.info("1. 安装调用追踪器...")
        
        # 追踪BinanceClient的平仓相关方法
        original_place_order = binance_client.place_market_order_futures
        binance_client.place_market_order_futures = trace_calls("BinanceClient.place_market_order_futures")(original_place_order)
        
        # 追踪SimpleExitManager的平仓方法
        original_execute_exit = simple_exit_manager._execute_exit
        simple_exit_manager._execute_exit = trace_calls("SimpleExitManager._execute_exit")(original_execute_exit)
        
        # 追踪数据库更新方法
        original_update_trade = db.update_trade_result
        db.update_trade_result = trace_calls("DatabaseManager.update_trade_result")(original_update_trade)
        
        # 2. 清理环境
        simple_exit_manager._pending_exits.clear()
        simple_exit_manager._processing_locks.clear()
        
        # 3. 创建测试交易（使用测试价格）
        logger.info("2. 创建测试交易...")
        
        signal_time = datetime.utcnow()
        test_signal = {
            "signal_timestamp": signal_time.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 50000.0,  # 测试价格
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 20.0,
            "decision_details": {"test": "exit_call_tracing"}
        }
        
        db_id = db.save_trade_signal(test_signal)
        logger.info(f"创建测试交易，ID: {db_id}")
        
        # 4. 添加到SimpleExitManager
        logger.info("3. 添加到SimpleExitManager...")
        
        trade_data = {
            "id": db_id,
            "entry_price": 50000.0,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 20.0,
            "signal_timestamp": signal_time.isoformat()
        }
        
        simple_exit_manager.add_position(db_id, trade_data)
        
        # 检查是否正确添加
        if db_id in simple_exit_manager._pending_exits:
            exit_info = simple_exit_manager._pending_exits[db_id]
            logger.info(f"✅ 成功添加到SimpleExitManager")
            logger.info(f"  计划平仓时间: {exit_info['planned_exit_time']}")
            logger.info(f"  最小持仓时间: {exit_info['min_hold_minutes']} 分钟")
        else:
            logger.error("❌ 未能添加到SimpleExitManager")
            return
        
        # 5. 模拟开仓操作（这可能触发立即平仓）
        logger.info("4. 模拟开仓操作...")
        
        try:
            # 模拟auto_trader的开仓操作
            order_resp = await binance_client.place_market_order_futures(
                symbol="BTCUSDT",
                side="BUY",
                notional_usdt=20.0,
                position_side="LONG",
                reduce_only=False,
            )
            logger.info(f"开仓响应: {order_resp}")
        except Exception as e:
            logger.error(f"开仓失败: {e}")
        
        # 6. 等待一段时间，观察是否有平仓调用
        logger.info("5. 等待观察平仓调用...")
        
        for i in range(10):  # 等待10秒
            await asyncio.sleep(1)
            
            # 检查交易状态
            with db.get_session() as session:
                from quant.database_manager import TradeHistory
                trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
                
                if trade and trade.status != "PENDING":
                    logger.warning(f"❌ 交易在第{i+1}秒被平仓！")
                    logger.warning(f"  状态: {trade.status}")
                    logger.warning(f"  平仓价格: ${trade.exit_price:,.2f}")
                    logger.warning(f"  平仓时间: {trade.exit_timestamp}")
                    break
            
            # 检查是否还在SimpleExitManager队列中
            if db_id not in simple_exit_manager._pending_exits:
                logger.warning(f"❌ 交易在第{i+1}秒从队列中消失！")
                break
        else:
            logger.info("✅ 10秒内未发现异常平仓")
        
        # 7. 手动触发各种检查，看是否会触发平仓
        logger.info("6. 手动触发各种检查...")
        
        # 触发SimpleExitManager检查
        logger.info("  触发SimpleExitManager检查...")
        await simple_exit_manager._check_exits()
        
        # 检查settlement_checker（如果有的话）
        logger.info("  检查settlement_checker...")
        try:
            from quant.settlement_checker import settlement_checker
            # 不直接调用，只是检查配置
            logger.info(f"    settlement_checker配置: 合约持续时间={settlement_checker.contract_duration}")
        except Exception as e:
            logger.info(f"    settlement_checker检查失败: {e}")
        
        # 8. 最终状态检查
        logger.info("7. 最终状态检查...")
        
        with db.get_session() as session:
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            
            if trade:
                logger.info(f"  最终状态: {trade.status}")
                if trade.status != "PENDING":
                    signal_time_final = datetime.fromisoformat(trade.signal_timestamp) if isinstance(trade.signal_timestamp, str) else trade.signal_timestamp
                    exit_time_final = datetime.fromisoformat(trade.exit_timestamp) if isinstance(trade.exit_timestamp, str) else trade.exit_timestamp
                    
                    hold_duration = (exit_time_final - signal_time_final).total_seconds()
                    logger.warning(f"  持仓时长: {hold_duration:.3f} 秒")
                    
                    if hold_duration < 120:  # 少于2分钟
                        logger.warning(f"  🚨 测试交易保护失效！应该至少持仓2分钟")
        
        # 9. 恢复原始方法
        logger.info("8. 恢复原始方法...")
        binance_client.place_market_order_futures = original_place_order
        simple_exit_manager._execute_exit = original_execute_exit
        db.update_trade_result = original_update_trade
        
        # 清理测试数据
        simple_exit_manager._pending_exits.clear()
        
        with db.get_session() as session:
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ 追踪测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_simple_reproduction():
    """简单重现问题"""
    logger.info("=== 简单重现问题 ===")
    
    try:
        # 1. 创建测试交易
        signal_time = datetime.utcnow()
        test_signal = {
            "signal_timestamp": signal_time.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 50000.0,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 20.0,
            "decision_details": {"test": "simple_reproduction"}
        }
        
        db_id = db.save_trade_signal(test_signal)
        logger.info(f"创建测试交易，ID: {db_id}")
        
        # 2. 记录开始时间
        start_time = datetime.utcnow()
        
        # 3. 模拟开仓（这可能触发问题）
        try:
            order_resp = await binance_client.place_market_order_futures(
                symbol="BTCUSDT",
                side="BUY",
                notional_usdt=20.0,
                position_side="LONG",
                reduce_only=False,
            )
            logger.info(f"开仓成功: {order_resp}")
        except Exception as e:
            logger.error(f"开仓失败: {e}")
        
        # 4. 立即检查状态
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            
            if trade:
                logger.info(f"交易状态: {trade.status}")
                if trade.status != "PENDING":
                    logger.warning(f"🚨 交易在 {duration:.3f} 秒内被平仓！")
                    logger.warning(f"  开仓价格: ${trade.entry_price:,.2f}")
                    logger.warning(f"  平仓价格: ${trade.exit_price:,.2f}")
                    logger.warning(f"  这证实了问题的存在")
                else:
                    logger.info(f"✅ 交易仍为PENDING状态")
        
        # 清理
        with db.get_session() as session:
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ 简单重现测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_simple_reproduction())
    print("\n" + "="*60 + "\n")
    asyncio.run(test_exit_call_tracing())
