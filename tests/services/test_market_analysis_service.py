"""
Unit tests for MarketAnalysisService

Tests market analysis, signal generation, and trade execution workflows.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from quant.services.market_analysis_service import MarketAnalysisService


class TestMarketAnalysisService:
    """Test MarketAnalysisService functionality."""
    
    @pytest.fixture
    def service(self):
        """Create market analysis service for testing."""
        return MarketAnalysisService()
    
    @pytest.fixture
    def mock_signal(self):
        """Create mock signal for testing."""
        return {
            "signal_timestamp": "2025-08-16T10:00:00Z",
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 50000.0,
            "confidence_score": 0.75,
            "market_state": "trending_up",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"]
        }
    
    def test_service_initialization(self, service):
        """Test service initialization."""
        assert service.service_name == "market_analysis"
        assert not service.is_running
        assert service._last_signal_time is None
    
    @pytest.mark.asyncio
    async def test_initialize_and_cleanup(self, service):
        """Test service initialize and cleanup."""
        await service.initialize()
        # No specific initialization needed for this service
        
        await service.cleanup()
        # No specific cleanup needed for this service
    
    @pytest.mark.asyncio
    @patch('quant.services.market_analysis_service.analysis_engine')
    async def test_analyze_market_and_trade_no_signal(self, mock_analysis_engine, service):
        """Test market analysis when no signal is generated."""
        mock_analysis_engine.analyze_market = AsyncMock(return_value=None)
        
        result = await service.analyze_market_and_trade()
        
        assert result is None
        mock_analysis_engine.analyze_market.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('quant.services.market_analysis_service.analysis_engine')
    @patch('quant.services.market_analysis_service.risk_manager')
    @patch('quant.services.market_analysis_service.db')
    @patch('quant.services.market_analysis_service.binance_client')
    @patch('quant.services.market_analysis_service.notification_manager')
    @patch('quant.services.market_analysis_service.auto_trader')
    @patch('quant.services.market_analysis_service.simple_exit_manager')
    async def test_analyze_market_and_trade_success(self, 
                                                    mock_exit_manager,
                                                    mock_auto_trader,
                                                    mock_notification_manager,
                                                    mock_binance_client,
                                                    mock_db,
                                                    mock_risk_manager,
                                                    mock_analysis_engine,
                                                    service,
                                                    mock_signal):
        """Test successful market analysis and trade execution."""
        # Setup mocks
        mock_analysis_engine.analyze_market = AsyncMock(return_value=mock_signal.copy())
        
        # Mock risk manager
        mock_position_sizing = Mock()
        mock_position_sizing.position_size_usdt = 100.0
        mock_position_sizing.risk_level.value = "low"
        mock_position_sizing.notes = ["Risk within limits"]
        mock_risk_manager.calculate_position_size.return_value = mock_position_sizing
        
        # Mock database
        mock_db.save_trade_signal.return_value = 123
        
        # Mock Binance client
        mock_binance_client.get_current_price = AsyncMock(return_value=50100.0)
        
        # Mock auto trader
        mock_exec_result = Mock()
        mock_exec_result.success = True
        mock_exec_result.trade_id = 456
        mock_auto_trader.handle_new_signal = AsyncMock(return_value=mock_exec_result)
        
        # Execute test
        result = await service.analyze_market_and_trade()
        
        # Verify results
        assert result is not None
        assert result["suggested_bet"] == 100.0
        assert result["risk_level"] == "low"
        assert result["trade_id"] == 123
        assert result["current_price"] == 50100.0
        
        # Verify service calls
        mock_analysis_engine.analyze_market.assert_called_once()
        mock_risk_manager.calculate_position_size.assert_called_once()
        mock_db.save_trade_signal.assert_called_once()
        mock_binance_client.get_current_price.assert_called_once()
        mock_auto_trader.handle_new_signal.assert_called_once()
        mock_exit_manager.add_position.assert_called_once_with(456, {
            "id": 456,
            "entry_price": 50000.0,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 100.0,
            "signal_timestamp": "2025-08-16T10:00:00Z"
        })
    
    @pytest.mark.asyncio
    @patch('quant.services.market_analysis_service.analysis_engine')
    @patch('quant.services.market_analysis_service.risk_manager')
    async def test_analyze_market_and_trade_trading_suspended(self,
                                                              mock_risk_manager,
                                                              mock_analysis_engine,
                                                              service,
                                                              mock_signal):
        """Test market analysis when trading is suspended by risk management."""
        mock_analysis_engine.analyze_market = AsyncMock(return_value=mock_signal.copy())
        
        # Mock risk manager to suspend trading
        mock_position_sizing = Mock()
        mock_position_sizing.position_size_usdt = 0  # Suspended
        mock_position_sizing.risk_level.value = "high"
        mock_position_sizing.notes = ["Risk limits exceeded"]
        mock_risk_manager.calculate_position_size.return_value = mock_position_sizing
        
        result = await service.analyze_market_and_trade()
        
        assert result is not None
        assert result["trading_suspended"] is True
        assert result["suspension_reason"] == "Risk management blocked trade"
        assert result["suggested_bet"] == 0
    
    @pytest.mark.asyncio
    async def test_apply_risk_management_error_handling(self, service, mock_signal):
        """Test risk management error handling."""
        with patch('quant.services.market_analysis_service.risk_manager') as mock_risk_manager:
            mock_risk_manager.calculate_position_size.side_effect = Exception("Risk calculation failed")
            
            result = await service._apply_risk_management(mock_signal.copy())
            
            assert result["trading_suspended"] is True
            assert "Risk management error" in result["suspension_reason"]
    
    @pytest.mark.asyncio
    async def test_persist_signal_error_handling(self, service, mock_signal):
        """Test signal persistence error handling."""
        with patch('quant.services.market_analysis_service.db') as mock_db:
            mock_db.save_trade_signal.side_effect = Exception("Database error")
            
            # Should not raise exception
            await service._persist_signal(mock_signal.copy())
    
    @pytest.mark.asyncio
    async def test_add_market_context_error_handling(self, service, mock_signal):
        """Test market context addition error handling."""
        with patch('quant.services.market_analysis_service.binance_client') as mock_binance_client:
            mock_binance_client.get_current_price.side_effect = Exception("API error")
            
            result = await service._add_market_context(mock_signal.copy())
            
            # Should return original signal without market context
            assert result == mock_signal
    
    def test_get_health_status(self, service):
        """Test service health status."""
        health = service.get_health_status()
        
        assert health["service_name"] == "market_analysis"
        assert "running" in health
        assert "last_signal_time" in health
        assert "analysis_engine_status" in health
        assert health["last_signal_time"] is None  # No signals processed yet
    
    @pytest.mark.asyncio
    async def test_execute_safe_wrapper(self, service):
        """Test execute_safe wrapper functionality."""
        # Test successful operation
        async def success_operation():
            return {"result": "success"}
        
        result = await service.execute_safe("test_operation", success_operation)
        assert result == {"result": "success"}
        
        # Test failed operation
        async def failing_operation():
            raise Exception("Operation failed")
        
        result = await service.execute_safe("test_operation", failing_operation)
        assert result is None
        assert service._status.error_count > 0
        assert "Operation failed" in service._status.last_error


if __name__ == "__main__":
    pytest.main([__file__, "-v"])