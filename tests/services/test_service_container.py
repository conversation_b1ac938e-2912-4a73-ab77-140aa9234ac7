"""
Unit tests for ServiceContainer

Tests dependency injection, service lifecycle, and configuration management.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from quant.config_manager import ConfigManager
from quant.services.service_container import ServiceContainer, ServiceConfig
from quant.services.base_service import BaseService


class MockService(BaseService):
    """Mock service for testing."""
    
    def __init__(self, name: str = "mock", dependency_value: str = "default"):
        super().__init__(name)
        self.dependency_value = dependency_value
        self.initialized = False
        self.cleaned_up = False
    
    async def initialize(self):
        self.initialized = True
    
    async def cleanup(self):
        self.cleaned_up = True


class MockDependentService(BaseService):
    """Mock service with dependencies for testing."""
    
    def __init__(self, name: str = "dependent"):
        super().__init__(name)
        self.initialized = False
        self.cleaned_up = False
    
    async def initialize(self):
        self.initialized = True
    
    async def cleanup(self):
        self.cleaned_up = True


class TestServiceContainer:
    """Test ServiceContainer functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock configuration."""
        config = Mock(spec=ConfigManager)
        config.get.return_value = {
            "market_analysis": {"enabled": True},
            "settlement": {"enabled": True, "max_concurrent": 10},
            "risk_management": {"enabled": True},
            "health_monitoring": {"enabled": False},  # Disabled for testing
            "system_metrics": {"enabled": True}
        }
        return config
    
    @pytest.fixture
    def container(self, mock_config):
        """Create service container for testing."""
        return ServiceContainer(mock_config)
    
    def test_initialization(self, container):
        """Test container initialization."""
        assert container is not None
        assert len(container._service_configs) > 0
        assert len(container._initialization_order) > 0
    
    def test_service_registration(self, container):
        """Test custom service registration."""
        service_config = ServiceConfig(
            service_class=MockService,
            enabled=True,
            init_args={"name": "test_service"}
        )
        
        container.register_service("test_service", service_config)
        
        assert "test_service" in container._service_configs
        assert container._service_configs["test_service"] == service_config
    
    def test_dependency_order_calculation(self, container):
        """Test dependency order calculation."""
        # Register services with dependencies
        container.register_service("service_a", ServiceConfig(
            service_class=MockService,
            enabled=True,
            dependencies=[]
        ))
        
        container.register_service("service_b", ServiceConfig(
            service_class=MockDependentService,
            enabled=True,  
            dependencies=["service_a"]
        ))
        
        # Service A should come before Service B in initialization order
        order = container._initialization_order
        a_index = order.index("service_a") if "service_a" in order else -1
        b_index = order.index("service_b") if "service_b" in order else -1
        
        if a_index >= 0 and b_index >= 0:
            assert a_index < b_index
    
    def test_circular_dependency_detection(self, container):
        """Test circular dependency detection."""
        # Clear existing services first
        container._service_configs.clear()
        
        container.register_service("service_x", ServiceConfig(
            service_class=MockService,
            enabled=True,
            dependencies=["service_y"]
        ))
        
        # This should raise the exception during registration due to circular dependency
        with pytest.raises(ValueError, match="Circular dependency"):
            container.register_service("service_y", ServiceConfig(
                service_class=MockService,
                enabled=True,
                dependencies=["service_x"]
            ))
    
    def test_get_service_lazy_instantiation(self, container):
        """Test lazy service instantiation."""
        # Register a mock service
        container.register_service("lazy_service", ServiceConfig(
            service_class=MockService,
            enabled=True,
            init_args={"name": "lazy"}
        ))
        
        # Service should not be instantiated yet
        assert "lazy_service" not in container._services
        
        # Get service should trigger instantiation
        service = container.get_service("lazy_service")
        
        assert service is not None
        assert isinstance(service, MockService)
        assert service.service_name == "lazy"
        assert "lazy_service" in container._services
    
    def test_get_service_disabled(self, container):
        """Test getting disabled service returns None."""
        container.register_service("disabled_service", ServiceConfig(
            service_class=MockService,
            enabled=False
        ))
        
        service = container.get_service("disabled_service")
        assert service is None
    
    def test_get_service_nonexistent(self, container):
        """Test getting nonexistent service returns None."""
        service = container.get_service("nonexistent_service")
        assert service is None
    
    @pytest.mark.asyncio
    async def test_start_all_services(self, container):
        """Test starting all services."""
        # Register mock services
        container.register_service("service1", ServiceConfig(
            service_class=MockService,
            enabled=True
        ))
        
        container.register_service("service2", ServiceConfig(
            service_class=MockService,
            enabled=True
        ))
        
        await container.start_all_services()
        
        # Check that services were started
        service1 = container.get_service("service1")
        service2 = container.get_service("service2")
        
        assert service1.initialized
        assert service2.initialized
        assert service1.is_running
        assert service2.is_running
    
    @pytest.mark.asyncio
    async def test_stop_all_services(self, container):
        """Test stopping all services."""
        # Register and start services
        container.register_service("service1", ServiceConfig(
            service_class=MockService,
            enabled=True
        ))
        
        await container.start_all_services()
        service1 = container.get_service("service1")
        
        # Stop services
        await container.stop_all_services()
        
        assert service1.cleaned_up
        assert not service1.is_running
    
    @pytest.mark.asyncio
    async def test_restart_service(self, container):
        """Test restarting a specific service."""
        container.register_service("restart_service", ServiceConfig(
            service_class=MockService,
            enabled=True
        ))
        
        await container.start_all_services()
        service = container.get_service("restart_service")
        
        # Reset flags for testing
        service.initialized = False
        service.cleaned_up = False
        
        # Restart service
        success = await container.restart_service("restart_service")
        
        assert success
        assert service.cleaned_up  # Should have been stopped
        assert service.initialized  # Should have been started again
    
    def test_get_service_health_status(self, container):
        """Test getting service health status."""
        container.register_service("health_service", ServiceConfig(
            service_class=MockService,
            enabled=True
        ))
        
        # Get service to instantiate it
        service = container.get_service("health_service")
        
        health_status = container.get_service_health_status()
        
        assert "health_service" in health_status
        assert "service_name" in health_status["health_service"]
        assert "running" in health_status["health_service"]
    
    def test_get_container_status(self, container):
        """Test getting container status."""
        status = container.get_container_status()
        
        assert "total_services" in status
        assert "enabled_services" in status
        assert "instantiated_services" in status
        assert "running_services" in status
        assert "initialization_order" in status
        assert "service_configs" in status
        
        assert isinstance(status["total_services"], int)
        assert isinstance(status["enabled_services"], int)
        assert isinstance(status["initialization_order"], list)
        assert isinstance(status["service_configs"], dict)


class TestServiceConfig:
    """Test ServiceConfig functionality."""
    
    def test_service_config_creation(self):
        """Test ServiceConfig creation with defaults."""
        config = ServiceConfig(service_class=MockService)
        
        assert config.service_class == MockService
        assert config.enabled is True
        assert config.init_args == {}
        assert config.dependencies == []
    
    def test_service_config_with_args(self):
        """Test ServiceConfig creation with arguments."""
        init_args = {"name": "test", "value": 42}
        dependencies = ["dep1", "dep2"]
        
        config = ServiceConfig(
            service_class=MockService,
            enabled=False,
            init_args=init_args,
            dependencies=dependencies
        )
        
        assert config.service_class == MockService
        assert config.enabled is False
        assert config.init_args == init_args
        assert config.dependencies == dependencies


if __name__ == "__main__":
    pytest.main([__file__, "-v"])