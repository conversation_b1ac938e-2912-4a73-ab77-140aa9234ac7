#!/usr/bin/env python3
import os

# 设置环境变量
os.environ['OKX_ACCESS_KEY'] = '2cc32344-c0dc-42b0-a36c-f4df2e9b43fa'
os.environ['OKX_SECRET_KEY'] = 'E06C64E3874AC284CC0CB3162BB13C1E'
os.environ['OKX_PASSPHRASE'] = 'i4LjH.PL!Gjq'

import sys
sys.path.insert(0, '.')

# 测试配置
try:
    print("🔧 测试OKX配置...")
    
    from quant.config_manager import config
    exchanges = config.get('EXCHANGES', {})
    auto_trader = config.get('AUTO_TRADER', {})
    
    print(f"默认交易所: {exchanges.get('default')}")
    print(f"自动交易交易所: {auto_trader.get('exchange')}")
    
    from quant.exchange_client import exchange_client
    print(f"当前客户端: {exchange_client.get_exchange_name()}")
    
    if exchange_client.get_exchange_name() == 'okx':
        print("✅ SUCCESS: OKX配置成功！")
    else:
        print("❌ FAIL: 仍在使用其他交易所")
        
except Exception as e:
    print(f"❌ ERROR: {e}")
    import traceback
    traceback.print_exc()