#!/usr/bin/env python3
"""
重现立即平仓问题的测试
专门针对交易436的情况：测试交易被立即平仓
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.strategies.simple_exit_manager import simple_exit_manager
from quant.strategies.position_exit_manager import position_exit_manager
from quant.settlement_checker import settlement_checker
# from quant.strategies.batch_settlement_processor import BatchSettlementProcessor
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_immediate_exit_reproduction():
    """重现立即平仓问题"""
    logger.info("=== 重现立即平仓问题 ===")
    
    try:
        # 清理环境
        simple_exit_manager._pending_exits.clear()
        simple_exit_manager._processing_locks.clear()
        
        # 1. 创建与交易436相同的测试交易
        logger.info("1. 创建测试交易（模拟交易436）...")
        
        signal_time = datetime.utcnow()
        test_signal = {
            "signal_timestamp": signal_time.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 50000.0,  # 测试交易的标志价格
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 20.0,
            "decision_details": {"test": "immediate_exit_reproduction"}
        }
        
        db_id = db.save_trade_signal(test_signal)
        logger.info(f"创建测试交易，ID: {db_id}")
        
        # 2. 添加到SimpleExitManager
        logger.info("2. 添加到SimpleExitManager...")
        
        trade_data = {
            "id": db_id,
            "entry_price": 50000.0,
            "direction": "LONG",
            "symbol": "BTCUSDT",
            "suggested_bet": 20.0,
            "signal_timestamp": signal_time.isoformat()
        }
        
        simple_exit_manager.add_position(db_id, trade_data)
        
        # 检查是否正确添加
        if db_id in simple_exit_manager._pending_exits:
            exit_info = simple_exit_manager._pending_exits[db_id]
            logger.info(f"✅ 成功添加到SimpleExitManager")
            logger.info(f"  计划平仓时间: {exit_info['planned_exit_time']}")
            logger.info(f"  最小持仓时间: {exit_info['min_hold_minutes']} 分钟")
            logger.info(f"  是否测试交易: {exit_info['is_test_trade']}")
            
            # 计算预期持仓时间
            expected_duration = (exit_info['planned_exit_time'] - signal_time).total_seconds() / 60
            logger.info(f"  预期持仓时间: {expected_duration:.2f} 分钟")
        else:
            logger.error("❌ 未能添加到SimpleExitManager")
            return
        
        # 3. 检查其他管理器是否会立即处理
        logger.info("3. 检查其他管理器的行为...")
        
        # 检查settlement_checker
        logger.info("  检查settlement_checker...")
        pending_trades = db.get_pending_trades()
        for trade in pending_trades:
            if trade['id'] == db_id:
                signal_time_check = datetime.fromisoformat(trade['signal_timestamp'])
                time_since_signal = (datetime.utcnow() - signal_time_check).total_seconds() / 60
                logger.info(f"    交易{db_id}信号后经过: {time_since_signal:.2f} 分钟")
                
                if time_since_signal >= 10:
                    logger.warning(f"    ⚠️  settlement_checker可能会处理此交易")
                else:
                    logger.info(f"    ✅ settlement_checker不会立即处理")
        
        # 检查position_exit_manager
        logger.info("  检查position_exit_manager...")
        if hasattr(position_exit_manager, '_monitored_positions'):
            if db_id in position_exit_manager._monitored_positions:
                logger.warning(f"    ⚠️  position_exit_manager正在监控交易{db_id}")
            else:
                logger.info(f"    ✅ position_exit_manager未监控交易{db_id}")
        
        # 4. 立即执行各种检查，看是否会被平仓
        logger.info("4. 执行各种平仓检查...")
        
        start_time = datetime.utcnow()
        
        # 检查SimpleExitManager
        logger.info("  执行SimpleExitManager检查...")
        await simple_exit_manager._check_exits()
        
        # 检查交易是否还在队列中
        if db_id in simple_exit_manager._pending_exits:
            logger.info("    ✅ 交易仍在SimpleExitManager队列中")
        else:
            logger.warning("    ❌ 交易已从SimpleExitManager队列中移除")
        
        # 检查数据库状态
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            
            if trade:
                logger.info(f"  数据库状态: {trade.status}")
                if trade.status != "PENDING":
                    exit_time = trade.exit_timestamp
                    if isinstance(exit_time, str):
                        exit_time = datetime.fromisoformat(exit_time)
                    
                    hold_duration = (exit_time - signal_time).total_seconds() / 60
                    logger.warning(f"    ❌ 交易被平仓！持仓时长: {hold_duration:.2f} 分钟")
                    logger.warning(f"    平仓价格: ${trade.exit_price:,.2f}")
                    logger.warning(f"    平仓时间: {trade.exit_timestamp}")
                else:
                    logger.info("    ✅ 交易仍为PENDING状态")
        
        # 5. 等待一段时间，观察是否有其他组件处理
        logger.info("5. 等待10秒观察其他组件...")
        await asyncio.sleep(10)
        
        # 再次检查状态
        with db.get_session() as session:
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            
            if trade and trade.status != "PENDING":
                end_time = datetime.utcnow()
                total_duration = (end_time - start_time).total_seconds()
                logger.warning(f"❌ 交易在{total_duration:.1f}秒内被平仓")
                
                # 分析可能的平仓原因
                logger.info("6. 分析平仓原因...")
                
                if hasattr(trade, 'decision_details') and trade.decision_details:
                    try:
                        import json
                        if isinstance(trade.decision_details, str):
                            details = json.loads(trade.decision_details)
                        else:
                            details = trade.decision_details
                        
                        if 'exit_reason' in details:
                            logger.info(f"  平仓原因: {details['exit_reason']}")
                        if 'settlement_method' in details:
                            logger.info(f"  结算方法: {details['settlement_method']}")
                    except:
                        pass
            else:
                logger.info("✅ 交易未被异常平仓")
        
        # 清理测试数据
        logger.info("7. 清理测试数据...")
        simple_exit_manager._pending_exits.clear()
        
        with db.get_session() as session:
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_all_exit_managers():
    """测试所有平仓管理器的行为"""
    logger.info("=== 测试所有平仓管理器 ===")
    
    try:
        # 1. 检查当前运行的管理器
        logger.info("1. 检查当前运行的管理器...")
        
        # SimpleExitManager
        logger.info(f"  SimpleExitManager队列: {len(simple_exit_manager._pending_exits)} 个交易")
        
        # PositionExitManager
        if hasattr(position_exit_manager, '_monitored_positions'):
            logger.info(f"  PositionExitManager监控: {len(position_exit_manager._monitored_positions)} 个交易")
        
        # 2. 检查settlement_checker的配置
        logger.info("2. 检查settlement_checker配置...")
        logger.info(f"  合约持续时间: {settlement_checker.contract_duration}")
        logger.info(f"  检查间隔: {settlement_checker.check_interval_seconds} 秒")
        
        # 3. 检查BatchSettlementProcessor
        logger.info("3. 检查BatchSettlementProcessor...")
        # batch_processor = BatchSettlementProcessor()
        # backlog = await batch_processor.analyze_settlement_backlog()
        # logger.info(f"  待结算交易: {backlog.get('total_pending', 0)} 个")
        logger.info("  BatchSettlementProcessor检查跳过（导入问题）")
        
        # 4. 查看最近的平仓活动
        logger.info("4. 查看最近的平仓活动...")
        
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            
            # 查询最近1小时内平仓的交易
            one_hour_ago = datetime.utcnow() - timedelta(hours=1)
            recent_exits = (
                session.query(TradeHistory)
                .filter(TradeHistory.exit_timestamp >= one_hour_ago)
                .filter(TradeHistory.status.in_(["WIN", "LOSS"]))
                .order_by(TradeHistory.exit_timestamp.desc())
                .limit(10)
                .all()
            )
            
            logger.info(f"  最近1小时内平仓的交易: {len(recent_exits)} 个")
            
            for trade in recent_exits:
                if trade.signal_timestamp and trade.exit_timestamp:
                    signal_time = datetime.fromisoformat(trade.signal_timestamp) if isinstance(trade.signal_timestamp, str) else trade.signal_timestamp
                    exit_time = datetime.fromisoformat(trade.exit_timestamp) if isinstance(trade.exit_timestamp, str) else trade.exit_timestamp
                    
                    hold_duration = (exit_time - signal_time).total_seconds() / 60
                    
                    logger.info(f"    交易{trade.id}: 持仓{hold_duration:.2f}分钟, 价格${trade.entry_price:,.2f}")
                    
                    if hold_duration < 1.0:
                        logger.warning(f"      ⚠️  短时间持仓！")
        
        logger.info("✅ 管理器检查完成")
        
    except Exception as e:
        logger.error(f"❌ 管理器检查失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_immediate_exit_reproduction())
    print("\n" + "="*60 + "\n")
    asyncio.run(test_all_exit_managers())
