#!/usr/bin/env python3
"""
性能对比分析 - 原系统 vs 重构系统

测量和对比关键性能指标，确保重构后没有性能回归。
"""

import asyncio
import time
import sys
import psutil
import gc
import tracemalloc
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass
from contextlib import asynccontextmanager
import json

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from quant.config_manager import ConfigManager
from quant.trading_system_orchestrator import TradingSystemOrchestrator


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    operation: str
    execution_time: float
    memory_before: float
    memory_after: float
    memory_peak: float
    cpu_percent: float
    
    @property
    def memory_used(self) -> float:
        return self.memory_after - self.memory_before


class PerformanceBenchmark:
    """
    性能基准测试工具
    
    测量关键操作的性能指标：
    - 系统初始化时间
    - 内存占用情况
    - CPU使用率
    - 服务启动时间
    - 配置加载时间
    """
    
    def __init__(self):
        self.config = ConfigManager('config/config.json')
        self.metrics: List[PerformanceMetrics] = []
        self.baseline_metrics: Dict[str, float] = {
            # 基线性能指标 (根据原系统经验值设定)
            "initialization_time": 2.0,  # 原系统初始化约2秒
            "memory_baseline": 50.0,     # 原系统基线内存约50MB
            "service_startup_time": 1.0,  # 服务启动时间约1秒
            "config_loading_time": 0.1   # 配置加载约0.1秒
        }
    
    @asynccontextmanager
    async def performance_monitor(self, operation_name: str):
        """性能监控上下文管理器"""
        # 开始内存追踪
        tracemalloc.start()
        gc.collect()  # 强制垃圾回收
        
        # 记录初始状态
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        start_time = time.perf_counter()
        
        try:
            yield
        finally:
            # 记录结束状态
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            
            # 获取内存峰值
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_peak = peak / 1024 / 1024  # MB
            cpu_percent = process.cpu_percent()
            
            # 记录性能指标
            metrics = PerformanceMetrics(
                operation=operation_name,
                execution_time=execution_time,
                memory_before=memory_before,
                memory_after=memory_after,
                memory_peak=memory_peak,
                cpu_percent=cpu_percent
            )
            
            self.metrics.append(metrics)
            
            print(f"⚡ {operation_name}:")
            print(f"   执行时间: {execution_time:.3f}s")
            print(f"   内存使用: {metrics.memory_used:.1f}MB")
            print(f"   内存峰值: {memory_peak:.1f}MB")
            print(f"   CPU使用率: {cpu_percent:.1f}%")
    
    async def benchmark_system_initialization(self) -> PerformanceMetrics:
        """基准测试：系统初始化"""
        print("🔄 测试系统初始化性能...")
        
        async with self.performance_monitor("system_initialization"):
            orchestrator = TradingSystemOrchestrator(self.config)
            # 验证初始化是否成功
            container_status = orchestrator.service_container.get_container_status()
            assert container_status["enabled_services"] > 0
        
        return self.metrics[-1]
    
    async def benchmark_service_lifecycle(self) -> PerformanceMetrics:
        """基准测试：服务生命周期"""
        print("🔄 测试服务生命周期性能...")
        
        orchestrator = TradingSystemOrchestrator(self.config)
        
        async with self.performance_monitor("service_lifecycle"):
            await orchestrator.service_container.start_all_services()
            
            # 验证服务启动成功
            health_status = orchestrator.service_container.get_service_health_status()
            assert len(health_status) > 0
            
            await orchestrator.service_container.stop_all_services()
        
        return self.metrics[-1]
    
    async def benchmark_configuration_loading(self) -> PerformanceMetrics:
        """基准测试：配置加载"""
        print("🔄 测试配置加载性能...")
        
        async with self.performance_monitor("configuration_loading"):
            # 重复加载配置以测试性能
            for _ in range(10):
                config = ConfigManager('config/config.json')
                _ = config.get("PLATFORMS")
                _ = config.get("SERVICES")
                _ = config.get("RISK_MANAGEMENT")
        
        return self.metrics[-1]
    
    async def benchmark_service_access_patterns(self) -> PerformanceMetrics:
        """基准测试：服务访问模式"""
        print("🔄 测试服务访问性能...")
        
        orchestrator = TradingSystemOrchestrator(self.config)
        await orchestrator.service_container.start_all_services()
        
        async with self.performance_monitor("service_access"):
            # 模拟频繁的服务访问
            for _ in range(100):
                market_service = orchestrator.market_analysis_service
                settlement_service = orchestrator.settlement_service
                risk_service = orchestrator.risk_management_service
                health_service = orchestrator.health_monitoring_service
                metrics_service = orchestrator.system_metrics_service
                
                # 验证服务可访问
                assert market_service is not None
                assert settlement_service is not None
                assert risk_service is not None
        
        await orchestrator.service_container.stop_all_services()
        return self.metrics[-1]
    
    async def benchmark_concurrent_operations(self) -> PerformanceMetrics:
        """基准测试：并发操作"""
        print("🔄 测试并发操作性能...")
        
        async with self.performance_monitor("concurrent_operations"):
            # 创建多个并发的系统实例
            tasks = []
            for i in range(5):
                async def create_orchestrator():
                    orchestrator = TradingSystemOrchestrator(self.config)
                    await orchestrator.service_container.start_all_services()
                    await asyncio.sleep(0.1)  # 模拟工作
                    await orchestrator.service_container.stop_all_services()
                
                tasks.append(create_orchestrator())
            
            await asyncio.gather(*tasks)
        
        return self.metrics[-1]
    
    def analyze_performance_regression(self) -> Dict[str, Any]:
        """分析性能回归"""
        print("\n🔍 分析性能回归...")
        
        analysis = {
            "regression_detected": False,
            "performance_improvements": [],
            "performance_regressions": [],
            "overall_assessment": "unknown",
            "detailed_analysis": {}
        }
        
        for metric in self.metrics:
            baseline_key = f"{metric.operation.replace('_', '')}_time"
            if baseline_key in self.baseline_metrics:
                baseline_time = self.baseline_metrics[baseline_key]
                
                # 计算性能差异百分比
                performance_diff = ((metric.execution_time - baseline_time) / baseline_time) * 100
                
                analysis["detailed_analysis"][metric.operation] = {
                    "execution_time": metric.execution_time,
                    "baseline_time": baseline_time,
                    "performance_diff_percent": performance_diff,
                    "memory_used_mb": metric.memory_used,
                    "memory_peak_mb": metric.memory_peak,
                    "cpu_percent": metric.cpu_percent
                }
                
                # 判断是回归还是改进 (10%阈值)
                if performance_diff > 10:
                    analysis["regression_detected"] = True
                    analysis["performance_regressions"].append({
                        "operation": metric.operation,
                        "regression_percent": performance_diff,
                        "severity": "high" if performance_diff > 50 else "medium"
                    })
                elif performance_diff < -10:
                    analysis["performance_improvements"].append({
                        "operation": metric.operation,
                        "improvement_percent": abs(performance_diff),
                        "significance": "high" if abs(performance_diff) > 30 else "medium"
                    })
        
        # 整体评估
        if analysis["regression_detected"]:
            high_severity_regressions = len([r for r in analysis["performance_regressions"] 
                                           if r["severity"] == "high"])
            if high_severity_regressions > 0:
                analysis["overall_assessment"] = "significant_regression"
            else:
                analysis["overall_assessment"] = "minor_regression"
        else:
            if len(analysis["performance_improvements"]) > 0:
                analysis["overall_assessment"] = "performance_improved"
            else:
                analysis["overall_assessment"] = "performance_maintained"
        
        return analysis
    
    def generate_performance_report(self, analysis: Dict[str, Any]) -> str:
        """生成性能报告"""
        report_lines = [
            "=" * 60,
            "📊 重构系统性能分析报告",
            "=" * 60,
            ""
        ]
        
        # 总体评估
        assessment = analysis["overall_assessment"]
        if assessment == "performance_improved":
            report_lines.extend([
                "🎉 总体评估: 性能显著改进",
                "✅ 重构系统性能优于原系统",
                ""
            ])
        elif assessment == "performance_maintained":
            report_lines.extend([
                "✅ 总体评估: 性能基本保持",
                "✅ 重构没有造成性能回归",
                ""
            ])
        elif assessment == "minor_regression":
            report_lines.extend([
                "⚠️  总体评估: 轻微性能回归",
                "⚠️  存在一些性能下降，但在可接受范围内",
                ""
            ])
        else:
            report_lines.extend([
                "❌ 总体评估: 重大性能回归",
                "❌ 存在显著性能问题，需要立即优化",
                ""
            ])
        
        # 性能改进
        if analysis["performance_improvements"]:
            report_lines.extend([
                "🚀 性能改进项:",
                ""
            ])
            for improvement in analysis["performance_improvements"]:
                report_lines.append(
                    f"   ✅ {improvement['operation']}: 改进 {improvement['improvement_percent']:.1f}%"
                )
            report_lines.append("")
        
        # 性能回归
        if analysis["performance_regressions"]:
            report_lines.extend([
                "⚠️  性能回归项:",
                ""
            ])
            for regression in analysis["performance_regressions"]:
                severity_icon = "🔴" if regression["severity"] == "high" else "🟡"
                report_lines.append(
                    f"   {severity_icon} {regression['operation']}: 回归 {regression['regression_percent']:.1f}%"
                )
            report_lines.append("")
        
        # 详细性能数据
        report_lines.extend([
            "📈 详细性能数据:",
            ""
        ])
        
        for operation, data in analysis["detailed_analysis"].items():
            report_lines.extend([
                f"🔍 {operation}:",
                f"   执行时间: {data['execution_time']:.3f}s (基线: {data['baseline_time']:.3f}s)",
                f"   性能差异: {data['performance_diff_percent']:+.1f}%",
                f"   内存使用: {data['memory_used_mb']:.1f}MB",
                f"   内存峰值: {data['memory_peak_mb']:.1f}MB",
                f"   CPU使用率: {data['cpu_percent']:.1f}%",
                ""
            ])
        
        # 推荐行动
        report_lines.extend([
            "💡 推荐行动:",
            ""
        ])
        
        if assessment == "performance_improved":
            report_lines.extend([
                "   ✅ 可以放心部署重构系统",
                "   ✅ 考虑将性能改进作为卖点宣传"
            ])
        elif assessment == "performance_maintained":
            report_lines.extend([
                "   ✅ 可以部署重构系统",
                "   📝 建议监控生产环境性能指标"
            ])
        elif assessment == "minor_regression":
            report_lines.extend([
                "   ⚠️  建议在非高峰时间部署",
                "   📊 加强生产环境性能监控",
                "   🔧 考虑针对回归项目进行优化"
            ])
        else:
            report_lines.extend([
                "   ❌ 不建议立即部署到生产环境",
                "   🔧 需要先解决重大性能问题",
                "   🧪 建议进行更多性能调优"
            ])
        
        return "\\n".join(report_lines)
    
    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """运行全面的性能基准测试"""
        print("=" * 60)
        print("⚡ 开始全面性能基准测试")
        print("=" * 60)
        
        start_time = time.time()
        
        # 执行所有基准测试
        await self.benchmark_system_initialization()
        await self.benchmark_service_lifecycle()
        await self.benchmark_configuration_loading()
        await self.benchmark_service_access_patterns()
        await self.benchmark_concurrent_operations()
        
        total_time = time.time() - start_time
        
        # 分析性能
        analysis = self.analyze_performance_regression()
        
        # 生成报告
        report = self.generate_performance_report(analysis)
        print("\\n" + report)
        
        # 汇总结果
        benchmark_results = {
            "total_benchmark_time": total_time,
            "metrics_collected": len(self.metrics),
            "performance_analysis": analysis,
            "performance_report": report,
            "raw_metrics": [
                {
                    "operation": m.operation,
                    "execution_time": m.execution_time,
                    "memory_used_mb": m.memory_used,
                    "memory_peak_mb": m.memory_peak,
                    "cpu_percent": m.cpu_percent
                }
                for m in self.metrics
            ]
        }
        
        return benchmark_results


async def main():
    """运行性能基准测试"""
    try:
        benchmark = PerformanceBenchmark()
        results = await benchmark.run_comprehensive_benchmark()
        
        # 保存测试结果
        with open("performance_benchmark_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\\n📄 性能测试结果已保存到: performance_benchmark_results.json")
        
        # 根据性能分析结果返回适当的退出码
        assessment = results["performance_analysis"]["overall_assessment"]
        if assessment in ["performance_improved", "performance_maintained"]:
            print("✅ 性能测试通过")
            return True
        elif assessment == "minor_regression":
            print("⚠️  性能测试警告 - 存在轻微回归")
            return True  # 仍然可以接受
        else:
            print("❌ 性能测试失败 - 存在重大回归")
            return False
        
    except Exception as e:
        print(f"❌ 性能基准测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)