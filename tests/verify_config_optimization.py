#!/usr/bin/env python3
"""
Configuration Optimization Verification Script

This script validates the configuration changes made to optimize trading execution.
It checks:
1. AUTO_TRADER configuration consistency
2. RISK_MANAGEMENT parameter alignment
3. RISK_FILTERS threshold validation
4. Simulates trade execution with new parameters
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.config_manager import config
from quant.database_manager import db


def load_config() -> Dict[str, Any]:
    """Load current configuration."""
    try:
        with open('config/config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Failed to load config/config.json: {e}")
        return {}


def validate_auto_trader_config(cfg: Dict[str, Any]) -> bool:
    """Validate AUTO_TRADER configuration."""
    print("\n🔍 Validating AUTO_TRADER Configuration...")
    
    at_cfg = cfg.get("AUTO_TRADER", {})
    issues = []
    
    # Check minimum order amount
    min_order = at_cfg.get("min_order_usdt", 0)
    max_order = at_cfg.get("max_order_usdt", 0)
    
    if min_order > 10:
        issues.append(f"min_order_usdt ({min_order}) might be too high for small signals")
    elif min_order < 1:
        issues.append(f"min_order_usdt ({min_order}) might be too low")
    
    if max_order < min_order:
        issues.append(f"max_order_usdt ({max_order}) is less than min_order_usdt ({min_order})")
    
    # Check if enabled
    if not at_cfg.get("enabled", False):
        issues.append("AUTO_TRADER is disabled")
    
    if at_cfg.get("emergency_stop", False):
        issues.append("Emergency stop is active")
    
    if issues:
        print("❌ AUTO_TRADER Issues:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ AUTO_TRADER Configuration Valid")
        print(f"  - Min Order: {min_order} USDT")
        print(f"  - Max Order: {max_order} USDT")
        print(f"  - Enabled: {at_cfg.get('enabled')}")
        return True


def validate_risk_management_config(cfg: Dict[str, Any]) -> bool:
    """Validate RISK_MANAGEMENT configuration."""
    print("\n🔍 Validating RISK_MANAGEMENT Configuration...")
    
    rm_cfg = cfg.get("RISK_MANAGEMENT", {})
    at_cfg = cfg.get("AUTO_TRADER", {})
    issues = []
    
    # Check alignment with AUTO_TRADER
    rm_min = rm_cfg.get("min_position_size_usdt", 0)
    rm_base = rm_cfg.get("base_position_size_usdt", 0)
    at_min = at_cfg.get("min_order_usdt", 0)
    
    if rm_min > at_min * 2:
        issues.append(f"RISK min_position_size_usdt ({rm_min}) much higher than AUTO_TRADER min ({at_min})")
    
    # Check thresholds
    conf_threshold = rm_cfg.get("confidence_threshold", 1.0)
    vol_threshold = rm_cfg.get("volatility_threshold", 1.0)
    
    if conf_threshold > 0.7:
        issues.append(f"confidence_threshold ({conf_threshold}) might be too high")
    
    if vol_threshold > 0.5:
        issues.append(f"volatility_threshold ({vol_threshold}) might be too restrictive")
    
    if issues:
        print("⚠️  RISK_MANAGEMENT Issues:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ RISK_MANAGEMENT Configuration Valid")
        print(f"  - Enabled: {rm_cfg.get('enabled')}")
        print(f"  - Min Position: {rm_min} USDT")
        print(f"  - Base Position: {rm_base} USDT")
        print(f"  - Confidence Threshold: {conf_threshold}")
        return True


def validate_risk_filters_config(cfg: Dict[str, Any]) -> bool:
    """Validate RISK_FILTERS configuration."""
    print("\n🔍 Validating RISK_FILTERS Configuration...")
    
    rf_cfg = cfg.get("RISK_FILTERS", {})
    issues = []
    
    # Check if thresholds are reasonable
    low_conf_block = rf_cfg.get("low_conf_block_threshold", 1.0)
    low_conf_reduce = rf_cfg.get("low_conf_reduce_threshold", 1.0)
    low_vol = rf_cfg.get("low_vol_threshold", 1.0)
    
    if low_conf_block > 0.4:
        issues.append(f"low_conf_block_threshold ({low_conf_block}) might be too high")
    
    if low_conf_reduce > 0.7:
        issues.append(f"low_conf_reduce_threshold ({low_conf_reduce}) might be too high")
    
    if low_vol > 0.1:
        issues.append(f"low_vol_threshold ({low_vol}) might be too high")
    
    if low_conf_block >= low_conf_reduce:
        issues.append("low_conf_block_threshold should be lower than low_conf_reduce_threshold")
    
    if issues:
        print("⚠️  RISK_FILTERS Issues:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ RISK_FILTERS Configuration Valid")
        print(f"  - Block Threshold: {low_conf_block}")
        print(f"  - Reduce Threshold: {low_conf_reduce}")
        print(f"  - Low Vol Threshold: {low_vol}")
        return True


def test_pending_trades_compatibility() -> bool:
    """Test if pending trades would now be executable."""
    print("\n🔍 Testing Pending Trades Compatibility...")
    
    try:
        # Get recent pending trades
        pending_trades = db.get_pending_trades()
        if not pending_trades:
            print("ℹ️  No pending trades to test")
            return True
        
        print(f"📊 Found {len(pending_trades)} pending trades")
        
        at_cfg = config.get("AUTO_TRADER", {})
        min_order = at_cfg.get("min_order_usdt", 100.0)
        
        executable_count = 0
        for trade in pending_trades[:5]:  # Test first 5
            suggested_bet = trade.get("suggested_bet", 0)
            if suggested_bet >= min_order:
                executable_count += 1
                print(f"  ✅ Trade ID {trade.get('id')}: ${suggested_bet} >= ${min_order}")
            else:
                print(f"  ❌ Trade ID {trade.get('id')}: ${suggested_bet} < ${min_order}")
        
        success_rate = executable_count / min(len(pending_trades), 5) * 100
        print(f"📈 Executability Rate: {success_rate:.1f}%")
        
        return success_rate > 50
        
    except Exception as e:
        print(f"❌ Error testing pending trades: {e}")
        return False


def simulate_trade_execution() -> bool:
    """Simulate trade execution with new parameters."""
    print("\n🔍 Simulating Trade Execution...")
    
    # Create test signals with various sizes
    test_signals = [
        {"suggested_bet": 5.0, "confidence_score": 0.6, "direction": "LONG"},
        {"suggested_bet": 0.5, "confidence_score": 0.4, "direction": "LONG"},
        {"suggested_bet": 50.0, "confidence_score": 0.8, "direction": "SHORT"},
        {"suggested_bet": 2.0, "confidence_score": 0.3, "direction": "LONG"},
    ]
    
    at_cfg = config.get("AUTO_TRADER", {})
    rm_cfg = config.get("RISK_MANAGEMENT", {})
    rf_cfg = config.get("RISK_FILTERS", {})
    
    min_order = at_cfg.get("min_order_usdt", 100.0)
    conf_threshold = rm_cfg.get("confidence_threshold", 0.6)
    block_threshold = rf_cfg.get("low_conf_block_threshold", 0.3)
    
    executable_count = 0
    
    for i, signal in enumerate(test_signals, 1):
        bet = signal["suggested_bet"]
        conf = signal["confidence_score"]
        
        # Check executability
        size_ok = bet >= min_order
        conf_ok = conf >= conf_threshold or conf > block_threshold
        
        executable = size_ok and conf_ok
        if executable:
            executable_count += 1
        
        status = "✅ EXECUTABLE" if executable else "❌ BLOCKED"
        print(f"  Signal {i}: ${bet}, conf={conf:.2f} -> {status}")
        print(f"    Size OK: {size_ok} (${bet} >= ${min_order})")
        print(f"    Conf OK: {conf_ok} (conf={conf:.2f}, threshold={conf_threshold:.2f})")
    
    success_rate = executable_count / len(test_signals) * 100
    print(f"📊 Simulation Success Rate: {success_rate:.1f}%")
    
    return success_rate > 25  # At least 25% should be executable


def main():
    """Main validation function."""
    print("🚀 Configuration Optimization Validation")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Load configuration
    cfg = load_config()
    if not cfg:
        print("❌ Failed to load configuration")
        return False
    
    # Run validations
    validations = [
        validate_auto_trader_config(cfg),
        validate_risk_management_config(cfg),
        validate_risk_filters_config(cfg),
        test_pending_trades_compatibility(),
        simulate_trade_execution(),
    ]
    
    passed = sum(validations)
    total = len(validations)
    
    print(f"\n📊 Validation Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All validations passed! Configuration optimization successful.")
        return True
    elif passed >= total * 0.8:
        print("⚠️  Most validations passed. Configuration is mostly good with minor issues.")
        return True
    else:
        print("❌ Several validations failed. Please review configuration.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)