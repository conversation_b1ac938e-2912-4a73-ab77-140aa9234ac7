#!/usr/bin/env python3
"""
验证测试交易保护修复效果
"""

import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 确保可以导入项目模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_protection_fix():
    """测试保护修复效果"""
    logger.info("=== 测试交易保护修复效果 ===")
    
    try:
        # 1. 创建测试交易
        logger.info("1. 创建测试交易...")
        
        signal_time = datetime.utcnow()
        test_signal = {
            "signal_timestamp": signal_time.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 50000.0,  # 测试价格
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 20.0,
            "decision_details": {"test": "protection_fix_verification"}
        }
        
        db_id = db.save_trade_signal(test_signal)
        logger.info(f"创建测试交易，ID: {db_id}")
        
        # 2. 立即尝试结算（应该被阻止）
        logger.info("2. 立即尝试结算（应该被阻止）...")
        
        immediate_settlement = {
            "exit_price": 55000.0,
            "exit_timestamp": datetime.utcnow().isoformat(),
            "status": "WIN",
            "pnl": 1000.0,
            "settlement_type": "test_immediate"
        }
        
        result = db.update_trade_result(db_id, immediate_settlement)
        
        if not result:
            logger.info("✅ 立即结算被正确阻止")
        else:
            logger.warning("❌ 立即结算未被阻止")
        
        # 3. 检查交易状态
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            
            if trade and trade.status == "PENDING":
                logger.info("✅ 交易仍为PENDING状态")
            else:
                logger.warning(f"❌ 交易状态异常: {trade.status if trade else 'NOT_FOUND'}")
        
        # 4. 等待2分钟后再次尝试（模拟）
        logger.info("3. 模拟2分钟后的结算...")
        
        # 修改信号时间为2分钟前
        past_signal_time = datetime.utcnow() - timedelta(minutes=2, seconds=10)
        
        with db.get_session() as session:
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            if trade:
                trade.signal_timestamp = past_signal_time
                session.commit()
        
        # 现在尝试结算
        delayed_settlement = {
            "exit_price": 52000.0,
            "exit_timestamp": datetime.utcnow().isoformat(),
            "status": "WIN",
            "pnl": 800.0,
            "settlement_type": "test_delayed"
        }
        
        result = db.update_trade_result(db_id, delayed_settlement)
        
        if result:
            logger.info("✅ 2分钟后结算成功")
        else:
            logger.warning("❌ 2分钟后结算失败")
        
        # 5. 最终状态检查
        with db.get_session() as session:
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            
            if trade:
                logger.info(f"最终状态: {trade.status}")
                logger.info(f"平仓价格: ${trade.exit_price or 0:,.2f}")
                logger.info(f"平仓时间: {trade.exit_timestamp}")
                
                if trade.status == "WIN" and trade.exit_price == 52000.0:
                    logger.info("✅ 保护机制工作正常：阻止了立即结算，允许了延迟结算")
                else:
                    logger.warning("❌ 保护机制异常")
        
        # 清理测试数据
        with db.get_session() as session:
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ 保护修复验证完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_normal_trade_not_affected():
    """测试正常交易不受影响"""
    logger.info("=== 测试正常交易不受影响 ===")
    
    try:
        # 1. 创建正常交易
        logger.info("1. 创建正常交易...")
        
        signal_time = datetime.utcnow()
        normal_signal = {
            "signal_timestamp": signal_time.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 120000.0,  # 正常价格
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "normal_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 10.0,
            "decision_details": {"test": "normal_trade_verification"}
        }
        
        db_id = db.save_trade_signal(normal_signal)
        logger.info(f"创建正常交易，ID: {db_id}")
        
        # 2. 立即尝试结算（应该成功）
        logger.info("2. 立即尝试结算正常交易...")
        
        immediate_settlement = {
            "exit_price": 121000.0,
            "exit_timestamp": datetime.utcnow().isoformat(),
            "status": "WIN",
            "pnl": 83.33,
            "settlement_type": "normal_immediate"
        }
        
        result = db.update_trade_result(db_id, immediate_settlement)
        
        if result:
            logger.info("✅ 正常交易立即结算成功")
        else:
            logger.warning("❌ 正常交易立即结算失败")
        
        # 3. 检查交易状态
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            
            if trade and trade.status == "WIN":
                logger.info("✅ 正常交易状态正确")
                logger.info(f"  平仓价格: ${trade.exit_price:,.2f}")
                logger.info(f"  盈亏: ${trade.pnl:,.2f}")
            else:
                logger.warning(f"❌ 正常交易状态异常: {trade.status if trade else 'NOT_FOUND'}")
        
        # 清理测试数据
        with db.get_session() as session:
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ 正常交易验证完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_edge_cases():
    """测试边界情况"""
    logger.info("=== 测试边界情况 ===")
    
    try:
        # 测试1：恰好2分钟的情况
        logger.info("1. 测试恰好2分钟的情况...")
        
        signal_time = datetime.utcnow() - timedelta(minutes=2, seconds=0)
        test_signal = {
            "signal_timestamp": signal_time.isoformat(),
            "symbol": "BTCUSDT",
            "direction": "LONG",
            "entry_price": 50000.0,
            "confidence_score": 0.8,
            "market_state": "TRENDING_UP",
            "trigger_pattern": "test_pattern",
            "confirmed_indicators": ["rsi", "macd"],
            "suggested_bet": 20.0,
            "decision_details": {"test": "edge_case_2min"}
        }
        
        db_id = db.save_trade_signal(test_signal)
        
        # 直接更新数据库中的信号时间
        with db.get_session() as session:
            from quant.database_manager import TradeHistory
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            if trade:
                trade.signal_timestamp = signal_time
                session.commit()
        
        settlement = {
            "exit_price": 51000.0,
            "exit_timestamp": datetime.utcnow().isoformat(),
            "status": "WIN",
            "pnl": 400.0,
            "settlement_type": "edge_case_2min"
        }
        
        result = db.update_trade_result(db_id, settlement)
        
        if result:
            logger.info("✅ 恰好2分钟的结算成功")
        else:
            logger.warning("❌ 恰好2分钟的结算失败")
        
        # 清理
        with db.get_session() as session:
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        # 测试2：1分59秒的情况
        logger.info("2. 测试1分59秒的情况...")
        
        signal_time = datetime.utcnow() - timedelta(minutes=1, seconds=59)
        test_signal["signal_timestamp"] = signal_time.isoformat()
        test_signal["decision_details"] = {"test": "edge_case_1min59s"}
        
        db_id = db.save_trade_signal(test_signal)
        
        with db.get_session() as session:
            trade = session.query(TradeHistory).filter(TradeHistory.id == db_id).first()
            if trade:
                trade.signal_timestamp = signal_time
                session.commit()
        
        result = db.update_trade_result(db_id, settlement)
        
        if not result:
            logger.info("✅ 1分59秒的结算被正确阻止")
        else:
            logger.warning("❌ 1分59秒的结算未被阻止")
        
        # 清理
        with db.get_session() as session:
            session.query(TradeHistory).filter(TradeHistory.id == db_id).delete()
            session.commit()
        
        logger.info("✅ 边界情况测试完成")
        
    except Exception as e:
        logger.error(f"❌ 边界测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_protection_fix())
    print("\n" + "="*60 + "\n")
    asyncio.run(test_normal_trade_not_affected())
    print("\n" + "="*60 + "\n")
    asyncio.run(test_edge_cases())
