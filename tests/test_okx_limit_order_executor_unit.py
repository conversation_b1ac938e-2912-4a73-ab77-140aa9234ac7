#!/usr/bin/env python3
"""
Unit tests for OkxLimitOrderExecutor

Tests the core functionality of the OKX-specific limit order executor
without requiring actual exchange connections.
"""

import unittest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import asyncio
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from quant.strategies.okx_limit_order_executor import (
    OkxLimitOrderExecutor,
    OkxOrderContext,
    OrderStatus
)
from quant.exchange.okx_exchange import OkxOrderAdapter


class TestOkxOrderContext(unittest.TestCase):
    """Test OkxOrderContext data class"""
    
    def test_context_creation(self):
        """Test creating an order context"""
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated"
        )
        
        self.assertEqual(context.symbol, "BTC/USDT")
        self.assertEqual(context.side, "BUY")
        self.assertEqual(context.quantity, 0.1)
        self.assertEqual(context.position_side, "long")
        self.assertEqual(context.trade_mode, "isolated")
        self.assertIsNone(context.limit_price)
        self.assertIsNone(context.order_id)
        self.assertEqual(context.status, OrderStatus.PENDING)
    
    def test_context_with_optional_params(self):
        """Test context with optional parameters"""
        context = OkxOrderContext(
            symbol="ETH/USDT",
            side="SELL",
            quantity=1.0,
            position_side="short",
            trade_mode="cross",
            limit_price=3500.0,
            order_id="TEST123",
            client_order_id="CLIENT456",
            leverage=10
        )
        
        self.assertEqual(context.limit_price, 3500.0)
        self.assertEqual(context.order_id, "TEST123")
        self.assertEqual(context.client_order_id, "CLIENT456")
        self.assertEqual(context.leverage, 10)


class TestOkxOrderAdapter(unittest.TestCase):
    """Test OkxOrderAdapter functionality"""
    
    def setUp(self):
        self.adapter = OkxOrderAdapter()
    
    def test_symbol_conversion(self):
        """Test symbol format conversion"""
        test_cases = [
            ("BTC/USDT", "BTC-USDT-SWAP"),
            ("ETH/USDT", "ETH-USDT-SWAP"),
            ("BTC/USDC", "BTC-USDC-SWAP"),
            ("BTC-USDT", "BTC-USDT-SWAP"),  # Already in OKX format
        ]
        
        for input_symbol, expected in test_cases:
            result = self.adapter.convert_symbol(input_symbol)
            self.assertEqual(result, expected)
    
    def test_order_side_conversion(self):
        """Test order side conversion"""
        test_cases = [
            ("BUY", "buy"),
            ("SELL", "sell"),
            ("buy", "buy"),
            ("sell", "sell"),
        ]
        
        for input_side, expected in test_cases:
            result = self.adapter.convert_side(input_side)
            self.assertEqual(result, expected)
    
    def test_position_side_conversion(self):
        """Test position side conversion"""
        test_cases = [
            ("LONG", "long"),
            ("SHORT", "short"),
            ("long", "long"),
            ("short", "short"),
        ]
        
        for input_side, expected in test_cases:
            result = self.adapter.convert_position_side(input_side)
            self.assertEqual(result, expected)


class TestOkxLimitOrderExecutor(unittest.IsolatedAsyncioTestCase):
    """Test OkxLimitOrderExecutor async functionality"""
    
    async def asyncSetUp(self):
        """Set up test fixtures"""
        # Mock exchange
        self.mock_exchange = Mock()
        self.mock_exchange.get_current_price = AsyncMock(return_value=50000.0)
        self.mock_exchange.get_orderbook = AsyncMock(return_value={
            "data": [{
                "asks": [["50010", "1"], ["50020", "2"]],
                "bids": [["49990", "1"], ["49980", "2"]]
            }]
        })
        self.mock_exchange.place_limit_order = AsyncMock(return_value={
            "code": "0",
            "data": [{"ordId": "TEST_ORDER_123", "clOrdId": "CLIENT_123"}]
        })
        self.mock_exchange.place_market_order = AsyncMock(return_value={
            "code": "0",
            "data": [{"ordId": "MARKET_ORDER_123"}]
        })
        self.mock_exchange.cancel_order = AsyncMock(return_value={
            "code": "0",
            "data": [{"ordId": "TEST_ORDER_123"}]
        })
        self.mock_exchange.get_order = AsyncMock(return_value={
            "code": "0",
            "data": [{
                "ordId": "TEST_ORDER_123",
                "state": "filled",
                "fillSz": "0.1",
                "avgPx": "50000"
            }]
        })
        
        # Mock config manager
        self.mock_config = Mock()
        self.mock_config.get.return_value = {
            "okx": {
                "MAKER_ORDER": {
                    "enabled": True,
                    "price_strategy": "adaptive",
                    "buy_offset_bps": 10,
                    "sell_offset_bps": 10,
                    "initial_timeout": 30,
                    "max_retries": 3,
                    "trade_mode": "isolated",
                    "post_only": True
                }
            }
        }
        
        # Create executor with mocks
        with patch('quant.strategies.okx_limit_order_executor.OkxExchange', return_value=self.mock_exchange):
            with patch('quant.strategies.okx_limit_order_executor.ConfigManager', return_value=self.mock_config):
                self.executor = OkxLimitOrderExecutor()
                await self.executor.initialize()
    
    async def test_initialize(self):
        """Test executor initialization"""
        self.assertIsNotNone(self.executor.exchange)
        self.assertIsNotNone(self.executor.config)
        self.assertIsNotNone(self.executor.order_adapter)
        self.assertTrue(self.executor.maker_config["enabled"])
    
    async def test_calculate_limit_price_buy(self):
        """Test limit price calculation for buy orders"""
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated"
        )
        
        price = await self.executor._calculate_limit_price(context)
        
        # Should be slightly below best bid for maker order
        self.assertLess(price, 49990)  # Below best bid
        self.assertGreater(price, 49900)  # But not too far
    
    async def test_calculate_limit_price_sell(self):
        """Test limit price calculation for sell orders"""
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="SELL",
            quantity=0.1,
            position_side="short",
            trade_mode="isolated"
        )
        
        price = await self.executor._calculate_limit_price(context)
        
        # Should be slightly above best ask for maker order
        self.assertGreater(price, 50010)  # Above best ask
        self.assertLess(price, 50100)  # But not too far
    
    async def test_place_limit_order_success(self):
        """Test successful limit order placement"""
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated"
        )
        
        result = await self.executor.place_limit_order(context)
        
        self.assertTrue(result)
        self.assertEqual(context.status, OrderStatus.PLACED)
        self.assertEqual(context.order_id, "TEST_ORDER_123")
        self.assertIsNotNone(context.limit_price)
        self.mock_exchange.place_limit_order.assert_called_once()
    
    async def test_place_limit_order_post_only_retry(self):
        """Test post-only order retry logic"""
        # First call fails with post-only error, second succeeds
        self.mock_exchange.place_limit_order.side_effect = [
            {"code": "51115", "msg": "Post-only order would be filled"},  # First attempt
            {"code": "0", "data": [{"ordId": "TEST_ORDER_456"}]}  # Retry succeeds
        ]
        
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated"
        )
        
        result = await self.executor.place_limit_order(context)
        
        self.assertTrue(result)
        self.assertEqual(context.order_id, "TEST_ORDER_456")
        self.assertEqual(self.mock_exchange.place_limit_order.call_count, 2)
    
    async def test_place_limit_order_fallback_to_market(self):
        """Test fallback to market order after max retries"""
        # All limit order attempts fail
        self.mock_exchange.place_limit_order.return_value = {
            "code": "51115",
            "msg": "Post-only order would be filled"
        }
        
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated"
        )
        
        # Force max retries
        self.executor.maker_config["max_retries"] = 2
        
        result = await self.executor.place_limit_order(context)
        
        # Should fallback to market order
        self.assertTrue(result)
        self.assertEqual(context.status, OrderStatus.FILLED)
        self.assertEqual(context.order_id, "MARKET_ORDER_123")
        self.mock_exchange.place_market_order.assert_called_once()
    
    async def test_monitor_order_filled(self):
        """Test monitoring an order until filled"""
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated",
            order_id="TEST_ORDER_123"
        )
        context.status = OrderStatus.PLACED
        
        # Simulate order being filled
        self.mock_exchange.get_order.return_value = {
            "code": "0",
            "data": [{
                "ordId": "TEST_ORDER_123",
                "state": "filled",
                "fillSz": "0.1",
                "avgPx": "50000"
            }]
        }
        
        result = await self.executor.monitor_order(context, timeout=1)
        
        self.assertTrue(result)
        self.assertEqual(context.status, OrderStatus.FILLED)
        self.assertEqual(context.fill_price, 50000.0)
        self.assertEqual(context.filled_quantity, 0.1)
    
    async def test_monitor_order_timeout(self):
        """Test order monitoring timeout"""
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated",
            order_id="TEST_ORDER_123"
        )
        context.status = OrderStatus.PLACED
        
        # Order remains live (not filled)
        self.mock_exchange.get_order.return_value = {
            "code": "0",
            "data": [{
                "ordId": "TEST_ORDER_123",
                "state": "live",
                "fillSz": "0",
                "avgPx": "0"
            }]
        }
        
        result = await self.executor.monitor_order(context, timeout=0.1)
        
        self.assertFalse(result)
        self.assertEqual(context.status, OrderStatus.PLACED)  # Still placed
    
    async def test_cancel_order(self):
        """Test order cancellation"""
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated",
            order_id="TEST_ORDER_123"
        )
        context.status = OrderStatus.PLACED
        
        result = await self.executor.cancel_order(context)
        
        self.assertTrue(result)
        self.assertEqual(context.status, OrderStatus.CANCELED)
        self.mock_exchange.cancel_order.assert_called_once_with(
            symbol="BTC-USDT-SWAP",
            order_id="TEST_ORDER_123"
        )
    
    async def test_get_position(self):
        """Test position retrieval"""
        self.mock_exchange.get_position.return_value = {
            "code": "0",
            "data": [{
                "posSide": "long",
                "pos": "0.1",
                "avgPx": "50000",
                "unrealizedPnl": "100"
            }]
        }
        
        position = await self.executor.get_position("BTC/USDT")
        
        self.assertIsNotNone(position)
        self.assertEqual(position["data"][0]["pos"], "0.1")
        self.mock_exchange.get_position.assert_called_once_with("BTC-USDT-SWAP")
    
    async def test_metrics_tracking(self):
        """Test metrics collection"""
        # Place a successful post-only order
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated"
        )
        
        await self.executor.place_limit_order(context)
        
        # Check metrics
        metrics = self.executor.get_metrics()
        self.assertIn("post_only_success_rate", metrics)
        self.assertIn("total_orders", metrics)
        self.assertIn("post_only_orders", metrics)
        self.assertEqual(metrics["total_orders"], 1)


class TestIntegrationScenarios(unittest.IsolatedAsyncioTestCase):
    """Test complete trading scenarios"""
    
    async def asyncSetUp(self):
        """Set up for integration scenarios"""
        # Similar setup as TestOkxLimitOrderExecutor
        self.mock_exchange = Mock()
        self.mock_config = Mock()
        
        # Configure mocks for full scenarios
        self.mock_exchange.get_current_price = AsyncMock(return_value=50000.0)
        self.mock_exchange.get_orderbook = AsyncMock(return_value={
            "data": [{
                "asks": [["50010", "1"]],
                "bids": [["49990", "1"]]
            }]
        })
        
        with patch('quant.strategies.okx_limit_order_executor.OkxExchange', return_value=self.mock_exchange):
            with patch('quant.strategies.okx_limit_order_executor.ConfigManager', return_value=self.mock_config):
                self.executor = OkxLimitOrderExecutor()
                await self.executor.initialize()
    
    async def test_complete_buy_order_flow(self):
        """Test complete buy order workflow"""
        # 1. Create order context
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="BUY",
            quantity=0.1,
            position_side="long",
            trade_mode="isolated"
        )
        
        # 2. Place limit order
        self.mock_exchange.place_limit_order.return_value = {
            "code": "0",
            "data": [{"ordId": "BUY_ORDER_123"}]
        }
        
        place_result = await self.executor.place_limit_order(context)
        self.assertTrue(place_result)
        self.assertEqual(context.order_id, "BUY_ORDER_123")
        
        # 3. Monitor until filled
        self.mock_exchange.get_order.return_value = {
            "code": "0",
            "data": [{
                "ordId": "BUY_ORDER_123",
                "state": "filled",
                "fillSz": "0.1",
                "avgPx": "49985"
            }]
        }
        
        monitor_result = await self.executor.monitor_order(context, timeout=10)
        self.assertTrue(monitor_result)
        self.assertEqual(context.status, OrderStatus.FILLED)
        self.assertEqual(context.fill_price, 49985.0)
        
        # 4. Verify position
        self.mock_exchange.get_position.return_value = {
            "code": "0",
            "data": [{
                "posSide": "long",
                "pos": "0.1",
                "avgPx": "49985"
            }]
        }
        
        position = await self.executor.get_position("BTC/USDT")
        self.assertEqual(float(position["data"][0]["pos"]), 0.1)
    
    async def test_sell_order_with_cancellation(self):
        """Test sell order with cancellation scenario"""
        # 1. Create sell order
        context = OkxOrderContext(
            symbol="BTC/USDT",
            side="SELL",
            quantity=0.1,
            position_side="short",
            trade_mode="isolated"
        )
        
        # 2. Place order
        self.mock_exchange.place_limit_order.return_value = {
            "code": "0",
            "data": [{"ordId": "SELL_ORDER_456"}]
        }
        
        await self.executor.place_limit_order(context)
        
        # 3. Order doesn't fill, cancel it
        self.mock_exchange.get_order.return_value = {
            "code": "0",
            "data": [{
                "ordId": "SELL_ORDER_456",
                "state": "live",
                "fillSz": "0"
            }]
        }
        
        # Monitor for short time (will timeout)
        monitor_result = await self.executor.monitor_order(context, timeout=0.1)
        self.assertFalse(monitor_result)
        
        # 4. Cancel the order
        self.mock_exchange.cancel_order.return_value = {
            "code": "0",
            "data": [{"ordId": "SELL_ORDER_456"}]
        }
        
        cancel_result = await self.executor.cancel_order(context)
        self.assertTrue(cancel_result)
        self.assertEqual(context.status, OrderStatus.CANCELED)


def run_tests():
    """Run all tests"""
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestOkxOrderContext))
    suite.addTests(loader.loadTestsFromTestCase(TestOkxOrderAdapter))
    suite.addTests(loader.loadTestsFromTestCase(TestOkxLimitOrderExecutor))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegrationScenarios))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return exit code
    return 0 if result.wasSuccessful() else 1


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)
