#!/usr/bin/env python3
"""
简单的OKX配置测试脚本
"""
import os
import sys
sys.path.insert(0, '.')

def main():
    try:
        print("=== OKX配置验证 ===")
        
        # 检查环境变量
        okx_key = os.getenv('OKX_ACCESS_KEY')
        okx_secret = os.getenv('OKX_SECRET_KEY')
        okx_passphrase = os.getenv('OKX_PASSPHRASE')
        
        print(f"环境变量检查:")
        print(f"  OKX_ACCESS_KEY: {'✅ 已设置' if okx_key else '❌ 未设置'}")
        print(f"  OKX_SECRET_KEY: {'✅ 已设置' if okx_secret else '❌ 未设置'}")
        print(f"  OKX_PASSPHRASE: {'✅ 已设置' if okx_passphrase else '❌ 未设置'}")
        
        if not (okx_key and okx_secret and okx_passphrase):
            print("❌ 缺少OKX环境变量，请设置后再试")
            return
            
        # 检查配置
        from quant.config_manager import config
        exchanges = config.get('EXCHANGES', {})
        auto_trader = config.get('AUTO_TRADER', {})
        
        print(f"配置文件检查:")
        print(f"  默认交易所: {exchanges.get('default', 'not_set')}")
        print(f"  自动交易交易所: {auto_trader.get('exchange', 'not_set')}")
        
        # 检查exchange_client
        from quant.exchange_client import exchange_client
        current_exchange = exchange_client.get_exchange_name()
        print(f"  当前交易所客户端: {current_exchange}")
        
        if current_exchange == 'okx':
            print("✅ OKX配置成功！系统将使用OKX作为数据源和交易执行")
        else:
            print(f"⚠️ 系统仍在使用{current_exchange}，请检查配置")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()