#!/usr/bin/env python3
"""
Test script for limit order functionality

Tests the new maker order implementation in a controlled environment
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import json
import os

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Load environment variables from .env file BEFORE importing modules
from dotenv import load_dotenv
load_dotenv()

# Verify environment variables are loaded
if not os.getenv("BINANCE_ACCESS_KEY"):
    print("WARNING: BINANCE_ACCESS_KEY not found in environment")
    print("Please ensure .env file exists with API credentials")

# Set to development mode for safe testing (uses mock data)
os.environ["ENVIRONMENT"] = "development"
os.environ["DEBUG"] = "true"
print("Running in DEVELOPMENT mode with mock data for safety")

# Now import our modules
from quant.config_manager import ConfigManager
from quant.strategies.limit_order_executor import limit_order_executor, OrderStatus
from quant.binance_client import binance_client
from quant.utils.logger import get_logger

logger = get_logger(__name__)


class LimitOrderTester:
    """Test harness for limit order functionality"""
    
    def __init__(self):
        self.test_results = []
        self.config = ConfigManager("config/config.json")
        
    async def setup(self):
        """Initialize test environment"""
        logger.info("=" * 60)
        logger.info("LIMIT ORDER TEST SUITE")
        logger.info("=" * 60)
        
        # Initialize binance client
        await binance_client.initialize()
        
        # Start limit order executor
        await limit_order_executor.start()
        
        logger.info("Test environment initialized")
    
    async def cleanup(self):
        """Clean up test environment"""
        await limit_order_executor.stop()
        await binance_client.close()
        logger.info("Test environment cleaned up")
    
    async def test_price_calculation(self):
        """Test 1: Price calculation strategies"""
        logger.info("\n[TEST 1] Testing price calculation...")
        
        try:
            # Get current market price
            current_price = await binance_client.get_current_price("BTCUSDT")
            logger.info(f"Current market price: ${current_price:,.2f}")
            
            # Test different strategies
            strategies = ["AGGRESSIVE", "BALANCED", "PASSIVE"]
            
            for strategy in strategies:
                # Temporarily change strategy
                original_strategy = limit_order_executor.config.price_strategy
                limit_order_executor.config.price_strategy = strategy
                
                # Calculate buy price
                buy_context = type('obj', (object,), {
                    'symbol': 'BTCUSDT',
                    'side': 'BUY'
                })()
                buy_price = await limit_order_executor._calculate_limit_price(buy_context)
                
                # Calculate sell price
                sell_context = type('obj', (object,), {
                    'symbol': 'BTCUSDT',
                    'side': 'SELL'
                })()
                sell_price = await limit_order_executor._calculate_limit_price(sell_context)
                
                # Restore original strategy
                limit_order_executor.config.price_strategy = original_strategy
                
                # Calculate spreads
                buy_spread_bps = ((current_price - buy_price) / current_price) * 10000
                sell_spread_bps = ((sell_price - current_price) / current_price) * 10000
                
                logger.info(f"\n  Strategy: {strategy}")
                logger.info(f"    Buy price: ${buy_price:,.2f} ({buy_spread_bps:.1f} bps below market)")
                logger.info(f"    Sell price: ${sell_price:,.2f} ({sell_spread_bps:.1f} bps above market)")
                
                self.test_results.append({
                    "test": "price_calculation",
                    "strategy": strategy,
                    "success": True,
                    "buy_price": buy_price,
                    "sell_price": sell_price,
                    "buy_spread_bps": buy_spread_bps,
                    "sell_spread_bps": sell_spread_bps
                })
            
            logger.info("✅ Price calculation test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Price calculation test failed: {e}")
            self.test_results.append({
                "test": "price_calculation",
                "success": False,
                "error": str(e)
            })
            return False
    
    async def test_limit_order_simulation(self):
        """Test 2: Simulate limit order placement"""
        logger.info("\n[TEST 2] Testing limit order placement (simulation)...")
        
        try:
            # Test parameters
            test_symbol = "BTCUSDT"
            test_quantity = 0.001  # Small test quantity
            
            # Test buy order
            logger.info("\n  Testing BUY limit order...")
            buy_context = await limit_order_executor.execute_limit_order(
                symbol=test_symbol,
                side="BUY",
                quantity=test_quantity,
                position_side="LONG",
                urgency="NORMAL"
            )
            
            logger.info(f"    Order ID: {buy_context.order_id}")
            logger.info(f"    Status: {buy_context.status}")
            logger.info(f"    Limit Price: ${buy_context.limit_price:,.2f}")
            logger.info(f"    Quantity: {buy_context.quantity}")
            
            # Test sell order
            logger.info("\n  Testing SELL limit order...")
            sell_context = await limit_order_executor.execute_limit_order(
                symbol=test_symbol,
                side="SELL",
                quantity=test_quantity,
                position_side="SHORT",
                urgency="NORMAL"
            )
            
            logger.info(f"    Order ID: {sell_context.order_id}")
            logger.info(f"    Status: {sell_context.status}")
            logger.info(f"    Limit Price: ${sell_context.limit_price:,.2f}")
            logger.info(f"    Quantity: {sell_context.quantity}")
            
            self.test_results.append({
                "test": "limit_order_simulation",
                "success": True,
                "buy_order": {
                    "order_id": buy_context.order_id,
                    "status": buy_context.status.value,
                    "limit_price": buy_context.limit_price
                },
                "sell_order": {
                    "order_id": sell_context.order_id,
                    "status": sell_context.status.value,
                    "limit_price": sell_context.limit_price
                }
            })
            
            logger.info("✅ Limit order simulation test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Limit order simulation test failed: {e}")
            self.test_results.append({
                "test": "limit_order_simulation",
                "success": False,
                "error": str(e)
            })
            return False
    
    async def test_urgency_modes(self):
        """Test 3: Different urgency modes"""
        logger.info("\n[TEST 3] Testing urgency modes...")
        
        try:
            urgency_modes = ["NORMAL", "HIGH", "EMERGENCY"]
            test_quantity = 0.001
            
            for urgency in urgency_modes:
                logger.info(f"\n  Testing {urgency} urgency...")
                
                start_time = datetime.utcnow()
                
                context = await limit_order_executor.execute_limit_order(
                    symbol="BTCUSDT",
                    side="BUY",
                    quantity=test_quantity,
                    urgency=urgency
                )
                
                execution_time = (datetime.utcnow() - start_time).total_seconds()
                
                logger.info(f"    Status: {context.status}")
                logger.info(f"    Execution time: {execution_time:.2f}s")
                
                if urgency == "EMERGENCY":
                    # Should use market order
                    logger.info(f"    Used market order: {context.status == OrderStatus.FILLED}")
                
                self.test_results.append({
                    "test": "urgency_mode",
                    "urgency": urgency,
                    "success": True,
                    "status": context.status.value,
                    "execution_time": execution_time
                })
            
            logger.info("✅ Urgency modes test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Urgency modes test failed: {e}")
            self.test_results.append({
                "test": "urgency_modes",
                "success": False,
                "error": str(e)
            })
            return False
    
    async def test_fee_calculation(self):
        """Test 4: Fee savings calculation"""
        logger.info("\n[TEST 4] Testing fee savings calculation...")
        
        try:
            test_amounts = [10, 50, 100, 500, 1000]  # USDT amounts
            
            total_saved = 0
            
            for amount in test_amounts:
                saved_fees = limit_order_executor.calculate_saved_fees(amount)
                
                # Calculate percentages
                taker_fee = amount * 0.0004  # 0.04%
                maker_fee = amount * 0.0002  # 0.02%
                
                logger.info(f"\n  Amount: ${amount} USDT")
                logger.info(f"    Taker fee: ${taker_fee:.4f}")
                logger.info(f"    Maker fee: ${maker_fee:.4f}")
                logger.info(f"    Saved: ${saved_fees:.4f} ({(saved_fees/taker_fee*100):.1f}%)")
                
                total_saved += saved_fees
                
                self.test_results.append({
                    "test": "fee_calculation",
                    "amount": amount,
                    "taker_fee": taker_fee,
                    "maker_fee": maker_fee,
                    "saved": saved_fees,
                    "success": True
                })
            
            logger.info(f"\n  Total fees saved in test: ${total_saved:.4f}")
            logger.info("✅ Fee calculation test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Fee calculation test failed: {e}")
            self.test_results.append({
                "test": "fee_calculation",
                "success": False,
                "error": str(e)
            })
            return False
    
    async def test_metrics(self):
        """Test 5: Performance metrics"""
        logger.info("\n[TEST 5] Testing performance metrics...")
        
        try:
            metrics = limit_order_executor.get_metrics()
            
            logger.info("\n  Current Metrics:")
            logger.info(f"    Total limit orders: {metrics['total_limit_orders']}")
            logger.info(f"    Filled orders: {metrics['filled_orders']}")
            logger.info(f"    Fill rate: {metrics['fill_rate']:.1%}")
            logger.info(f"    Average fill time: {metrics['avg_fill_time']:.1f}s")
            logger.info(f"    Total fees saved: ${metrics['fees_saved']:.4f}")
            logger.info(f"    Market fallback count: {metrics['fallback_to_market']}")
            logger.info(f"    Fallback rate: {metrics['fallback_rate']:.1%}")
            
            self.test_results.append({
                "test": "metrics",
                "success": True,
                "metrics": metrics
            })
            
            logger.info("✅ Metrics test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Metrics test failed: {e}")
            self.test_results.append({
                "test": "metrics",
                "success": False,
                "error": str(e)
            })
            return False
    
    async def run_all_tests(self):
        """Run all tests"""
        await self.setup()
        
        try:
            # Run tests
            await self.test_price_calculation()
            await asyncio.sleep(1)
            
            await self.test_limit_order_simulation()
            await asyncio.sleep(1)
            
            await self.test_urgency_modes()
            await asyncio.sleep(1)
            
            await self.test_fee_calculation()
            await asyncio.sleep(1)
            
            await self.test_metrics()
            
            # Generate report
            self.generate_report()
            
        finally:
            await self.cleanup()
    
    def generate_report(self):
        """Generate test report"""
        logger.info("\n" + "=" * 60)
        logger.info("TEST REPORT")
        logger.info("=" * 60)
        
        # Count results
        total_tests = len([r for r in self.test_results if 'test' in r])
        passed_tests = len([r for r in self.test_results if r.get('success', False)])
        failed_tests = total_tests - passed_tests
        
        logger.info(f"\nTotal Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        # Save results to file
        report_file = f"test_results/limit_order_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        Path("test_results").mkdir(exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "success_rate": passed_tests/total_tests
                },
                "results": self.test_results
            }, f, indent=2)
        
        logger.info(f"\nDetailed report saved to: {report_file}")
        
        # Show recommendations
        logger.info("\n" + "=" * 60)
        logger.info("RECOMMENDATIONS")
        logger.info("=" * 60)
        
        if passed_tests == total_tests:
            logger.info("✅ All tests passed! The limit order system is ready for production.")
            logger.info("\nNext steps:")
            logger.info("1. Start with BALANCED price strategy")
            logger.info("2. Monitor fill rates closely")
            logger.info("3. Adjust offset_bps if needed")
            logger.info("4. Consider using SMART mode for automatic selection")
        else:
            logger.info("⚠️ Some tests failed. Please review the errors before deploying.")
            logger.info("\nTroubleshooting:")
            logger.info("1. Check API credentials")
            logger.info("2. Verify network connectivity")
            logger.info("3. Review error logs")


async def main():
    """Main test runner"""
    tester = LimitOrderTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
