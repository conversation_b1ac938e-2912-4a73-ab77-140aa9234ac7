#!/usr/bin/env python3
"""
演示配置文件symbol更改功能
Demonstrates config file symbol change functionality
"""

import asyncio
import json
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from quant.symbol_manager import symbol_manager
from quant.binance_client import binance_client
from quant.config_manager import ConfigManager


async def demonstrate_config_symbol_change():
    """演示配置文件symbol更改流程"""
    
    print("🎯 演示：配置文件Symbol更改功能")
    print("=" * 50)
    
    config_manager = ConfigManager("config/config.json")
    
    # 步骤1：显示当前状态
    print("\n📊 步骤1：当前状态")
    print("-" * 30)
    current_symbol = symbol_manager.get_current_symbol()
    print(f"当前活跃交易对: {current_symbol}")
    
    # 步骤2：读取配置文件
    config_data = config_manager.load_config()
    config_symbol = config_data.get("AUTO_TRADER", {}).get("symbol", "BTCUSDT")
    print(f"配置文件中的交易对: {config_symbol}")
    
    # 步骤3：模拟修改配置文件
    print("\n🔄 步骤2：模拟修改配置文件")
    print("-" * 30)
    
    # 选择不同的交易对进行测试
    test_symbols = ["BTCUSDT", "BTCUSDC"]
    new_symbol = test_symbols[1] if config_symbol == test_symbols[0] else test_symbols[0]
    
    print(f"将配置文件中的symbol从 {config_symbol} 改为 {new_symbol}")
    
    # 修改配置文件
    config_data["AUTO_TRADER"]["symbol"] = new_symbol
    with open("config/config.json", 'w') as f:
        json.dump(config_data, f, indent=2)
    
    print(f"✅ 配置文件已更新")
    
    # 步骤3：重新加载配置管理器
    print("\n🔄 步骤3：应用配置更改")
    print("-" * 30)
    
    # 重新初始化配置管理器
    config_manager = ConfigManager("config/config.json")
    
    # symbol_manager会自动检测配置变化
    updated_symbol = symbol_manager.get_current_symbol()
    print(f"Symbol Manager检测到配置变化: {updated_symbol}")
    
    # 更新Binance客户端
    binance_client.set_current_symbol(updated_symbol)
    client_symbol = binance_client.get_current_symbol()
    print(f"Binance Client已更新: {client_symbol}")
    
    # 步骤4：验证更改
    print("\n✅ 步骤4：验证更改")
    print("-" * 30)
    
    if updated_symbol == client_symbol == new_symbol:
        print(f"✅ 所有组件已成功切换到: {new_symbol}")
        
        # 测试价格获取
        try:
            await binance_client.initialize()
            price = await binance_client.get_current_price()
            print(f"💰 {new_symbol} 当前价格: ${price:,.2f}")
        except Exception as e:
            print(f"⚠️  价格获取测试: {e}")
        
        print(f"\n🎉 配置文件Symbol更改演示完成!")
        print(f"✨ 结果：成功从 {config_symbol} 切换到 {new_symbol}")
        return True
        
    else:
        print(f"❌ 组件同步失败")
        print(f"   Expected: {new_symbol}")
        print(f"   Symbol Manager: {updated_symbol}")
        print(f"   Binance Client: {client_symbol}")
        return False


async def main():
    """主函数"""
    print("🚀 配置文件Symbol更改功能演示")
    print("=" * 60)
    print("此演示将展示如何通过直接修改config.json中的symbol来切换交易对")
    print()
    
    success = await demonstrate_config_symbol_change()
    
    if success:
        print(f"\n💡 使用方法:")
        print(f"1. 直接编辑 config/config.json 文件")
        print(f"2. 修改 AUTO_TRADER.symbol 的值")
        print(f"3. 保存文件")
        print(f"4. 系统会自动检测并应用更改!")
        print(f"   或运行: python apply_config_changes.py")
    else:
        print(f"\n❌ 演示过程中出现问题")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(asyncio.run(main()))