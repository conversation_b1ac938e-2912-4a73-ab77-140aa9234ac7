"""
Unit tests for position sizer module.

Tests the modular position sizing strategies to ensure proper
bet amount calculations under different scenarios.
"""

import pytest
import pandas as pd
import numpy as np

import sys
sys.path.append('/Users/<USER>/PycharmProjects/Mitchquant自动交易策略版本20250810')

from quant.analysis.position_sizer import (
    SizingMode,
    FixedSizer,
    ConfidenceBasedSizer,
    RiskParitySizer,
    KellySizer,
    PositionSizer,
    create_position_sizer_from_config
)


class TestFixedSizer:
    """Test FixedSizer functionality."""
    
    def test_initialization(self):
        """Test sizer initialization."""
        sizer = FixedSizer(fixed_amount=100.0)
        assert sizer.fixed_amount == 100.0
        assert sizer.min_amount == 100.0
        assert sizer.max_amount == 100.0
    
    def test_initialization_with_limits(self):
        """Test sizer initialization with explicit min/max."""
        sizer = FixedSizer(fixed_amount=150.0, min_amount=50.0, max_amount=200.0)
        assert sizer.fixed_amount == 150.0
        assert sizer.min_amount == 50.0
        assert sizer.max_amount == 200.0
    
    def test_calculate_size(self):
        """Test fixed size calculation."""
        sizer = FixedSizer(fixed_amount=100.0)
        
        # Should return fixed amount regardless of confidence
        assert sizer.calculate_size(0.1) == 100.0
        assert sizer.calculate_size(0.5) == 100.0
        assert sizer.calculate_size(0.9) == 100.0
    
    def test_clamping(self):
        """Test that size is clamped to min/max bounds."""
        sizer = FixedSizer(fixed_amount=150.0, min_amount=100.0, max_amount=120.0)
        
        # Fixed amount should be clamped to max
        assert sizer.calculate_size(0.5) == 120.0
        
        sizer2 = FixedSizer(fixed_amount=50.0, min_amount=80.0, max_amount=200.0)
        
        # Fixed amount should be clamped to min  
        assert sizer2.calculate_size(0.5) == 80.0


class TestConfidenceBasedSizer:
    """Test ConfidenceBasedSizer functionality."""
    
    def test_initialization(self):
        """Test sizer initialization."""
        sizer = ConfidenceBasedSizer(
            min_amount=10.0,
            max_amount=100.0,
            min_confidence_for_full=0.8
        )
        
        assert sizer.min_amount == 10.0
        assert sizer.max_amount == 100.0
        assert sizer.min_confidence_for_full == 0.8
        assert sizer.confidence_curve == "linear"
    
    def test_linear_scaling(self):
        """Test linear confidence scaling."""
        sizer = ConfidenceBasedSizer(
            min_amount=10.0,
            max_amount=100.0,
            min_confidence_for_full=0.8,
            confidence_curve="linear"
        )
        
        # Full confidence should give max amount
        assert sizer.calculate_size(0.8) == 100.0
        assert sizer.calculate_size(1.0) == 100.0
        
        # Half the required confidence should give roughly midpoint
        size_at_half = sizer.calculate_size(0.4)  # 0.4/0.8 = 0.5 ratio
        expected = 10.0 + (100.0 - 10.0) * 0.5  # 55.0
        assert abs(size_at_half - expected) < 0.1
        
        # Very low confidence should give min amount
        assert sizer.calculate_size(0.0) == 10.0
    
    def test_exponential_scaling(self):
        """Test exponential confidence scaling."""
        sizer = ConfidenceBasedSizer(
            min_amount=10.0,
            max_amount=100.0,
            min_confidence_for_full=0.8,
            confidence_curve="exponential"
        )
        
        # Exponential curve should be more conservative
        size_linear = ConfidenceBasedSizer(10.0, 100.0, 0.8, "linear").calculate_size(0.4)
        size_exponential = sizer.calculate_size(0.4)
        
        assert size_exponential < size_linear
    
    def test_logarithmic_scaling(self):
        """Test logarithmic confidence scaling.""" 
        sizer = ConfidenceBasedSizer(
            min_amount=10.0,
            max_amount=100.0,
            min_confidence_for_full=0.8,
            confidence_curve="logarithmic"
        )
        
        # Should produce valid results
        size = sizer.calculate_size(0.4)
        assert 10.0 <= size <= 100.0


class TestRiskParitySizer:
    """Test RiskParitySizer functionality."""
    
    def test_initialization(self):
        """Test sizer initialization."""
        sizer = RiskParitySizer(
            min_amount=10.0,
            max_amount=200.0,
            target_risk=0.02,
            lookback_periods=20
        )
        
        assert sizer.min_amount == 10.0
        assert sizer.max_amount == 200.0
        assert sizer.target_risk == 0.02
        assert sizer.lookback_periods == 20
    
    def test_calculate_size_no_market_data(self):
        """Test size calculation without market data (fallback)."""
        sizer = RiskParitySizer(min_amount=10.0, max_amount=100.0)
        
        # Without market data, should fallback to confidence-based sizing
        size = sizer.calculate_size(0.5)
        expected_fallback = 10.0 + (100.0 - 10.0) * 0.5  # 55.0
        assert abs(size - expected_fallback) < 0.1
    
    def test_calculate_size_with_market_data(self):
        """Test size calculation with market data."""
        # Create sample market data with known volatility
        np.random.seed(42)
        prices = [100 * (1 + np.random.normal(0, 0.02)) for _ in range(30)]
        market_data = pd.DataFrame({'close': prices})
        
        sizer = RiskParitySizer(
            min_amount=10.0,
            max_amount=200.0,
            target_risk=0.02,
            lookback_periods=20
        )
        
        size = sizer.calculate_size(0.5, market_data=market_data)
        
        # Should return a valid size within bounds
        assert 10.0 <= size <= 200.0
    
    def test_calculate_size_high_volatility(self):
        """Test that high volatility reduces position size."""
        # High volatility market data
        np.random.seed(42)
        high_vol_prices = [100 * (1 + np.random.normal(0, 0.1)) for _ in range(30)]
        high_vol_data = pd.DataFrame({'close': high_vol_prices})
        
        # Low volatility market data
        low_vol_prices = [100 * (1 + np.random.normal(0, 0.01)) for _ in range(30)]
        low_vol_data = pd.DataFrame({'close': low_vol_prices})
        
        sizer = RiskParitySizer(min_amount=10.0, max_amount=200.0, target_risk=0.02)
        
        size_high_vol = sizer.calculate_size(0.5, market_data=high_vol_data)
        size_low_vol = sizer.calculate_size(0.5, market_data=low_vol_data)
        
        # High volatility should result in smaller position
        assert size_high_vol < size_low_vol


class TestKellySizer:
    """Test KellySizer functionality."""
    
    def test_initialization(self):
        """Test sizer initialization."""
        sizer = KellySizer(
            min_amount=10.0,
            max_amount=100.0,
            win_rate=0.6,
            avg_win=1.5,
            avg_loss=1.0,
            kelly_fraction=0.25
        )
        
        assert sizer.min_amount == 10.0
        assert sizer.max_amount == 100.0
        assert sizer.win_rate == 0.6
        assert sizer.avg_win == 1.5
        assert sizer.avg_loss == 1.0
        assert sizer.kelly_fraction == 0.25
    
    def test_calculate_size_profitable_strategy(self):
        """Test Kelly sizing with profitable parameters."""
        sizer = KellySizer(
            min_amount=10.0,
            max_amount=1000.0,
            win_rate=0.6,  # 60% win rate
            avg_win=2.0,   # 2:1 reward
            avg_loss=1.0,  # 1:1 risk
            kelly_fraction=0.25
        )
        
        # High confidence should increase effective win rate and position size
        size_high_conf = sizer.calculate_size(0.8)
        size_low_conf = sizer.calculate_size(0.4)
        
        assert size_high_conf > size_low_conf
        assert 10.0 <= size_high_conf <= 1000.0
        assert 10.0 <= size_low_conf <= 1000.0
    
    def test_calculate_size_break_even_strategy(self):
        """Test Kelly sizing with break-even parameters."""
        sizer = KellySizer(
            min_amount=10.0,
            max_amount=100.0,
            win_rate=0.5,  # 50% win rate
            avg_win=1.0,   # 1:1 reward/risk
            avg_loss=1.0,
            kelly_fraction=0.25
        )
        
        size = sizer.calculate_size(0.5)
        
        # Break-even strategy should result in minimal position
        assert size == 10.0  # Should clamp to minimum
    
    def test_calculate_size_invalid_parameters(self):
        """Test Kelly sizing with invalid parameters."""
        sizer = KellySizer(
            min_amount=10.0,
            max_amount=100.0,
            win_rate=0.6,
            avg_win=1.0,
            avg_loss=0.0,  # Invalid: zero loss
            kelly_fraction=0.25
        )
        
        size = sizer.calculate_size(0.5)
        assert size == 10.0  # Should fallback to minimum


class TestPositionSizer:
    """Test main PositionSizer class."""
    
    def test_initialization_fixed_mode(self):
        """Test initialization with fixed sizing mode."""
        config = {
            'mode': 'fixed',
            'min_amount': 10,
            'max_amount': 100,
            'fixed_amount': 50
        }
        
        sizer = PositionSizer(config)
        assert sizer.mode == SizingMode.FIXED
        assert isinstance(sizer.sizer, FixedSizer)
    
    def test_initialization_confidence_mode(self):
        """Test initialization with confidence-based sizing mode."""
        config = {
            'mode': 'confidence_based',
            'min_amount': 10,
            'max_amount': 100,
            'min_confidence_for_full': 0.8,
            'confidence_curve': 'linear'
        }
        
        sizer = PositionSizer(config)
        assert sizer.mode == SizingMode.CONFIDENCE_BASED
        assert isinstance(sizer.sizer, ConfidenceBasedSizer)
    
    def test_initialization_risk_parity_mode(self):
        """Test initialization with risk parity sizing mode.""" 
        config = {
            'mode': 'risk_parity',
            'min_amount': 10,
            'max_amount': 200,
            'target_risk': 0.02,
            'lookback_periods': 20
        }
        
        sizer = PositionSizer(config)
        assert sizer.mode == SizingMode.RISK_PARITY
        assert isinstance(sizer.sizer, RiskParitySizer)
    
    def test_initialization_kelly_mode(self):
        """Test initialization with Kelly criterion sizing mode."""
        config = {
            'mode': 'kelly',
            'min_amount': 10,
            'max_amount': 100,
            'win_rate': 0.55,
            'avg_win': 1.2,
            'avg_loss': 1.0,
            'kelly_fraction': 0.25
        }
        
        sizer = PositionSizer(config)
        assert sizer.mode == SizingMode.KELLY
        assert isinstance(sizer.sizer, KellySizer)
    
    def test_initialization_unknown_mode(self):
        """Test initialization with unknown sizing mode falls back to fixed."""
        config = {
            'mode': 'unknown_mode',
            'min_amount': 10,
            'max_amount': 100
        }
        
        sizer = PositionSizer(config)
        # Should fallback to fixed sizing
        assert isinstance(sizer.sizer, FixedSizer)
    
    def test_calculate_position_size(self):
        """Test position size calculation."""
        config = {
            'mode': 'confidence_based',
            'min_amount': 20,
            'max_amount': 200,
            'min_confidence_for_full': 0.8
        }
        
        sizer = PositionSizer(config)
        size = sizer.calculate_position_size(confidence=0.6)
        
        assert 20 <= size <= 200
    
    def test_update_performance_stats_kelly(self):
        """Test updating performance stats for Kelly sizer."""
        config = {
            'mode': 'kelly',
            'min_amount': 10,
            'max_amount': 100,
            'win_rate': 0.5,
            'avg_win': 1.0,
            'avg_loss': 1.0
        }
        
        sizer = PositionSizer(config)
        
        # Update stats
        sizer.update_performance_stats(win_rate=0.65, avg_win=1.3, avg_loss=0.8)
        
        # Verify Kelly sizer was updated
        assert sizer.sizer.win_rate == 0.65
        assert sizer.sizer.avg_win == 1.3
        assert sizer.sizer.avg_loss == 0.8
    
    def test_update_performance_stats_non_kelly(self):
        """Test updating performance stats for non-Kelly sizer (should not crash)."""
        config = {
            'mode': 'fixed',
            'fixed_amount': 100
        }
        
        sizer = PositionSizer(config)
        
        # Should not crash for non-Kelly sizers
        sizer.update_performance_stats(win_rate=0.65, avg_win=1.3, avg_loss=0.8)


class TestCreatePositionSizerFromConfig:
    """Test config-based position sizer creation."""
    
    def test_create_from_simple_bet_config(self):
        """Test creation from legacy simple bet configuration."""
        config_dict = {
            'SIMPLE_BET_CONTROL': {
                'enabled': True,
                'fixed_bet_amount': 150.0
            },
            'AUTO_TRADER': {
                'min_order_usdt': 10
            }
        }
        
        sizer = create_position_sizer_from_config(config_dict)
        
        # Should create fixed sizer
        assert isinstance(sizer.sizer, FixedSizer)
        assert sizer.sizer.fixed_amount == 150.0
    
    def test_create_from_confidence_config(self):
        """Test creation from confidence-based configuration."""
        config_dict = {
            'SIMPLE_BET_CONTROL': {
                'enabled': False,
                'min_confidence_for_full_bet': 0.75
            },
            'RISK_MANAGEMENT': {
                'base_position_size_usdt': 200
            },
            'AUTO_TRADER': {
                'min_order_usdt': 15
            }
        }
        
        sizer = create_position_sizer_from_config(config_dict)
        
        # Should create confidence-based sizer
        assert isinstance(sizer.sizer, ConfidenceBasedSizer)
        assert sizer.min_amount == 15
        assert sizer.max_amount == 200
    
    def test_create_with_missing_config(self):
        """Test creation with missing configuration sections."""
        config_dict = {}  # Empty config
        
        sizer = create_position_sizer_from_config(config_dict)
        
        # Should still create a valid sizer with defaults
        assert sizer is not None
        assert isinstance(sizer, PositionSizer)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])