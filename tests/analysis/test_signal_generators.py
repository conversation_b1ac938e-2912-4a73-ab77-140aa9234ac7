"""
Unit tests for signal generators module.

Tests the modular signal generation strategies to ensure they work correctly
and maintain expected behavior after refactoring.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import sys
sys.path.append('/Users/<USER>/PycharmProjects/Mitchquant自动交易策略版本20250810')

from quant.analysis.signal_generators import (
    SignalDirection,
    SignalStrength,
    SignalResult,
    BaseSignalGenerator,
    ConfidenceScoringSignalGenerator,
    SimpleSignalGenerator,
    TrendFollowingSignalGenerator,
    SignalGeneratorFactory
)


class TestSignalResult:
    """Test SignalResult data structure."""
    
    def test_signal_result_creation(self):
        """Test creating a SignalResult object."""
        result = SignalResult(
            direction=SignalDirection.LONG,
            confidence=0.75,
            strength=SignalStrength.STRONG,
            entry_price=50000.0,
            market_state="trending_up",
            trigger_pattern="test_pattern",
            indicators=["rsi", "macd"],
            metadata={"test_key": "test_value"}
        )
        
        assert result.direction == SignalDirection.LONG
        assert result.confidence == 0.75
        assert result.strength == SignalStrength.STRONG
        assert result.entry_price == 50000.0
        assert result.market_state == "trending_up"
        assert result.trigger_pattern == "test_pattern"
        assert result.indicators == ["rsi", "macd"]
        assert result.metadata["test_key"] == "test_value"
        assert result.timestamp is not None
    
    def test_signal_result_to_dict(self):
        """Test converting SignalResult to dictionary."""
        result = SignalResult(
            direction=SignalDirection.SHORT,
            confidence=0.6,
            strength=SignalStrength.MEDIUM,
            entry_price=48000.0,
            market_state="trending_down",
            trigger_pattern="test",
            indicators=["sma"],
            metadata={"extra": "data"}
        )
        
        signal_dict = result.to_dict("BTCUSDT", 100.0)
        
        assert signal_dict["symbol"] == "BTCUSDT"
        assert signal_dict["direction"] == "SHORT"
        assert signal_dict["confidence_score"] == 0.6
        assert signal_dict["entry_price"] == 48000.0
        assert signal_dict["suggested_bet"] == 100.0
        assert signal_dict["market_state"] == "trending_down"
        assert signal_dict["trigger_pattern"] == "test"
        assert signal_dict["confirmed_indicators"] == ["sma"]


@pytest.fixture
def sample_market_data():
    """Create sample market data for testing."""
    dates = pd.date_range(start='2025-01-01', periods=50, freq='30T')
    
    # Generate realistic price data
    np.random.seed(42)  # For reproducible tests
    base_price = 50000
    price_changes = np.random.normal(0, 0.01, 50)  # 1% volatility
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    df = pd.DataFrame({
        'open': [p * 0.999 for p in prices],
        'high': [p * 1.002 for p in prices],
        'low': [p * 0.998 for p in prices],
        'close': prices,
        'volume': np.random.uniform(100, 1000, 50)
    }, index=dates)
    
    return df


@pytest.fixture
def empty_market_data():
    """Create empty market data for error testing."""
    return pd.DataFrame()


@pytest.fixture
def invalid_market_data():
    """Create invalid market data for error testing."""
    return pd.DataFrame({
        'close': [100, 200, 150],
        # Missing other required columns
    })


class TestSimpleSignalGenerator:
    """Test SimpleSignalGenerator functionality."""
    
    def test_initialization(self):
        """Test generator initialization."""
        generator = SimpleSignalGenerator(confidence_threshold=0.4)
        assert generator.confidence_threshold == 0.4
        assert generator.get_strategy_name() == "simple_price_action"
    
    def test_generate_signal_success(self, sample_market_data):
        """Test successful signal generation."""
        generator = SimpleSignalGenerator()
        signal = generator.generate_signal(sample_market_data)
        
        assert signal is not None
        assert isinstance(signal, SignalResult)
        assert signal.direction in [SignalDirection.LONG, SignalDirection.SHORT]
        assert 0.2 <= signal.confidence <= 0.8
        assert signal.entry_price > 0
        assert signal.trigger_pattern == "simple_price_action"
        assert "price_action" in signal.indicators
    
    def test_generate_signal_empty_data(self, empty_market_data):
        """Test signal generation with empty data."""
        generator = SimpleSignalGenerator()
        signal = generator.generate_signal(empty_market_data)
        
        assert signal is None
    
    def test_generate_signal_invalid_data(self, invalid_market_data):
        """Test signal generation with invalid data."""
        generator = SimpleSignalGenerator()
        signal = generator.generate_signal(invalid_market_data)
        
        assert signal is None
    
    def test_validate_market_data(self, sample_market_data):
        """Test market data validation."""
        generator = SimpleSignalGenerator()
        
        # Valid data
        assert generator._validate_market_data(sample_market_data) is True
        
        # Empty data
        assert generator._validate_market_data(pd.DataFrame()) is False
        
        # Missing columns
        invalid_df = pd.DataFrame({'close': [1, 2, 3]})
        assert generator._validate_market_data(invalid_df) is False


class TestTrendFollowingSignalGenerator:
    """Test TrendFollowingSignalGenerator functionality."""
    
    def test_initialization(self):
        """Test generator initialization with custom parameters."""
        generator = TrendFollowingSignalGenerator(
            confidence_threshold=0.5,
            short_window=3,
            long_window=10
        )
        
        assert generator.confidence_threshold == 0.5
        assert generator.short_window == 3
        assert generator.long_window == 10
        assert generator.get_strategy_name() == "trend_following"
    
    def test_generate_signal_sufficient_data(self, sample_market_data):
        """Test signal generation with sufficient data."""
        generator = TrendFollowingSignalGenerator(short_window=5, long_window=20)
        signal = generator.generate_signal(sample_market_data)
        
        if signal is not None:  # May be None if trend not clear
            assert isinstance(signal, SignalResult)
            assert signal.direction in [SignalDirection.LONG, SignalDirection.SHORT]
            assert signal.confidence > 0
            assert signal.trigger_pattern == "trend_following"
            assert "MA5" in signal.indicators
            assert "MA20" in signal.indicators
    
    def test_generate_signal_insufficient_data(self):
        """Test signal generation with insufficient data."""
        # Create small dataset
        small_data = pd.DataFrame({
            'open': [100, 101],
            'high': [102, 103],
            'low': [99, 100],
            'close': [101, 102],
            'volume': [1000, 1100]
        })
        
        generator = TrendFollowingSignalGenerator(long_window=20)
        signal = generator.generate_signal(small_data)
        
        assert signal is None


class TestConfidenceScoringSignalGenerator:
    """Test ConfidenceScoringSignalGenerator functionality."""
    
    @patch('quant.confidence_scorer.ConfidenceScorer')
    def test_initialization(self, mock_confidence_scorer):
        """Test generator initialization."""
        generator = ConfidenceScoringSignalGenerator(confidence_threshold=0.6)
        
        assert generator.confidence_threshold == 0.6
        assert generator.get_strategy_name() == "confidence_scoring"
        mock_confidence_scorer.assert_called_once()
    
    @patch('quant.confidence_scorer.ConfidenceScorer')
    def test_generate_signal_high_confidence(self, mock_confidence_scorer, sample_market_data):
        """Test signal generation with high confidence."""
        # Mock confidence scorer
        mock_confidence_obj = Mock()
        mock_confidence_obj.overall_confidence = 0.8
        mock_confidence_obj.trend_score = 0.7
        mock_confidence_obj.momentum_score = 0.75
        mock_confidence_obj.volatility_score = 0.6
        mock_confidence_obj.volume_score = 0.65
        mock_confidence_obj.market_regime_score = 0.7
        mock_confidence_obj.calculation_details = {'market_regime': 'bullish'}
        mock_confidence_obj.indicator_scores = {'rsi': 0.8, 'macd': 0.7}
        
        mock_scorer_instance = mock_confidence_scorer.return_value
        mock_scorer_instance.calculate_confidence.return_value = mock_confidence_obj
        
        # Mock signal strength
        mock_strength = Mock()
        mock_strength.value = 3
        mock_scorer_instance.get_signal_strength.return_value = mock_strength
        
        generator = ConfidenceScoringSignalGenerator(confidence_threshold=0.5)
        signal = generator.generate_signal(sample_market_data)
        
        assert signal is not None
        assert signal.confidence == 0.8
        assert signal.direction in [SignalDirection.LONG, SignalDirection.SHORT]
        assert signal.trigger_pattern == "intelligent_confidence_scoring"
        assert 'rsi' in signal.indicators
        assert 'macd' in signal.indicators
    
    @patch('quant.confidence_scorer.ConfidenceScorer')
    def test_generate_signal_low_confidence_fallback(self, mock_confidence_scorer, sample_market_data):
        """Test fallback signal generation with low confidence."""
        # Mock low confidence
        mock_confidence_obj = Mock()
        mock_confidence_obj.overall_confidence = 0.2  # Below threshold
        mock_confidence_obj.calculation_details = {'market_regime': 'uncertain'}
        mock_confidence_obj.indicator_scores = {'rsi': 0.3}
        
        mock_scorer_instance = mock_confidence_scorer.return_value
        mock_scorer_instance.calculate_confidence.return_value = mock_confidence_obj
        
        generator = ConfidenceScoringSignalGenerator(confidence_threshold=0.5)
        signal = generator.generate_signal(sample_market_data)
        
        # Should generate fallback signal
        assert signal is not None
        assert signal.confidence >= 0.25  # Minimum fallback confidence
        assert signal.trigger_pattern == "confidence_fallback"
        assert signal.strength == SignalStrength.WEAK
    
    def test_determine_signal_direction(self):
        """Test signal direction determination logic."""
        generator = ConfidenceScoringSignalGenerator()
        
        # Mock confidence object for bullish signal
        bullish_obj = Mock()
        bullish_obj.trend_score = 0.7
        bullish_obj.momentum_score = 0.8
        
        direction = generator._determine_signal_direction(bullish_obj)
        assert direction == SignalDirection.LONG
        
        # Mock confidence object for bearish signal
        bearish_obj = Mock()
        bearish_obj.trend_score = 0.3
        bearish_obj.momentum_score = 0.2
        
        direction = generator._determine_signal_direction(bearish_obj)
        assert direction == SignalDirection.SHORT


class TestSignalGeneratorFactory:
    """Test SignalGeneratorFactory functionality."""
    
    def test_create_generator_confidence_scoring(self):
        """Test creating confidence scoring generator."""
        generator = SignalGeneratorFactory.create_generator("confidence_scoring")
        assert isinstance(generator, ConfidenceScoringSignalGenerator)
    
    def test_create_generator_simple(self):
        """Test creating simple generator."""
        generator = SignalGeneratorFactory.create_generator("simple")
        assert isinstance(generator, SimpleSignalGenerator)
    
    def test_create_generator_trend_following(self):
        """Test creating trend following generator."""
        generator = SignalGeneratorFactory.create_generator("trend_following")
        assert isinstance(generator, TrendFollowingSignalGenerator)
    
    def test_create_generator_with_params(self):
        """Test creating generator with custom parameters."""
        generator = SignalGeneratorFactory.create_generator(
            "trend_following",
            confidence_threshold=0.6,
            short_window=3,
            long_window=15
        )
        
        assert generator.confidence_threshold == 0.6
        assert generator.short_window == 3
        assert generator.long_window == 15
    
    def test_create_generator_unknown_strategy(self):
        """Test creating generator with unknown strategy."""
        with pytest.raises(ValueError, match="Unknown strategy"):
            SignalGeneratorFactory.create_generator("unknown_strategy")
    
    def test_get_available_strategies(self):
        """Test getting available strategies."""
        strategies = SignalGeneratorFactory.get_available_strategies()
        
        expected_strategies = ["confidence_scoring", "simple", "trend_following"]
        for strategy in expected_strategies:
            assert strategy in strategies


class TestBaseSignalGenerator:
    """Test BaseSignalGenerator abstract functionality."""
    
    def test_cannot_instantiate_directly(self):
        """Test that BaseSignalGenerator cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseSignalGenerator()
    
    def test_base_methods_available(self):
        """Test that base methods are available to subclasses."""
        generator = SimpleSignalGenerator()
        
        # Test base methods
        assert hasattr(generator, '_get_current_price')
        assert hasattr(generator, '_validate_market_data')
        assert generator.confidence_threshold == 0.3  # Default value


if __name__ == "__main__":
    pytest.main([__file__, "-v"])