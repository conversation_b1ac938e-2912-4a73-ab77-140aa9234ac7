#!/usr/bin/env python3
"""
Analyze the last 4 settled losing trades to diagnose failure patterns.
Outputs a structured JSON report per trade with:
- core fields (timestamps, direction, prices, pnl)
- decision_details snapshot (RSI, MACD, BB, entry window info if present)
- confidence breakdown if present
- post-signal kline performance (next 1-2 30m candles)
"""
import asyncio
import json
from datetime import datetime, timezone
import sqlite3
from pathlib import Path

from quant.binance_client import binance_client

DB_PATH = Path("data/trading_system.db")

async def fetch_post_klines(symbol: str, signal_ts_iso: str, limit: int = 3):
    try:
        if 'T' in signal_ts_iso:
            ts = datetime.fromisoformat(signal_ts_iso.replace('Z', '+00:00'))
        else:
            ts = datetime.fromisoformat(signal_ts_iso)
        # Align to 30m kline open time (approximate): subtract few minutes
        start_ms = int(ts.timestamp() * 1000)
        kl = await binance_client.get_historical_klines(symbol, '30m', start_ms, limit=limit)
        return kl
    except Exception as e:
        return {"error": str(e)}

async def main():
    if not DB_PATH.exists():
        print(json.dumps({"error": "DB not found"}))
        return

    # Connect DB
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cur = conn.cursor()

    # Get last 4 settled losing trades
    cur.execute(
        """
        SELECT id, signal_timestamp, direction, entry_price, exit_price, pnl, symbol,
               confidence_score, market_state, trigger_pattern,
               decision_details, confidence_breakdown, exit_timestamp
        FROM trade_history
        WHERE status = 'LOSS' AND exit_timestamp IS NOT NULL
        ORDER BY exit_timestamp DESC
        LIMIT 4
        """
    )
    rows = cur.fetchall()
    conn.close()

    # Initialize binance client
    await binance_client.initialize()

    report = {"count": len(rows), "items": []}

    for r in rows:
        item = {
            "id": r["id"],
            "signal_timestamp": r["signal_timestamp"],
            "exit_timestamp": r["exit_timestamp"],
            "direction": r["direction"],
            "symbol": r["symbol"],
            "entry_price": r["entry_price"],
            "exit_price": r["exit_price"],
            "pnl": r["pnl"],
            "confidence_score": r["confidence_score"],
            "market_state": r["market_state"],
            "trigger_pattern": r["trigger_pattern"],
            "decision_details": None,
            "confidence_breakdown": None,
            "post_klines": None,
            "post_moves": None
        }
        # parse JSON fields
        try:
            item["decision_details"] = json.loads(r["decision_details"]) if r["decision_details"] else None
        except Exception:
            item["decision_details"] = {"_parse_error": True}
        try:
            item["confidence_breakdown"] = json.loads(r["confidence_breakdown"]) if r["confidence_breakdown"] else None
        except Exception:
            item["confidence_breakdown"] = {"_parse_error": True}

        # fetch post klines
        kl = await fetch_post_klines(item["symbol"], item["signal_timestamp"], limit=3)
        item["post_klines"] = kl

        # compute simple post moves based on first kline after signal
        try:
            if isinstance(kl, list) and len(kl) >= 2:
                # Binance returns array of klines; take the next one (index 1)
                # Schema: [open_time, open, high, low, close, volume, close_time, ...]
                after1 = float(kl[1][4])
                entry = float(item["entry_price"])
                if entry > 0:
                    if item["direction"] == "LONG":
                        delta_pct = (after1 - entry) / entry
                    else:
                        delta_pct = (entry - after1) / entry
                    item["post_moves"] = {"after1_close": after1, "delta_pct_after1": round(delta_pct*100, 3)}
        except Exception:
            item["post_moves"] = {"_calc_error": True}

        report["items"].append(item)

    print(json.dumps(report, ensure_ascii=False, indent=2, default=str))

if __name__ == "__main__":
    asyncio.run(main())
