#!/bin/bash
# OKX交易系统快速启动脚本
# 基于OKX API集成规范的简化启动方式

set -e
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_ROOT"

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] $1${NC}"
}

# 快速检查
quick_check() {
    # 检查.env文件
    if [ ! -f ".env" ]; then
        error ".env文件不存在，请先配置OKX API密钥"
    fi
    
    # 加载环境变量
    set -a
    source .env
    set +a
    
    # 检查OKX密钥
    if [ -z "$OKX_ACCESS_KEY" ] || [ -z "$OKX_SECRET_KEY" ] || [ -z "$OKX_PASSPHRASE" ]; then
        error "OKX API密钥配置不完整"
    fi
    
    log "✅ 环境检查通过"
}

# 激活虚拟环境
activate_env() {
    if [ -f "trading_system_venv/bin/activate" ]; then
        source trading_system_venv/bin/activate
        log "✅ 虚拟环境已激活"
    else
        log "⚠️ 使用系统Python环境"
    fi
}

# 启动系统
start_system() {
    export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
    export PYTHONUNBUFFERED=1
    
    # 选择启动方式
    if [ -f "start_okx_trading_system.py" ]; then
        info "🚀 启动OKX交易系统..."
        python start_okx_trading_system.py
    elif [ -f "main.py" ]; then
        info "🚀 启动主程序..."
        python main.py
    else
        error "找不到主程序文件"
    fi
}

# 主函数
main() {
    echo ""
    echo "🚀 OKX交易系统快速启动"
    echo "========================"
    
    quick_check
    activate_env
    start_system
}

# 错误处理
trap 'error "启动被中断"' INT TERM

# 执行
main "$@"