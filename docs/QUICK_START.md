# 🚀 重构交易系统快速操作指南

## 📱 一键操作命令

### 系统部署
```bash
# 首次部署（只需运行一次）
./scripts/deploy_refactored.sh development
```

### 系统启动
```bash
# 启动开发环境
./scripts/start_system.sh config.development.json

# 启动生产环境  
./scripts/start_system.sh config.production.json

# 使用默认配置启动
./scripts/start_system.sh
```

### 系统停止
```bash
# 停止系统
./scripts/stop_system.sh
```

### 系统重启
```bash
# 重启开发环境
./scripts/restart_system.sh config.development.json

# 重启生产环境
./scripts/restart_system.sh config.production.json

# 后台重启
./scripts/restart_system.sh config.development.json --background
```

### 系统监控
```bash
# 健康检查
./scripts/health_check.sh

# 查看实时日志
tail -f logs/trading_system.log

# 查看错误日志
grep ERROR logs/trading_system.log | tail -10
```

## 🔧 常用操作

### 修改配置
```bash
# 1. 停止系统
./scripts/stop_system.sh

# 2. 编辑配置
nano config.development.json

# 3. 重启系统  
./scripts/restart_system.sh config.development.json
```

### 查看系统状态
```bash
# 快速状态检查
ps aux | grep main_refactored

# 详细健康检查
./scripts/health_check.sh

# 查看系统资源
python3 -c "import psutil; print(f'CPU: {psutil.cpu_percent()}%, 内存: {psutil.virtual_memory().percent}%')"
```

### 故障排除
```bash
# 强制停止所有进程
pkill -f "main_refactored.py"

# 检查依赖
source trading_system_venv/bin/activate
pip install -r requirements.txt

# 验证配置
python -m json.tool config.json

# 回滚到原系统
python main.py config.json
```

## 📊 系统监控面板

### 实时监控命令
```bash
# 方法1: 使用watch命令实时监控
watch -n 5 './scripts/health_check.sh'

# 方法2: 自定义监控脚本
while true; do
    clear
    echo "=== 交易系统实时状态 $(date) ==="
    echo ""
    
    # 进程状态
    echo "🔄 进程状态:"
    if pgrep -f "main_refactored.py" > /dev/null; then
        echo "  ✅ 系统正在运行"
    else
        echo "  ❌ 系统未运行"
    fi
    
    # 最新日志
    echo ""
    echo "📝 最新日志 (最近5行):"
    tail -5 logs/trading_system.log 2>/dev/null || echo "  无日志文件"
    
    # 系统资源
    echo ""
    echo "💻 系统资源:"
    python3 -c "
import psutil
print(f'  CPU: {psutil.cpu_percent():.1f}%')  
print(f'  内存: {psutil.virtual_memory().percent:.1f}%')
print(f'  磁盘: {psutil.disk_usage(\".\").percent:.1f}%')
" 2>/dev/null || echo "  无法获取资源信息"
    
    echo ""
    echo "按 Ctrl+C 退出监控"
    sleep 10
done
```

## 📅 日常维护计划

### 每日检查 (2分钟)
```bash
# 1. 检查系统是否正常运行
./scripts/health_check.sh | grep "系统健康状态"

# 2. 查看是否有错误  
grep ERROR logs/trading_system.log | tail -1

# 3. 检查磁盘空间
df -h | grep -E "/$|disk"
```

### 每周维护 (5分钟)
```bash
# 1. 清理旧日志 (保留7天)
find logs/ -name "*.log" -mtime +7 -delete

# 2. 检查系统更新
git status
git log --oneline -5

# 3. 备份配置
cp config.json config.json.backup.$(date +%Y%m%d)
```

### 每月维护 (15分钟)
```bash
# 1. 运行完整性能测试
source trading_system_venv/bin/activate
python tests/integration/performance_comparison.py

# 2. 更新依赖包
pip list --outdated
# pip install --upgrade package_name  # 谨慎更新

# 3. 系统清理
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
```

## 🚨 紧急操作

### 紧急停机
```bash
# 立即停止所有相关进程
pkill -9 -f "main_refactored.py"
launchctl stop com.trading.system.refactored 2>/dev/null || true
sudo systemctl stop trading-system-refactored 2>/dev/null || true
```

### 紧急恢复
```bash
# 方法1: 重启重构系统
./scripts/restart_system.sh config.development.json

# 方法2: 回滚到原系统
python main.py config.json

# 方法3: 最小配置启动
source trading_system_venv/bin/activate
python -c "
from quant.config_manager import ConfigManager
from quant.trading_system_orchestrator import TradingSystemOrchestrator
config = ConfigManager('config.json')
orchestrator = TradingSystemOrchestrator(config)
print('系统可以正常初始化')
"
```

### 故障诊断
```bash
# 生成故障报告
echo "=== 故障报告 $(date) ===" > incident_report.txt
echo "" >> incident_report.txt

echo "进程状态:" >> incident_report.txt
ps aux | grep python >> incident_report.txt
echo "" >> incident_report.txt

echo "最近错误:" >> incident_report.txt  
tail -50 logs/trading_system.log | grep ERROR >> incident_report.txt
echo "" >> incident_report.txt

echo "系统资源:" >> incident_report.txt
python3 -c "
import psutil
print(f'CPU: {psutil.cpu_percent()}%')
print(f'内存: {psutil.virtual_memory().percent}%')  
print(f'磁盘: {psutil.disk_usage(\".\").percent}%')
" >> incident_report.txt

echo "故障报告已生成: incident_report.txt"
```

## 📞 支持联系

### 日志位置
- 主要日志: `logs/trading_system.log`
- 错误日志: `logs/error.log`  
- 后台日志: `logs/nohup.out`

### 配置文件位置  
- 主配置: `config.json`
- 开发配置: `config.development.json`
- 生产配置: `config.production.json`
- 环境变量: `.env`

### 脚本位置
- 启动: `./scripts/start_system.sh`
- 停止: `./scripts/stop_system.sh`  
- 重启: `./scripts/restart_system.sh`
- 健康检查: `./scripts/health_check.sh`
- 部署: `./scripts/deploy_refactored.sh`

---

**💡 提示**: 建议将这些命令添加到你的 shell 别名中，例如：

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
alias ts-start='./scripts/start_system.sh'
alias ts-stop='./scripts/stop_system.sh'  
alias ts-restart='./scripts/restart_system.sh'
alias ts-health='./scripts/health_check.sh'
alias ts-logs='tail -f logs/trading_system.log'
```