# 限价单生产环境部署检查清单

## 🔴 必须确认的关键设置

### 1. API配置验证
```bash
# 确认.env文件中的API密钥是否为生产环境密钥
BINANCE_ACCESS_KEY=你的真实API密钥
BINANCE_SECRET_KEY=你的真实秘密密钥

# 确认API权限设置
- ✅ 启用现货交易 (Enable Spot Trading)
- ✅ 启用合约交易 (Enable Futures Trading)
- ✅ IP白名单设置（如果有）
```

### 2. 环境变量设置
```bash
# 修改.env文件
ENVIRONMENT=production  # 不是 development
DEBUG=false            # 关闭调试模式
```

### 3. 配置文件核心参数 (config/config.json)

```json
{
  "AUTO_TRADER": {
    "ORDER_MODE": "SMART",  // 建议从 SMART 开始，自动选择
    "ENABLED": true,
    "POSITION_SIZE_USDT": 100,  // 建议从小额开始测试
    "MAX_OPEN_POSITIONS": 1     // 初期限制同时持仓数
  },
  
  "MAKER_ORDER": {
    "enabled": true,
    "price_strategy": "BALANCED",  // 初期使用平衡策略
    "buy_offset_bps": 10.0,       // 买单低于市价10个基点(0.1%)
    "sell_offset_bps": 10.0,       // 卖单高于市价10个基点(0.1%)
    "initial_timeout": 30,         // 30秒超时（可根据需要调整）
    "retry_timeout": 20,           // 重试超时20秒
    "max_retries": 2,              // 最多重试2次（避免过度重试）
    "allow_partial_fill": true,    // 允许部分成交
    "emergency_threshold": 60,     // 60秒后使用市价单
    "allow_market_fallback": true, // 必须开启，保证成交
    "use_orderbook": false,        // 初期关闭，简化逻辑
    "max_slippage_bps": 50.0      // 最大滑点保护(0.5%)
  },
  
  "RISK_MANAGEMENT": {
    "MAX_POSITION_SIZE_USDT": 1000,  // 单笔最大仓位
    "STOP_LOSS_PERCENTAGE": 2.0,     // 止损2%
    "TAKE_PROFIT_PERCENTAGE": 3.0,   // 止盈3%
    "MAX_DAILY_LOSS_USDT": 100      // 每日最大亏损限制
  }
}
```

## 🟡 建议的初始参数设置

### 币种特定配置
```json
{
  "SYMBOL_OVERRIDES": {
    "BTCUSDT": {
      "offset_bps": 5.0,        // BTC流动性好，可以更小偏移
      "timeout_seconds": 20,
      "tick_size": 0.01,        // 价格精度
      "min_quantity": 0.001     // 最小下单量
    },
    "ETHUSDT": {
      "offset_bps": 8.0,
      "timeout_seconds": 25,
      "tick_size": 0.01,
      "min_quantity": 0.001
    }
  }
}
```

### 分阶段参数调整计划

#### 第1天：保守测试
```json
{
  "ORDER_MODE": "LIMIT",           // 强制使用限价单测试
  "POSITION_SIZE_USDT": 50,       // 小额测试
  "buy_offset_bps": 20.0,         // 较大偏移确保成交
  "sell_offset_bps": 20.0,
  "max_retries": 1                // 减少重试
}
```

#### 第3天：正常运行
```json
{
  "ORDER_MODE": "SMART",           // 智能模式
  "POSITION_SIZE_USDT": 200,      // 增加仓位
  "buy_offset_bps": 10.0,         // 标准偏移
  "sell_offset_bps": 10.0,
  "max_retries": 2
}
```

#### 第7天：优化调整
```json
{
  "ORDER_MODE": "SMART",
  "POSITION_SIZE_USDT": 500,      // 正常仓位
  "buy_offset_bps": 5.0,          // 根据成交率优化
  "sell_offset_bps": 5.0,
  "use_orderbook": true           // 启用订单簿优化
}
```

## 🟢 部署前测试命令

### 1. 验证配置
```bash
# 检查配置文件语法
python3 -c "import json; json.load(open('config/config.json'))"

# 验证环境变量
python3 -c "import os; print('ENV:', os.getenv('ENVIRONMENT')); print('API Key exists:', bool(os.getenv('BINANCE_ACCESS_KEY')))"
```

### 2. 连接测试
```bash
# 测试API连接（创建test_connection.py）
python3 scripts/test_binance_connection.py
```

### 3. 小额实盘测试
```bash
# 使用最小金额进行一次真实交易测试
python3 scripts/test_small_trade.py --symbol BTCUSDT --usdt 10
```

## 🔵 监控指标设置

### 关键指标阈值
```python
MONITORING_THRESHOLDS = {
    "min_fill_rate": 0.7,           # 成交率低于70%告警
    "max_retry_rate": 0.3,          # 重试率高于30%告警
    "max_fallback_rate": 0.2,       # 市价单回退率高于20%告警
    "max_order_delay_seconds": 45,  # 订单延迟超过45秒告警
    "min_daily_profit_usdt": -50    # 日亏损超过50 USDT告警
}
```

### 监控脚本
```bash
# 实时监控脚本
python3 scripts/monitor_limit_orders.py --interval 60

# 每日报告
python3 scripts/daily_trading_report.py
```

## ⚠️ 风险控制设置

### 紧急停止条件
```json
{
  "EMERGENCY_STOP": {
    "max_consecutive_failures": 5,   // 连续5次失败停止
    "max_hourly_loss_usdt": 50,     // 小时亏损超过50 USDT
    "min_api_success_rate": 0.9,    // API成功率低于90%
    "circuit_breaker_minutes": 30   // 熔断30分钟
  }
}
```

### 降级策略
```python
# 自动降级条件
if fill_rate < 0.5:  # 成交率低于50%
    # 自动切换回市价单模式
    config["ORDER_MODE"] = "MARKET"
    notify_admin("限价单成交率过低，已切换到市价单模式")
```

## 📋 部署步骤

### 1. 备份当前配置
```bash
cp config/config.json config/config.backup.$(date +%Y%m%d).json
cp .env .env.backup.$(date +%Y%m%d)
```

### 2. 更新配置
```bash
# 编辑配置文件
vim config/config.json
vim .env
```

### 3. 重启服务
```bash
# 停止当前服务
pkill -f "python.*auto_trader"

# 启动新服务
nohup python3 -m quant.strategies.auto_trader > logs/auto_trader.log 2>&1 &

# 检查日志
tail -f logs/auto_trader.log
```

### 4. 验证运行
```bash
# 检查进程
ps aux | grep auto_trader

# 检查订单执行
grep "Limit order" logs/auto_trader.log | tail -20

# 检查错误
grep "ERROR" logs/error.log | tail -20
```

## 📊 性能基准

### 预期指标（基于BTCUSDT）
- **成交率**: 75-85%（BALANCED策略）
- **平均成交时间**: 5-15秒
- **手续费节省**: 40-50%
- **市价单回退率**: <15%

### 告警阈值
- 成交率 < 60%：检查市场流动性
- 成交时间 > 30秒：调整offset_bps
- 回退率 > 25%：考虑使用更激进策略

## 🔧 故障排查

### 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|-----|---------|---------|
| 订单一直不成交 | offset_bps太大 | 减小offset_bps值 |
| 频繁使用市价单 | 超时时间太短 | 增加timeout值 |
| API错误-4061 | 仓位模式不匹配 | 检查账户仓位模式设置 |
| 成交价格差异大 | 市场波动剧烈 | 启用max_slippage_bps保护 |

## 📞 紧急联系

### 出现以下情况立即处理：
1. 连续3笔订单失败
2. 单笔亏损超过设定阈值
3. API连接断开超过5分钟
4. 成交率突然下降到50%以下

### 紧急恢复命令
```bash
# 立即切换到市价单模式
python3 scripts/emergency_switch_market.py

# 停止所有交易
python3 scripts/stop_all_trading.py

# 撤销所有挂单
python3 scripts/cancel_all_orders.py
```

---

**最后更新**: 2024-01-17
**重要提醒**: 生产环境部署前必须进行小额实盘测试！
