# 限价单（Maker）模式改造方案

## 一、系统架构改造概览

### 1.1 当前系统架构分析

当前系统使用市价单（taker）模式：
- **开仓**: `auto_trader.py` → `binance_client.place_market_order_futures()` → 立即成交
- **平仓**: `simple_exit_manager.py` → 市价单平仓 → 立即成交
- **手续费**: Taker费率 0.04%（较高）

### 1.2 目标架构

改造为限价单（maker）模式：
- **开仓**: 限价单挂单 → 等待成交 → 超时处理
- **平仓**: 限价单挂单 → 等待成交 → 紧急市价单兜底
- **手续费**: Maker费率 0.00%（节省100%）

## 二、核心模块改造方案

### 2.1 新增限价单执行器

创建 `quant/strategies/limit_order_executor.py`:

```python
"""
Limit Order Executor - 限价单执行管理器

负责限价单的下单、监控、撤销和重试逻辑
"""

from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, Any, Optional, List
import asyncio
import math

from quant.binance_client import binance_client
from quant.database_manager import db
from quant.utils.logger import get_logger
from quant.config_manager import config

logger = get_logger(__name__)


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "PENDING"          # 待下单
    PLACED = "PLACED"            # 已下单
    PARTIAL_FILLED = "PARTIAL"   # 部分成交
    FILLED = "FILLED"            # 完全成交
    CANCELLED = "CANCELLED"      # 已撤销
    EXPIRED = "EXPIRED"          # 已过期
    FAILED = "FAILED"            # 失败


class PriceStrategy(Enum):
    """价格策略枚举"""
    AGGRESSIVE = "AGGRESSIVE"    # 激进：更接近市场价
    BALANCED = "BALANCED"        # 平衡：中等偏移
    PASSIVE = "PASSIVE"          # 被动：较大偏移


@dataclass
class LimitOrderConfig:
    """限价单配置"""
    # 价格偏移策略
    price_strategy: PriceStrategy = PriceStrategy.BALANCED
    
    # 价格偏移量（基点，1bp = 0.01%）
    buy_offset_bps: float = 5.0    # 买单低于市价5bp
    sell_offset_bps: float = 5.0   # 卖单高于市价5bp
    
    # 订单超时配置（秒）
    initial_timeout: int = 30       # 首次下单超时
    retry_timeout: int = 20         # 重试订单超时
    
    # 最大重试次数
    max_retries: int = 3
    
    # 是否启用部分成交
    allow_partial_fill: bool = True
    
    # 紧急模式阈值（超过此时间使用市价单）
    emergency_threshold_seconds: int = 90
    
    # 订单簿深度使用
    use_orderbook: bool = True
    orderbook_level: int = 3        # 使用订单簿前N档
    
    # 滑点保护
    max_slippage_bps: float = 20.0  # 最大允许滑点20bp


@dataclass
class OrderContext:
    """订单上下文"""
    order_id: Optional[str] = None
    client_order_id: Optional[str] = None
    symbol: str = "BTCUSDT"
    side: str = "BUY"
    position_side: Optional[str] = None
    quantity: float = 0.0
    limit_price: float = 0.0
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    created_at: datetime = None
    updated_at: datetime = None
    retry_count: int = 0
    error_message: Optional[str] = None


class LimitOrderExecutor:
    """限价单执行器"""
    
    def __init__(self):
        # 加载配置
        maker_config = config.get("MAKER_ORDER", {}) or {}
        
        self.config = LimitOrderConfig(
            price_strategy=PriceStrategy(maker_config.get("price_strategy", "BALANCED")),
            buy_offset_bps=float(maker_config.get("buy_offset_bps", 5.0)),
            sell_offset_bps=float(maker_config.get("sell_offset_bps", 5.0)),
            initial_timeout=int(maker_config.get("initial_timeout", 30)),
            retry_timeout=int(maker_config.get("retry_timeout", 20)),
            max_retries=int(maker_config.get("max_retries", 3)),
            allow_partial_fill=bool(maker_config.get("allow_partial_fill", True)),
            emergency_threshold_seconds=int(maker_config.get("emergency_threshold", 90)),
            use_orderbook=bool(maker_config.get("use_orderbook", True)),
            orderbook_level=int(maker_config.get("orderbook_level", 3)),
            max_slippage_bps=float(maker_config.get("max_slippage_bps", 20.0))
        )
        
        # 活跃订单追踪
        self._active_orders: Dict[str, OrderContext] = {}
        
        # 监控任务
        self._monitor_task: Optional[asyncio.Task] = None
        
        logger.info(f"LimitOrderExecutor initialized with config: {self.config}")
    
    async def start(self):
        """启动限价单执行器"""
        if self._monitor_task and not self._monitor_task.done():
            logger.warning("Limit order executor already running")
            return
        
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("Limit order executor started")
    
    async def stop(self):
        """停止限价单执行器"""
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            self._monitor_task = None
        
        # 撤销所有活跃订单
        await self._cancel_all_active_orders()
        logger.info("Limit order executor stopped")
    
    async def execute_limit_order(
        self,
        symbol: str,
        side: str,
        quantity: float,
        position_side: Optional[str] = None,
        urgency: str = "NORMAL"
    ) -> OrderContext:
        """
        执行限价单
        
        Args:
            symbol: 交易对
            side: BUY/SELL
            quantity: 数量
            position_side: LONG/SHORT（合约）
            urgency: NORMAL/HIGH/EMERGENCY
        
        Returns:
            OrderContext: 订单上下文
        """
        # 创建订单上下文
        context = OrderContext(
            symbol=symbol,
            side=side,
            position_side=position_side,
            quantity=quantity,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        try:
            # 紧急模式直接使用市价单
            if urgency == "EMERGENCY":
                return await self._execute_market_order(context)
            
            # 计算限价
            limit_price = await self._calculate_limit_price(context)
            context.limit_price = limit_price
            
            # 下限价单
            await self._place_limit_order(context)
            
            # 加入监控
            if context.order_id:
                self._active_orders[context.order_id] = context
            
            # 等待成交或超时
            timeout = self.config.initial_timeout if urgency == "NORMAL" else self.config.initial_timeout // 2
            await self._wait_for_fill(context, timeout)
            
            # 处理结果
            if context.status == OrderStatus.FILLED:
                logger.info(f"Limit order filled: {context.order_id}")
                return context
            elif context.status == OrderStatus.PARTIAL_FILLED and self.config.allow_partial_fill:
                logger.info(f"Limit order partial filled: {context.order_id}")
                return context
            else:
                # 超时或失败，执行重试或降级
                return await self._handle_order_failure(context, urgency)
            
        except Exception as e:
            logger.error(f"Error executing limit order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            return context
    
    async def _calculate_limit_price(self, context: OrderContext) -> float:
        """
        计算限价单价格
        
        使用多种策略：
        1. 基于当前市价的偏移
        2. 基于订单簿的最优价格
        3. 基于近期成交价的VWAP
        """
        try:
            # 获取当前市价
            current_price = await binance_client.get_current_price(context.symbol)
            
            # 策略1：基础偏移
            if context.side == "BUY":
                offset_bps = self.config.buy_offset_bps
                base_price = current_price * (1 - offset_bps / 10000)
            else:
                offset_bps = self.config.sell_offset_bps
                base_price = current_price * (1 + offset_bps / 10000)
            
            # 策略2：订单簿优化
            if self.config.use_orderbook:
                orderbook_price = await self._get_orderbook_price(context)
                if orderbook_price:
                    # 加权平均
                    base_price = base_price * 0.7 + orderbook_price * 0.3
            
            # 策略3：根据价格策略调整
            if self.config.price_strategy == PriceStrategy.AGGRESSIVE:
                # 更接近市价
                base_price = current_price * 0.3 + base_price * 0.7
            elif self.config.price_strategy == PriceStrategy.PASSIVE:
                # 更远离市价
                if context.side == "BUY":
                    base_price *= 0.995
                else:
                    base_price *= 1.005
            
            # 价格精度处理（Binance要求）
            tick_size = 0.01  # BTCUSDT的价格精度
            base_price = math.floor(base_price / tick_size) * tick_size
            
            logger.info(f"Calculated limit price: {base_price} (current: {current_price})")
            return base_price
            
        except Exception as e:
            logger.error(f"Error calculating limit price: {e}")
            raise
    
    async def _get_orderbook_price(self, context: OrderContext) -> Optional[float]:
        """基于订单簿获取最优价格"""
        try:
            orderbook = await binance_client.get_orderbook(context.symbol, limit=10)
            
            if context.side == "BUY":
                # 买单看卖盘
                asks = orderbook.get("asks", [])
                if len(asks) >= self.config.orderbook_level:
                    # 使用第N档价格
                    return float(asks[self.config.orderbook_level - 1][0])
            else:
                # 卖单看买盘
                bids = orderbook.get("bids", [])
                if len(bids) >= self.config.orderbook_level:
                    return float(bids[self.config.orderbook_level - 1][0])
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting orderbook price: {e}")
            return None
    
    async def _place_limit_order(self, context: OrderContext) -> None:
        """下限价单到交易所"""
        try:
            # 生成客户端订单ID
            context.client_order_id = f"MAKER_{datetime.utcnow().strftime('%Y%m%d%H%M%S%f')}"
            
            # 构建订单参数
            order_params = {
                "symbol": context.symbol,
                "side": context.side,
                "type": "LIMIT",
                "timeInForce": "GTC",  # Good Till Cancel
                "quantity": context.quantity,
                "price": context.limit_price,
                "newClientOrderId": context.client_order_id
            }
            
            if context.position_side:
                order_params["positionSide"] = context.position_side
            
            # 下单
            response = await binance_client.place_limit_order_futures(**order_params)
            
            if response and not response.get("error"):
                context.order_id = str(response.get("orderId"))
                context.status = OrderStatus.PLACED
                logger.info(f"Limit order placed: {context.order_id} at {context.limit_price}")
            else:
                raise Exception(f"Order placement failed: {response}")
            
        except Exception as e:
            logger.error(f"Error placing limit order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            raise
    
    async def _wait_for_fill(self, context: OrderContext, timeout_seconds: int) -> None:
        """等待订单成交"""
        start_time = datetime.utcnow()
        check_interval = 1  # 每秒检查一次
        
        while (datetime.utcnow() - start_time).total_seconds() < timeout_seconds:
            try:
                # 查询订单状态
                order_info = await binance_client.get_order(
                    symbol=context.symbol,
                    orderId=context.order_id
                )
                
                if order_info:
                    status = order_info.get("status")
                    
                    if status == "FILLED":
                        context.status = OrderStatus.FILLED
                        context.filled_quantity = float(order_info.get("executedQty", 0))
                        context.avg_fill_price = float(order_info.get("avgPrice", 0))
                        return
                    
                    elif status == "PARTIALLY_FILLED":
                        context.status = OrderStatus.PARTIAL_FILLED
                        context.filled_quantity = float(order_info.get("executedQty", 0))
                        
                        if self.config.allow_partial_fill:
                            # 部分成交也可以接受
                            if context.filled_quantity > 0:
                                context.avg_fill_price = float(order_info.get("avgPrice", 0))
                                return
                    
                    elif status in ["CANCELED", "EXPIRED", "REJECTED"]:
                        context.status = OrderStatus.CANCELLED
                        return
                
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"Error checking order status: {e}")
                await asyncio.sleep(check_interval)
        
        # 超时
        context.status = OrderStatus.EXPIRED
    
    async def _handle_order_failure(self, context: OrderContext, urgency: str) -> OrderContext:
        """处理订单失败（超时/拒绝）"""
        try:
            # 先尝试撤销原订单
            if context.order_id and context.status in [OrderStatus.PLACED, OrderStatus.PARTIAL_FILLED]:
                await self._cancel_order(context)
            
            # 检查是否达到重试上限
            if context.retry_count >= self.config.max_retries:
                logger.warning(f"Max retries reached, falling back to market order")
                return await self._execute_market_order(context)
            
            # 检查是否超过紧急阈值
            elapsed = (datetime.utcnow() - context.created_at).total_seconds()
            if elapsed > self.config.emergency_threshold_seconds:
                logger.warning(f"Emergency threshold reached, using market order")
                return await self._execute_market_order(context)
            
            # 重试逻辑
            context.retry_count += 1
            logger.info(f"Retrying limit order (attempt {context.retry_count})")
            
            # 调整价格策略（更激进）
            if context.side == "BUY":
                context.limit_price *= 1.002  # 提高买价
            else:
                context.limit_price *= 0.998  # 降低卖价
            
            # 重新下单
            await self._place_limit_order(context)
            
            if context.order_id:
                self._active_orders[context.order_id] = context
            
            # 等待成交（更短的超时）
            await self._wait_for_fill(context, self.config.retry_timeout)
            
            if context.status == OrderStatus.FILLED:
                return context
            else:
                # 递归重试
                return await self._handle_order_failure(context, urgency)
            
        except Exception as e:
            logger.error(f"Error handling order failure: {e}")
            # 最终兜底：市价单
            return await self._execute_market_order(context)
    
    async def _execute_market_order(self, context: OrderContext) -> OrderContext:
        """执行市价单（兜底方案）"""
        try:
            logger.warning(f"Falling back to market order for {context.symbol}")
            
            # 计算USDT名义价值
            current_price = await binance_client.get_current_price(context.symbol)
            notional_usdt = context.quantity * current_price
            
            # 执行市价单
            response = await binance_client.place_market_order_futures(
                symbol=context.symbol,
                side=context.side,
                notional_usdt=notional_usdt,
                position_side=context.position_side
            )
            
            if response and not response.get("error"):
                context.status = OrderStatus.FILLED
                context.filled_quantity = context.quantity
                context.avg_fill_price = current_price
                logger.info(f"Market order executed as fallback")
            else:
                context.status = OrderStatus.FAILED
                context.error_message = f"Market order failed: {response}"
            
            return context
            
        except Exception as e:
            logger.error(f"Error executing market order: {e}")
            context.status = OrderStatus.FAILED
            context.error_message = str(e)
            return context
    
    async def _cancel_order(self, context: OrderContext) -> bool:
        """撤销订单"""
        try:
            if not context.order_id:
                return False
            
            response = await binance_client.cancel_order(
                symbol=context.symbol,
                orderId=context.order_id
            )
            
            if response and response.get("status") == "CANCELED":
                context.status = OrderStatus.CANCELLED
                logger.info(f"Order cancelled: {context.order_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
    
    async def _cancel_all_active_orders(self) -> None:
        """撤销所有活跃订单"""
        for order_id, context in list(self._active_orders.items()):
            try:
                await self._cancel_order(context)
            except Exception as e:
                logger.error(f"Error cancelling order {order_id}: {e}")
        
        self._active_orders.clear()
    
    async def _monitor_loop(self) -> None:
        """监控循环"""
        logger.info("Starting limit order monitor loop")
        
        while True:
            try:
                await self._check_active_orders()
                await asyncio.sleep(5)  # 每5秒检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitor loop: {e}")
                await asyncio.sleep(5)
    
    async def _check_active_orders(self) -> None:
        """检查活跃订单状态"""
        for order_id, context in list(self._active_orders.items()):
            try:
                # 查询订单状态
                order_info = await binance_client.get_order(
                    symbol=context.symbol,
                    orderId=order_id
                )
                
                if order_info:
                    status = order_info.get("status")
                    
                    if status == "FILLED":
                        context.status = OrderStatus.FILLED
                        context.filled_quantity = float(order_info.get("executedQty", 0))
                        context.avg_fill_price = float(order_info.get("avgPrice", 0))
                        del self._active_orders[order_id]
                        logger.info(f"Order {order_id} filled")
                    
                    elif status in ["CANCELED", "EXPIRED", "REJECTED"]:
                        context.status = OrderStatus.CANCELLED
                        del self._active_orders[order_id]
                        logger.info(f"Order {order_id} cancelled/expired")
                    
                    context.updated_at = datetime.utcnow()
                
            except Exception as e:
                logger.error(f"Error checking order {order_id}: {e}")
    
    def get_active_orders(self) -> List[OrderContext]:
        """获取所有活跃订单"""
        return list(self._active_orders.values())
    
    def get_order_status(self, order_id: str) -> Optional[OrderContext]:
        """获取订单状态"""
        return self._active_orders.get(order_id)


# 全局实例
limit_order_executor = LimitOrderExecutor()
```

### 2.2 改造 AutoTrader 集成限价单

修改 `quant/strategies/auto_trader.py`:

```python
# 在文件顶部添加导入
from quant.strategies.limit_order_executor import limit_order_executor, OrderStatus

class AutoTrader:
    """Automatic order executor for generated signals."""
    
    def __init__(self):
        at_cfg = config.get("AUTO_TRADER", {}) or {}
        # ... 现有配置 ...
        
        # 新增：订单执行模式配置
        self.order_mode = at_cfg.get("order_mode", "LIMIT")  # LIMIT/MARKET/SMART
        self.use_limit_for_open = at_cfg.get("use_limit_for_open", True)
        self.use_limit_for_close = at_cfg.get("use_limit_for_close", True)
        self.urgency_threshold_confidence = at_cfg.get("urgency_threshold", 0.85)
        
        logger.info(f"AutoTrader initialized with order mode: {self.order_mode}")
    
    async def handle_new_signal(self, signal: dict[str, Any]) -> ExecutionResult:
        """Handle a freshly generated tradable signal and place an order."""
        try:
            # ... 现有验证逻辑 ...
            
            # 决定执行模式
            execution_mode = self._determine_execution_mode(signal)
            
            # 计算数量
            exec_price = float(signal.get("entry_price"))
            quantity = size_usdt / exec_price
            quantity = self._quantize_quantity(quantity)
            
            # 执行订单
            if execution_mode == "LIMIT" and self.use_limit_for_open:
                # 使用限价单
                order_result = await self._execute_limit_order(
                    symbol=symbol,
                    side="BUY" if signal.get("direction") == "LONG" else "SELL",
                    quantity=quantity,
                    position_side="LONG" if signal.get("direction") == "LONG" else "SHORT",
                    signal=signal
                )
            else:
                # 使用市价单（原逻辑）
                order_result = await self._execute_market_order(
                    symbol=symbol,
                    side="BUY" if signal.get("direction") == "LONG" else "SELL",
                    notional_usdt=size_usdt,
                    position_side="LONG" if signal.get("direction") == "LONG" else "SHORT"
                )
            
            # 处理执行结果
            if order_result.get("success"):
                # 更新数据库
                execution_info = {
                    "action": "OPEN",
                    "symbol": symbol,
                    "direction": signal.get("direction"),
                    "size_usdt": round(size_usdt, 2),
                    "price": order_result.get("avg_price", exec_price),
                    "order_type": execution_mode,
                    "order_id": order_result.get("order_id"),
                    "fills": order_result.get("fills", []),
                    "timestamp": signal.get("signal_timestamp"),
                    "reason": "auto_trade_on_signal",
                    "exchange_response": order_result,
                }
                
                db.update_trade_decision_details(
                    trade_id=trade["id"],
                    updates={"order_execution": execution_info}
                )
                
                # 发送通知
                notification_manager.send_trade_execution({
                    **execution_info,
                    "trade_id": trade["id"],
                    "extra": {
                        "confidence": signal.get("confidence_score"),
                        "suggested_bet": signal.get("suggested_bet"),
                        "trigger": signal.get("trigger_pattern"),
                        "order_mode": execution_mode,
                        "saved_fees": self._calculate_saved_fees(execution_mode, size_usdt)
                    }
                })
                
                return ExecutionResult(
                    True, 
                    trade["id"], 
                    f"Order executed via {execution_mode}", 
                    execution_info
                )
            else:
                error_msg = f"Order execution failed: {order_result.get('error')}"
                logger.error(error_msg)
                return ExecutionResult(False, None, error_msg, {})
            
        except Exception as e:
            logger.error(f"Error handling new signal: {e}")
            return ExecutionResult(False, None, str(e), {})
    
    def _determine_execution_mode(self, signal: dict[str, Any]) -> str:
        """
        决定订单执行模式
        
        考虑因素：
        1. 配置的默认模式
        2. 信号置信度
        3. 市场波动性
        4. 时间紧迫性
        """
        # 基础模式
        mode = self.order_mode
        
        # SMART模式：智能选择
        if mode == "SMART":
            confidence = float(signal.get("confidence_score", 0))
            
            # 高置信度使用限价单
            if confidence >= self.urgency_threshold_confidence:
                mode = "LIMIT"
            # 低置信度或紧急情况使用市价单
            elif confidence < 0.7 or signal.get("urgent", False):
                mode = "MARKET"
            else:
                mode = "LIMIT"
            
            # 检查市场状态
            market_state = signal.get("market_state", "")
            if market_state == "volatile":
                # 高波动市场使用市价单
                mode = "MARKET"
        
        logger.info(f"Execution mode determined: {mode} (confidence: {signal.get('confidence_score')})")
        return mode
    
    async def _execute_limit_order(
        self, 
        symbol: str, 
        side: str, 
        quantity: float,
        position_side: Optional[str],
        signal: dict[str, Any]
    ) -> dict[str, Any]:
        """执行限价单"""
        try:
            # 决定紧急程度
            confidence = float(signal.get("confidence_score", 0))
            if confidence >= 0.9:
                urgency = "NORMAL"
            elif confidence >= 0.75:
                urgency = "HIGH"
            else:
                urgency = "NORMAL"  # 低置信度也用普通模式，给更多时间成交
            
            # 执行限价单
            order_context = await limit_order_executor.execute_limit_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                position_side=position_side,
                urgency=urgency
            )
            
            # 构建返回结果
            if order_context.status == OrderStatus.FILLED:
                return {
                    "success": True,
                    "order_id": order_context.order_id,
                    "avg_price": order_context.avg_fill_price,
                    "filled_quantity": order_context.filled_quantity,
                    "order_type": "LIMIT",
                    "fills": [{
                        "qty": str(order_context.filled_quantity),
                        "price": str(order_context.avg_fill_price)
                    }]
                }
            elif order_context.status == OrderStatus.PARTIAL_FILLED:
                # 部分成交也算成功
                return {
                    "success": True,
                    "order_id": order_context.order_id,
                    "avg_price": order_context.avg_fill_price,
                    "filled_quantity": order_context.filled_quantity,
                    "order_type": "LIMIT",
                    "partial": True,
                    "fills": [{
                        "qty": str(order_context.filled_quantity),
                        "price": str(order_context.avg_fill_price)
                    }]
                }
            else:
                return {
                    "success": False,
                    "error": order_context.error_message or "Limit order failed",
                    "order_type": "LIMIT"
                }
            
        except Exception as e:
            logger.error(f"Error executing limit order: {e}")
            return {
                "success": False,
                "error": str(e),
                "order_type": "LIMIT"
            }
    
    async def _execute_market_order(
        self,
        symbol: str,
        side: str,
        notional_usdt: float,
        position_side: Optional[str]
    ) -> dict[str, Any]:
        """执行市价单（保持原有逻辑）"""
        try:
            order_resp = await binance_client.place_market_order_futures(
                symbol=symbol,
                side=side,
                notional_usdt=notional_usdt,
                position_side=position_side,
                reduce_only=False
            )
            
            if not order_resp.get("error"):
                # 计算平均成交价
                if order_resp.get("fills"):
                    total_qty = sum(float(fill["qty"]) for fill in order_resp["fills"])
                    total_value = sum(float(fill["price"]) * float(fill["qty"]) for fill in order_resp["fills"])
                    avg_price = total_value / total_qty if total_qty > 0 else 0
                else:
                    avg_price = float(order_resp.get("price", 0))
                
                return {
                    "success": True,
                    "order_id": order_resp.get("orderId"),
                    "avg_price": avg_price,
                    "filled_quantity": float(order_resp.get("executedQty", 0)),
                    "order_type": "MARKET",
                    "fills": order_resp.get("fills", [])
                }
            else:
                return {
                    "success": False,
                    "error": order_resp.get("error"),
                    "order_type": "MARKET"
                }
            
        except Exception as e:
            logger.error(f"Error executing market order: {e}")
            return {
                "success": False,
                "error": str(e),
                "order_type": "MARKET"
            }
    
    def _quantize_quantity(self, quantity: float) -> float:
        """量化数量到交易所要求的精度"""
        step_size = 0.001  # BTCUSDT的数量精度
        min_qty = 0.001
        
        qty_steps = math.floor(quantity / step_size)
        return max(min_qty, round(qty_steps * step_size, 3))
    
    def _calculate_saved_fees(self, order_mode: str, notional_usdt: float) -> float:
        """计算节省的手续费"""
        if order_mode == "LIMIT":
            # Maker费率 0.02%
            maker_fee = notional_usdt * 0.0002
            # Taker费率 0.04%
            taker_fee = notional_usdt * 0.0004
            # 节省的费用
            return round(taker_fee - maker_fee, 4)
        return 0.0
```

### 2.3 改造 SimpleExitManager 支持限价单平仓

修改 `quant/strategies/simple_exit_manager.py`:

```python
# 在文件顶部添加导入
from quant.strategies.limit_order_executor import limit_order_executor, OrderStatus

class SimpleExitManager:
    """简化的平仓管理器 - 支持限价单平仓"""
    
    def __init__(self):
        # ... 现有配置 ...
        
        # 新增：平仓模式配置
        self.exit_order_mode = exit_cfg.get("exit_order_mode", "LIMIT")  # LIMIT/MARKET/SMART
        self.exit_limit_timeout = int(exit_cfg.get("exit_limit_timeout", 20))  # 限价单超时
        self.emergency_exit_seconds = int(exit_cfg.get("emergency_exit_seconds", 30))  # 紧急平仓时间
        
        logger.info(f"SimpleExitManager initialized with exit mode: {self.exit_order_mode}")
    
    async def _execute_exit(self, trade_id: int, position_info: Dict[str, Any]):
        """执行平仓 - 支持限价单"""
        try:
            # ... 现有验证逻辑 ...
            
            # 决定平仓紧急程度
            time_to_kline_end = (position_info["planned_exit_time"] - datetime.utcnow()).total_seconds()
            
            if time_to_kline_end < self.emergency_exit_seconds:
                # 紧急平仓，使用市价单
                urgency = "EMERGENCY"
                use_limit = False
            elif time_to_kline_end < 60:
                # 高优先级
                urgency = "HIGH"
                use_limit = self.exit_order_mode != "MARKET"
            else:
                # 普通优先级
                urgency = "NORMAL"
                use_limit = self.exit_order_mode == "LIMIT"
            
            # 执行平仓
            if use_limit:
                exit_result = await self._execute_limit_exit(
                    trade_id=trade_id,
                    position_info=position_info,
                    urgency=urgency
                )
            else:
                exit_result = await self._execute_market_exit(
                    trade_id=trade_id,
                    position_info=position_info
                )
            
            # 处理结果
            if exit_result.get("success"):
                # 更新数据库
                exit_price = exit_result.get("avg_price")
                pnl = self._calculate_pnl(position_info, exit_price)
                
                db.update_trade_exit(
                    trade_id=trade_id,
                    exit_price=exit_price,
                    exit_timestamp=datetime.utcnow().isoformat(),
                    pnl=pnl,
                    exit_details={
                        "order_type": exit_result.get("order_type"),
                        "order_id": exit_result.get("order_id"),
                        "fills": exit_result.get("fills", []),
                        "saved_fees": exit_result.get("saved_fees", 0)
                    }
                )
                
                # 发送通知
                notification_manager.send_position_closed({
                    "trade_id": trade_id,
                    "exit_price": exit_price,
                    "pnl": pnl,
                    "order_type": exit_result.get("order_type"),
                    "saved_fees": exit_result.get("saved_fees", 0)
                })
                
                logger.info(f"Position {trade_id} closed via {exit_result.get('order_type')}, PnL: {pnl}")
                
                # 从监控列表移除
                del self._pending_exits[trade_id]
                
            else:
                # 平仓失败，记录并重试
                logger.error(f"Failed to close position {trade_id}: {exit_result.get('error')}")
                position_info["exit_retry_count"] = position_info.get("exit_retry_count", 0) + 1
                
                if position_info["exit_retry_count"] >= self.exit_max_retries:
                    # 达到最大重试，强制市价单
                    logger.warning(f"Max retries reached for {trade_id}, forcing market exit")
                    await self._force_market_exit(trade_id, position_info)
            
        except Exception as e:
            logger.error(f"Error executing exit for {trade_id}: {e}")
            # 出错时尝试市价单兜底
            await self._force_market_exit(trade_id, position_info)
    
    async def _execute_limit_exit(
        self,
        trade_id: int,
        position_info: Dict[str, Any],
        urgency: str
    ) -> dict[str, Any]:
        """执行限价单平仓"""
        try:
            trade_data = position_info["trade_data"]
            
            # 计算平仓方向和数量
            if trade_data["direction"] == "LONG":
                side = "SELL"
                position_side = "LONG"
            else:
                side = "BUY"
                position_side = "SHORT"
            
            # 获取当前价格估算数量
            current_price = await binance_client.get_current_price(trade_data["symbol"])
            quantity = float(trade_data["suggested_bet"]) / current_price
            quantity = self._quantize_quantity(quantity)
            
            # 执行限价单
            order_context = await limit_order_executor.execute_limit_order(
                symbol=trade_data["symbol"],
                side=side,
                quantity=quantity,
                position_side=position_side,
                urgency=urgency
            )
            
            # 构建返回结果
            if order_context.status in [OrderStatus.FILLED, OrderStatus.PARTIAL_FILLED]:
                return {
                    "success": True,
                    "order_id": order_context.order_id,
                    "avg_price": order_context.avg_fill_price,
                    "order_type": "LIMIT",
                    "fills": [{
                        "qty": str(order_context.filled_quantity),
                        "price": str(order_context.avg_fill_price)
                    }],
                    "saved_fees": self._calculate_saved_fees("LIMIT", trade_data["suggested_bet"])
                }
            else:
                return {
                    "success": False,
                    "error": order_context.error_message or "Limit exit failed",
                    "order_type": "LIMIT"
                }
            
        except Exception as e:
            logger.error(f"Error executing limit exit: {e}")
            return {
                "success": False,
                "error": str(e),
                "order_type": "LIMIT"
            }
    
    async def _execute_market_exit(
        self,
        trade_id: int,
        position_info: Dict[str, Any]
    ) -> dict[str, Any]:
        """执行市价单平仓（保持原有逻辑）"""
        # ... 原有市价单平仓逻辑 ...
    
    async def _force_market_exit(
        self,
        trade_id: int,
        position_info: Dict[str, Any]
    ) -> None:
        """强制市价单平仓（紧急兜底）"""
        try:
            logger.warning(f"Forcing market exit for position {trade_id}")
            
            # 直接使用市价单
            exit_result = await self._execute_market_exit(trade_id, position_info)
            
            if exit_result.get("success"):
                logger.info(f"Emergency market exit successful for {trade_id}")
            else:
                # 记录严重错误
                logger.critical(f"FAILED TO EXIT POSITION {trade_id}: {exit_result.get('error')}")
                
                # 发送紧急通知
                notification_manager.send_emergency_alert({
                    "type": "EXIT_FAILURE",
                    "trade_id": trade_id,
                    "error": exit_result.get("error"),
                    "position_info": position_info
                })
            
        except Exception as e:
            logger.critical(f"Critical error in force market exit: {e}")
```

## 三、配置参数设计

### 3.1 更新 config.json

```json
{
  "MAKER_ORDER": {
    "enabled": true,
    "price_strategy": "BALANCED",
    "buy_offset_bps": 5.0,
    "sell_offset_bps": 5.0,
    "initial_timeout": 30,
    "retry_timeout": 20,
    "max_retries": 3,
    "allow_partial_fill": true,
    "emergency_threshold": 90,
    "use_orderbook": true,
    "orderbook_level": 3,
    "max_slippage_bps": 20.0
  },
  
  "AUTO_TRADER": {
    "enabled": true,
    "order_mode": "SMART",
    "use_limit_for_open": true,
    "use_limit_for_close": true,
    "urgency_threshold": 0.85,
    "emergency_stop": false,
    "max_position_minutes": 30,
    "min_order_usdt": 5.0,
    "max_order_usdt": 100.0
  },
  
  "SIMPLE_EXIT": {
    "exit_before_kline_end_minutes": 2,
    "min_hold_minutes": 1.0,
    "exit_order_mode": "LIMIT",
    "exit_limit_timeout": 20,
    "emergency_exit_seconds": 30,
    "max_exit_retries": 3,
    "retry_backoff_seconds": 2.0
  }
}
```

### 3.2 参数说明

- **price_strategy**: 价格策略
  - `AGGRESSIVE`: 激进，接近市价（成交快，费用略高）
  - `BALANCED`: 平衡，适中偏移（推荐）
  - `PASSIVE`: 被动，远离市价（费用最低，成交慢）

- **offset_bps**: 价格偏移（基点）
  - 5 bps = 0.05%
  - 买单低于市价，卖单高于市价

- **order_mode**: 订单模式
  - `LIMIT`: 始终使用限价单
  - `MARKET`: 始终使用市价单
  - `SMART`: 智能选择（根据置信度和市场状态）

## 四、风险控制调整

### 4.1 限价单风险控制

```python
class LimitOrderRiskManager:
    """限价单专用风险管理"""
    
    def __init__(self):
        self.max_pending_orders = 5  # 最大待成交订单数
        self.max_pending_value = 500  # 最大待成交价值（USDT）
        self.slippage_monitor = {}   # 滑点监控
    
    def check_order_risk(self, order_context: OrderContext) -> bool:
        """检查订单风险"""
        # 1. 检查待成交订单数
        active_orders = limit_order_executor.get_active_orders()
        if len(active_orders) >= self.max_pending_orders:
            logger.warning("Too many pending orders")
            return False
        
        # 2. 检查待成交价值
        total_pending = sum(o.quantity * o.limit_price for o in active_orders)
        if total_pending >= self.max_pending_value:
            logger.warning("Pending value limit exceeded")
            return False
        
        # 3. 检查滑点
        if order_context.symbol in self.slippage_monitor:
            recent_slippage = self.slippage_monitor[order_context.symbol]
            if recent_slippage > 0.005:  # 0.5%滑点警告
                logger.warning(f"High slippage detected: {recent_slippage:.2%}")
                return False
        
        return True
    
    def update_slippage(self, symbol: str, expected_price: float, actual_price: float):
        """更新滑点统计"""
        slippage = abs(actual_price - expected_price) / expected_price
        self.slippage_monitor[symbol] = slippage
```

### 4.2 紧急止损机制

```python
class EmergencyStopLoss:
    """紧急止损 - 限价单失败时的保护"""
    
    async def check_emergency_exit(self, trade_id: int, position_info: dict):
        """检查是否需要紧急止损"""
        # 计算浮动亏损
        entry_price = position_info["entry_price"]
        current_price = await binance_client.get_current_price()
        
        if position_info["direction"] == "LONG":
            pnl_pct = (current_price - entry_price) / entry_price
        else:
            pnl_pct = (entry_price - current_price) / entry_price
        
        # 止损阈值
        stop_loss_threshold = -0.02  # -2%
        
        if pnl_pct <= stop_loss_threshold:
            logger.warning(f"Emergency stop loss triggered for {trade_id}: {pnl_pct:.2%}")
            # 强制市价单止损
            await self._force_market_stop_loss(trade_id, position_info)
```

## 五、性能优化建议

### 5.1 订单簿数据缓存

```python
class OrderbookCache:
    """订单簿缓存，减少API调用"""
    
    def __init__(self, ttl_seconds: int = 1):
        self._cache = {}
        self._ttl = ttl_seconds
    
    async def get_orderbook(self, symbol: str) -> Optional[dict]:
        """获取缓存的订单簿"""
        if symbol in self._cache:
            cached_data, timestamp = self._cache[symbol]
            if (datetime.utcnow() - timestamp).total_seconds() < self._ttl:
                return cached_data
        
        # 获取新数据
        orderbook = await binance_client.get_orderbook(symbol)
        self._cache[symbol] = (orderbook, datetime.utcnow())
        return orderbook
```

### 5.2 批量订单处理

```python
async def batch_place_orders(orders: List[dict]) -> List[OrderContext]:
    """批量下单，提高效率"""
    tasks = []
    for order in orders:
        task = limit_order_executor.execute_limit_order(**order)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

## 六、迁移计划

### 6.1 分阶段实施

1. **第一阶段**：测试环境验证
   - 部署限价单执行器
   - 小额测试订单
   - 监控成交率和延迟

2. **第二阶段**：混合模式
   - 开仓使用限价单
   - 平仓保持市价单
   - 收集性能数据

3. **第三阶段**：全面切换
   - 开仓和平仓都使用限价单
   - 保留紧急市价单兜底
   - 优化参数配置

### 6.2 回滚方案

```json
{
  "AUTO_TRADER": {
    "order_mode": "MARKET"  // 快速回滚到市价单模式
  }
}
```

## 七、监控指标

### 7.1 关键指标

- **成交率**: 限价单成交比例
- **成交延迟**: 从下单到成交的时间
- **滑点统计**: 实际成交价vs预期价格
- **手续费节省**: Maker vs Taker费用对比
- **失败率**: 超时/撤销订单比例

### 7.2 监控仪表板

```python
class MakerOrderMetrics:
    """限价单指标监控"""
    
    def get_metrics(self) -> dict:
        return {
            "total_limit_orders": self.total_limit_orders,
            "filled_orders": self.filled_orders,
            "fill_rate": self.filled_orders / max(self.total_limit_orders, 1),
            "avg_fill_time": self.total_fill_time / max(self.filled_orders, 1),
            "fees_saved": self.total_fees_saved,
            "fallback_to_market": self.market_fallback_count
        }
```

## 八、总结

### 优势
- **手续费节省**: 50%手续费减少（0.04% → 0.02%）
- **价格改善**: 限价单可能获得更好的成交价
- **风险可控**: 多重兜底机制确保执行

### 挑战
- **成交不确定性**: 需要等待和重试机制
- **复杂度增加**: 订单管理逻辑更复杂
- **延迟增加**: 可能错过最佳交易时机

### 建议
1. 先在测试环境充分验证
2. 使用SMART模式自动选择
3. 保留市价单兜底机制
4. 密切监控成交率和延迟指标
5. 根据实际数据调整参数
