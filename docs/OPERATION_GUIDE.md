# 重构交易系统操作指南

## 📖 概述

本指南提供重构交易系统的完整操作方法，包括部署、启动、停止、修改配置、监控、故障排除等所有日常操作。

## 🚀 快速开始

### 1. 首次部署

```bash
# 1. 进入项目目录
cd /Users/<USER>/PycharmProjects/Mitchquant自动交易策略版本20250810

# 2. 运行部署脚本
chmod +x scripts/deploy_refactored.sh
./scripts/deploy_refactored.sh development

# 3. 等待部署完成（约2-3分钟）
# 部署成功后会显示启动命令
```

### 2. 快速启动系统

```bash
# 激活虚拟环境
source trading_system_venv/bin/activate

# 启动重构系统
python main_refactored.py config.development.json
```

## 🔧 详细操作指南

### 系统启动操作

#### 方法1: 直接启动（推荐用于开发）
```bash
# 1. 激活虚拟环境
cd /Users/<USER>/PycharmProjects/Mitchquant自动交易策略版本20250810
source trading_system_venv/bin/activate

# 2. 启动系统 - 开发环境
python main_refactored.py config.development.json

# 或者启动系统 - 生产环境
python main_refactored.py config.production.json

# 或者自动环境检测
ENVIRONMENT=production python main_refactored.py
```

#### 方法2: 后台服务启动 (macOS)
```bash
# 1. 启动后台服务
launchctl start com.trading.system.refactored

# 2. 检查服务状态
launchctl list | grep com.trading.system.refactored

# 3. 查看服务日志
tail -f logs/stdout.log
tail -f logs/stderr.log
```

#### 方法3: 后台服务启动 (Linux)
```bash
# 1. 启动系统服务
sudo systemctl start trading-system-refactored

# 2. 检查服务状态
sudo systemctl status trading-system-refactored

# 3. 查看服务日志
sudo journalctl -u trading-system-refactored -f
```

### 系统停止操作

#### 方法1: 直接停止
```bash
# 如果是前台运行，直接按 Ctrl+C
# 系统会优雅关闭所有服务
```

#### 方法2: 后台服务停止 (macOS)
```bash
# 停止后台服务
launchctl stop com.trading.system.refactored

# 卸载服务（如果需要）
launchctl unload ~/Library/LaunchAgents/com.trading.system.refactored.plist
```

#### 方法3: 后台服务停止 (Linux)
```bash
# 停止系统服务
sudo systemctl stop trading-system-refactored

# 禁用自动启动（如果需要）
sudo systemctl disable trading-system-refactored
```

### 系统重启操作

#### 快速重启
```bash
# 1. 停止系统
# (使用上述停止方法之一)

# 2. 等待3秒确保完全停止
sleep 3

# 3. 重新启动
# (使用上述启动方法之一)
```

#### 服务重启 (macOS)
```bash
# 一键重启服务
launchctl stop com.trading.system.refactored
sleep 3
launchctl start com.trading.system.refactored
```

#### 服务重启 (Linux)
```bash
# 一键重启服务
sudo systemctl restart trading-system-refactored
```

### 配置修改操作

#### 1. 修改基本配置
```bash
# 1. 停止系统
# (使用上述停止方法)

# 2. 编辑配置文件
nano config.json                    # 通用配置
nano config.development.json        # 开发环境配置
nano config.production.json         # 生产环境配置

# 3. 验证配置文件语法
python -m json.tool config.json

# 4. 重启系统
# (使用上述启动方法)
```

#### 2. 修改服务配置
```bash
# 编辑配置文件中的SERVICES节
nano config.json

# 示例配置修改：
{
  "SERVICES": {
    "market_analysis": {
      "enabled": true
    },
    "settlement": {
      "enabled": true,
      "max_concurrent": 50    # 增加并发数
    },
    "risk_management": {
      "enabled": true
    },
    "health_monitoring": {
      "enabled": false        # 暂时禁用监控
    },
    "system_metrics": {
      "enabled": true
    }
  }
}
```

#### 3. 修改环境变量
```bash
# 编辑环境变量文件
nano .env

# 示例修改：
BINANCE_ACCESS_KEY=your_new_key
BINANCE_SECRET_KEY=your_new_secret
ENVIRONMENT=production
DEBUG=false
```

### 系统监控操作

#### 1. 检查系统状态
```bash
# 激活虚拟环境
source trading_system_venv/bin/activate

# 检查服务状态
python -c "
from quant.config_manager import ConfigManager
from quant.trading_system_orchestrator import TradingSystemOrchestrator
config = ConfigManager('config.json')
orchestrator = TradingSystemOrchestrator(config)
print('=== 服务容器状态 ===')
status = orchestrator.service_container.get_container_status()
print(f'启用服务数: {status[\"enabled_services\"]}')
print(f'初始化顺序: {status[\"initialization_order\"]}')
"
```

#### 2. 检查系统健康
```bash
# 运行健康检查
python -c "
import asyncio
from quant.config_manager import ConfigManager
from quant.trading_system_orchestrator import TradingSystemOrchestrator

async def check_health():
    config = ConfigManager('config.json')
    orchestrator = TradingSystemOrchestrator(config)
    await orchestrator.service_container.start_all_services()
    health = orchestrator.service_container.get_service_health_status()
    print('=== 服务健康状态 ===')
    for service, status in health.items():
        print(f'{service}: {\"✅运行中\" if status.get(\"running\") else \"❌停止\"}')
    await orchestrator.service_container.stop_all_services()

asyncio.run(check_health())
"
```

#### 3. 查看日志
```bash
# 查看实时日志
tail -f logs/trading_system.log

# 查看错误日志
tail -f logs/error.log

# 查看特定服务日志
grep "market_analysis" logs/trading_system.log | tail -20

# 查看最近的交易信号
grep "signal" logs/trading_system.log | tail -10
```

#### 4. 性能监控
```bash
# 运行性能基准测试
source trading_system_venv/bin/activate
python tests/integration/performance_comparison.py

# 查看结果
cat performance_benchmark_results.json | python -m json.tool
```

### 故障排除操作

#### 1. 常见启动问题

**问题: 模块导入错误**
```bash
# 解决方案: 检查依赖是否完整安装
source trading_system_venv/bin/activate
pip install -r requirements.txt

# 检查特定模块
python -c "import pandas, numpy, scipy, sklearn; print('所有依赖正常')"
```

**问题: 配置文件错误**
```bash
# 解决方案: 验证配置文件
python -m json.tool config.json

# 如果配置文件损坏，恢复备份
cp config.json.backup config.json
```

**问题: 数据库连接失败**
```bash
# 解决方案: 检查数据库文件权限
ls -la trading_system.db
ls -la data/timeseries.db

# 重新初始化数据库（如果必要）
source trading_system_venv/bin/activate
python -c "
from quant.database_manager import DatabaseManager
db = DatabaseManager('trading_system.db')
print('数据库连接正常')
"
```

#### 2. 性能问题

**问题: 系统响应慢**
```bash
# 1. 检查系统资源使用
python -c "
import psutil
print(f'CPU使用率: {psutil.cpu_percent()}%')
print(f'内存使用率: {psutil.virtual_memory().percent}%')
"

# 2. 调整并发设置
nano config.json
# 减少 settlement.max_concurrent 值

# 3. 检查日志中的性能警告
grep "WARNING\|ERROR" logs/trading_system.log | tail -10
```

#### 3. 网络连接问题

**问题: Binance API连接失败**
```bash
# 1. 检查网络连接
curl -I https://api.binance.com

# 2. 验证API密钥
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
key = os.getenv('BINANCE_ACCESS_KEY')
print(f'API Key配置: {\"✅已配置\" if key else \"❌未配置\"}')
"

# 3. 测试API连接
python -c "
from quant.binance_client import binance_client
try:
    info = binance_client.get_server_time()
    print('✅ Binance API连接正常')
except Exception as e:
    print(f'❌ API连接失败: {e}')
"
```

### 维护操作

#### 1. 定期维护
```bash
# 每日维护检查清单
echo "=== 每日系统检查 ==="

# 1. 检查系统状态
launchctl list | grep com.trading.system.refactored || echo "服务未运行"

# 2. 检查日志大小
du -h logs/

# 3. 检查数据库大小
du -h *.db data/*.db

# 4. 清理旧日志（保留7天）
find logs/ -name "*.log" -mtime +7 -delete

echo "维护检查完成"
```

#### 2. 版本升级
```bash
# 1. 备份当前系统
cp -r /current/system /backup/system-$(date +%Y%m%d)

# 2. 停止系统
launchctl stop com.trading.system.refactored

# 3. 更新代码
git pull origin main

# 4. 更新依赖
source trading_system_venv/bin/activate
pip install -r requirements.txt --upgrade

# 5. 运行测试
python tests/integration/test_behavior_verification.py

# 6. 重启系统
launchctl start com.trading.system.refactored
```

#### 3. 数据备份
```bash
# 创建每日数据备份
#!/bin/bash
BACKUP_DIR="backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份数据库
cp trading_system.db $BACKUP_DIR/
cp data/timeseries.db $BACKUP_DIR/

# 备份配置
cp config.json $BACKUP_DIR/
cp .env $BACKUP_DIR/

# 备份日志（最近7天）
find logs/ -name "*.log" -mtime -7 -exec cp {} $BACKUP_DIR/ \\;

echo "备份完成: $BACKUP_DIR"
```

## 🔍 系统监控命令速查

### 快速状态检查
```bash
# 一键健康检查
./scripts/health_check.sh

# 查看所有进程
ps aux | grep python | grep main_refactored

# 查看端口占用
lsof -i :8080  # 如果有web接口

# 查看系统资源
top | grep python
```

### 日志分析命令
```bash
# 查看最新错误
tail -50 logs/trading_system.log | grep ERROR

# 统计今日交易信号数
grep "$(date +%Y-%m-%d)" logs/trading_system.log | grep "signal" | wc -l

# 查看性能指标
grep "performance\|memory\|cpu" logs/trading_system.log | tail -20
```

## 📞 紧急情况处理

### 紧急停机
```bash
# 1. 立即停止所有服务
pkill -f "main_refactored.py"
launchctl stop com.trading.system.refactored

# 2. 检查是否完全停止
ps aux | grep main_refactored

# 3. 记录停机时间和原因
echo "$(date): 紧急停机 - 原因: XXX" >> logs/emergency.log
```

### 紧急恢复
```bash
# 1. 检查系统状态
python -c "import quant; print('模块导入正常')"

# 2. 使用最小配置启动
python main_refactored.py config.json

# 3. 如果失败，回滚到原系统
python main.py config.json
```

### 故障报告
```bash
# 生成故障报告
echo "=== 故障报告 $(date) ===" > incident_report.txt
echo "系统状态:" >> incident_report.txt
ps aux | grep python >> incident_report.txt
echo "最近错误:" >> incident_report.txt
tail -100 logs/trading_system.log | grep ERROR >> incident_report.txt
echo "系统资源:" >> incident_report.txt
free -h >> incident_report.txt
df -h >> incident_report.txt
```

## 📋 操作清单

### 每日操作清单
- [ ] 检查系统是否正常运行
- [ ] 查看过夜日志是否有错误
- [ ] 验证交易信号生成正常
- [ ] 检查数据库连接状态
- [ ] 监控系统资源使用

### 每周操作清单
- [ ] 运行完整的健康检查
- [ ] 清理过期日志文件
- [ ] 备份重要数据和配置
- [ ] 检查系统性能指标
- [ ] 更新系统依赖包

### 每月操作清单
- [ ] 运行完整的性能基准测试
- [ ] 分析系统运行统计数据
- [ ] 检查硬盘空间使用情况
- [ ] 审查和优化配置参数
- [ ] 计划系统升级维护

---

**💡 提示**: 建议将常用命令创建为shell别名或脚本，提高操作效率。如有问题，首先查看日志文件，大部分问题都能在日志中找到线索。