# 🔧 故障排除指南

## 常见部署问题及解决方案

### 问题1: `ModuleNotFoundError: No module named 'quant'`

**现象:**
```
Traceback (most recent call last):
  File "scripts/init_database.py", line 15, in <module>
    from quant.database_manager import db
ModuleNotFoundError: No module named 'quant'
```

**原因:** Python模块路径配置问题

**解决方案:**
```bash
# 方法1: 运行自动修复脚本
./scripts/fix_deployment_issues.sh

# 方法2: 手动修复
cd /Users/<USER>/PycharmProjects/Mitchquant自动交易策略版本20250810
source trading_system_venv/bin/activate
export PYTHONPATH=$PWD:$PYTHONPATH
python scripts/init_database.py --force
```

### 问题2: 虚拟环境缺失或损坏

**现象:**
```
[ERROR] 虚拟环境不存在，请先运行部署脚本
```

**解决方案:**
```bash
# 重新创建虚拟环境
python3 -m venv trading_system_venv
source trading_system_venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 问题3: 依赖包缺失

**现象:**
```
ModuleNotFoundError: No module named 'scipy'
ModuleNotFoundError: No module named 'sklearn'
```

**解决方案:**
```bash
# 激活虚拟环境并安装缺失依赖
source trading_system_venv/bin/activate
pip install scipy scikit-learn psutil pytest-asyncio

# 或者重新安装所有依赖
pip install -r requirements.txt
```

### 问题4: 配置文件JSON语法错误

**现象:**
```
[WARNING] JSON语法: ⚠️  可能有错误
```

**解决方案:**
```bash
# 验证JSON语法
python -m json.tool config.json

# 如果有错误，手动修复或恢复备份
cp config.json.backup config.json
```

### 问题5: 系统启动后立即停止

**现象:**
系统启动几秒后自动退出

**排查步骤:**
```bash
# 1. 检查日志
tail -50 logs/trading_system.log

# 2. 检查配置
./scripts/health_check.sh

# 3. 手动启动查看详细错误
source trading_system_venv/bin/activate
python main_refactored.py config.development.json
```

### 问题6: 端口占用或权限问题

**现象:**
```
Permission denied
Address already in use
```

**解决方案:**
```bash
# 检查端口占用
lsof -i :8080  # 如果系统使用了端口

# 杀死占用进程
pkill -f "main_refactored.py"

# 检查文件权限
chmod +x scripts/*.sh
chmod 755 main_refactored.py
```

## 🚨 紧急处理流程

### 系统完全无法启动

1. **立即回滚到原系统:**
   ```bash
   python main.py config.json
   ```

2. **运行自动修复:**
   ```bash
   ./scripts/fix_deployment_issues.sh
   ```

3. **手动重新部署:**
   ```bash
   rm -rf trading_system_venv
   ./scripts/deploy_refactored.sh development
   ```

### 数据丢失或损坏

1. **恢复数据库备份:**
   ```bash
   cp trading_system.db.backup trading_system.db
   ```

2. **重新初始化数据库:**
   ```bash
   source trading_system_venv/bin/activate
   python scripts/init_database.py --force
   ```

### 配置文件损坏

1. **恢复配置备份:**
   ```bash
   cp config.json.backup config.json
   ```

2. **重新创建配置:**
   ```bash
   # 参考 docs/MIGRATION_GUIDE.md 中的配置示例
   ```

## 📋 诊断检查清单

### 快速诊断命令
```bash
# 1. 系统整体健康检查
./scripts/health_check.sh

# 2. 检查虚拟环境
ls -la trading_system_venv/

# 3. 检查依赖包
source trading_system_venv/bin/activate
pip list | grep -E "(pandas|numpy|scipy|sklearn)"

# 4. 检查配置文件
python -m json.tool config.json

# 5. 检查数据库
ls -la *.db data/*.db

# 6. 检查日志
tail -20 logs/trading_system.log

# 7. 检查进程
ps aux | grep python
```

### 详细诊断脚本
```bash
# 创建诊断报告
./scripts/generate_diagnostic_report.sh
```

## 🆘 获得帮助

### 自助排查顺序

1. **查看错误日志**
   ```bash
   tail -50 logs/trading_system.log | grep ERROR
   ```

2. **运行健康检查**
   ```bash
   ./scripts/health_check.sh
   ```

3. **运行自动修复**
   ```bash
   ./scripts/fix_deployment_issues.sh
   ```

4. **手动重新部署**
   ```bash
   ./scripts/deploy_refactored.sh development
   ```

5. **如果仍有问题，回滚到原系统**
   ```bash
   python main.py config.json
   ```

### 收集问题信息

如果问题仍然存在，请收集以下信息：

```bash
# 生成完整的系统报告
echo "=== 系统环境 ===" > problem_report.txt
uname -a >> problem_report.txt
python3 --version >> problem_report.txt

echo -e "\n=== 虚拟环境 ===" >> problem_report.txt
source trading_system_venv/bin/activate
pip list >> problem_report.txt

echo -e "\n=== 错误日志 ===" >> problem_report.txt
tail -100 logs/trading_system.log >> problem_report.txt

echo -e "\n=== 配置文件 ===" >> problem_report.txt
cat config.json >> problem_report.txt

echo -e "\n=== 目录结构 ===" >> problem_report.txt
find . -name "*.py" | head -20 >> problem_report.txt

echo "问题报告已生成: problem_report.txt"
```

### 常用修复命令速查

| 问题类型 | 修复命令 |
|---------|---------|
| 模块导入错误 | `export PYTHONPATH=$PWD:$PYTHONPATH` |
| 虚拟环境问题 | `python3 -m venv trading_system_venv` |
| 依赖缺失 | `pip install -r requirements.txt` |
| 配置错误 | `python -m json.tool config.json` |
| 权限问题 | `chmod +x scripts/*.sh` |
| 进程残留 | `pkill -f "main_refactored.py"` |
| 数据库问题 | `python scripts/init_database.py --force` |
| 完全重置 | `./scripts/fix_deployment_issues.sh` |

---

**💡 提示**: 大多数问题都可以通过运行 `./scripts/fix_deployment_issues.sh` 自动解决。如果问题持续存在，请按照上述步骤逐一排查。