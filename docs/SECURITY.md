# Security Configuration Guide

## Overview

This document provides instructions for securely configuring the trading system with proper API key management.

## Environment Variable Setup

### 1. Create Environment File

Copy the example environment file and fill in your credentials:

```bash
cp .env.example .env
```

### 2. Configure API Credentials

Edit the `.env` file with your actual API credentials:

```bash
# Binance API Credentials
BINANCE_ACCESS_KEY=your_actual_binance_access_key
BINANCE_SECRET_KEY=your_actual_binance_secret_key

# OKX API Credentials  
OKX_ACCESS_KEY=your_actual_okx_access_key
OKX_SECRET_KEY=your_actual_okx_secret_key
OKX_PASSPHRASE=your_actual_okx_passphrase

# DingTalk Webhook (optional)
DINGTALK_WEBHOOK=your_actual_dingtalk_webhook_url

# Environment Configuration
ENVIRONMENT=development
```

### 3. Set File Permissions

Ensure your `.env` file has restricted permissions:

```bash
chmod 600 .env
```

## Security Best Practices

### API Key Management

1. **Never commit API keys to version control**
   - The `.env` file should be in your `.gitignore`
   - API keys have been removed from `config.json`

2. **Use environment variables in production**
   - Set environment variables directly on your production server
   - Consider using a secrets management service

3. **Rotate API keys regularly**
   - Change your API keys periodically
   - Monitor API key usage for suspicious activity

### File Security

1. **Protect configuration files**
   ```bash
   chmod 600 config.json
   chmod 600 .env
   ```

2. **Restrict log file access**
   ```bash
   chmod 750 logs/
   chmod 640 logs/*.log
   ```

## Migration from Old Configuration

If you're upgrading from the old system with API keys in `config.json`:

1. **Create your `.env` file** using the template above
2. **Copy your API keys** from the old `config.json` to the `.env` file
3. **Verify the system works** with environment variables
4. **Remove API keys from `config.json`** (already done in this update)

## System Behavior

The configuration manager now follows this priority:

1. **Environment variables** (highest priority)
2. **Config file values** (fallback)
3. **Error if neither found** (for critical credentials)

## Troubleshooting

### Common Issues

1. **"API credentials not found" error**
   - Check that your `.env` file exists and has the correct variable names
   - Verify file permissions allow reading
   - Ensure no extra spaces around the `=` sign in `.env`

2. **Environment variables not loading**
   - Make sure you're running from the correct directory
   - Check that the `.env` file is in the project root
   - Restart your application after changing environment variables

### Verification

To verify your configuration is working:

```bash
# Check environment variables are loaded
echo $BINANCE_ACCESS_KEY
echo $BINANCE_SECRET_KEY

# Test the system in development mode
ENVIRONMENT=development python3 main.py config.development.json
```

## Production Deployment

For production environments:

1. **Set environment variables directly** instead of using `.env` files
2. **Use a secrets management service** (AWS Secrets Manager, Azure Key Vault, etc.)
3. **Implement key rotation policies**
4. **Monitor API usage and set up alerts**
5. **Use separate API keys** for different environments

## Support

If you encounter issues with the security configuration, check:

1. The application logs in `logs/error.log`
2. Ensure all required environment variables are set
3. Verify API key permissions on the exchange platforms