# OKX Exchange Production Deployment Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [API Setup](#api-setup)
3. [Configuration](#configuration)
4. [Deployment Steps](#deployment-steps)
5. [Testing](#testing)
6. [Monitoring](#monitoring)
7. [Troubleshooting](#troubleshooting)
8. [Security Best Practices](#security-best-practices)

## Prerequisites

### System Requirements
- Python 3.8 or higher
- Ubuntu 20.04+ / macOS 12+ / Windows 10+
- Minimum 4GB RAM
- Stable internet connection
- Git installed

### Python Dependencies
```bash
# Install required packages
pip install -r config/requirements.txt
```

## API Setup

### Step 1: Create OKX Account
1. Go to [OKX Exchange](https://www.okx.com)
2. Complete KYC verification for API trading
3. Enable 2FA for security

### Step 2: Generate API Keys
1. Navigate to Account → API → Create V5 API Key
2. Configure API permissions:
   - ✅ Trade (required)
   - ✅ Read (required)
   - ❌ Withdraw (not recommended for security)
3. Set IP whitelist (recommended)
4. Save the following credentials securely:
   - API Key
   - Secret Key
   - Passphrase

### Step 3: Configure API Permissions
```
Recommended Settings:
- Permission: Trade
- IP Whitelist: Your server IP(s)
- Validity: No expiry (monitor regularly)
```

## Configuration

### Step 1: Environment Variables
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env file with your credentials
nano .env
```

Add your OKX credentials:
```env
# OKX API Credentials
OKX_ACCESS_KEY=your-api-key-here
OKX_SECRET_KEY=your-secret-key-here
OKX_PASSPHRASE=your-passphrase-here

# Trading Configuration
DEFAULT_EXCHANGE=okx
ENVIRONMENT=production
MAX_DAILY_LOSS=1000
MAX_POSITION_SIZE=5000
DEFAULT_LEVERAGE=10
```

### Step 2: Production Configuration
Use the OKX production config:
```bash
# Use OKX production configuration
cp config/config.okx_production.json config/config.production.json
```

### Step 3: Customize Trading Parameters
Edit `config/config.production.json`:
```json
{
  "EXCHANGES": {
    "okx": {
      "trading_settings": {
        "leverage": 10,           // Your preferred leverage
        "default_symbol": "BTCUSDT",  // Trading pair
        "trade_mode": "isolated"  // isolated or cross
      }
    }
  },
  
  "RISK_MANAGEMENT": {
    "max_daily_loss": 1000,      // Maximum daily loss in USDT
    "max_position_size_usdt": 5000,  // Maximum position size
    "base_position_size_usdt": 200   // Default position size
  }
}
```

## Deployment Steps

### Step 1: Initial Setup
```bash
# Clone or update the repository
git pull origin main

# Create virtual environment
python3 -m venv trading_venv
source trading_venv/bin/activate  # Linux/Mac
# or
# trading_venv\Scripts\activate  # Windows

# Install dependencies
pip install -r config/requirements.txt
```

### Step 2: Test Connection
```bash
# Test OKX connection
python tests/test_okx_basic_functionality.py

# Test multi-exchange system
python tests/test_multi_exchange_system.py
```

### Step 3: Dry Run (Paper Trading)
```bash
# Run in test mode first
python main.py config/config.okx_production.json --dry-run
```

### Step 4: Production Deployment
```bash
# Start the trading system
python main.py config/config.okx_production.json

# Or run in background with nohup
nohup python main.py config/config.okx_production.json > logs/trading.log 2>&1 &

# Or use systemd service (recommended)
sudo systemctl start okx-trading-bot
```

### Step 5: Using systemd (Recommended for Linux)
Create service file `/etc/systemd/system/okx-trading-bot.service`:
```ini
[Unit]
Description=OKX Trading Bot
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/Mitchquant自动交易(OKX交易所)
Environment="PATH=/path/to/trading_venv/bin"
ExecStart=/path/to/trading_venv/bin/python main.py config/config.okx_production.json
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable okx-trading-bot
sudo systemctl start okx-trading-bot
sudo systemctl status okx-trading-bot
```

## Testing

### Connection Test
```python
# Test script: test_okx_connection.py
import asyncio
from quant.exchange.exchange_factory import ExchangeFactory

async def test():
    okx = await ExchangeFactory.get_exchange("okx")
    price = await okx.get_current_price("BTCUSDT")
    print(f"BTC Price: ${price:,.2f}")
    
    balance = await okx.get_balance("USDT")
    print(f"USDT Balance: {balance}")

asyncio.run(test())
```

### Order Execution Test
```python
# Test with small amount first
from quant.exchange.exchange_interface import OrderRequest, OrderType, OrderSide

# Create a test order (very small size)
order = OrderRequest(
    symbol="BTCUSDT",
    side=OrderSide.BUY,
    order_type=OrderType.LIMIT,
    quantity=0.001,  # Minimum size
    price=40000,     # Far from market price
    position_side=PositionSide.LONG
)
```

## Monitoring

### Log Files
```bash
# Main trading log
tail -f logs/trading_system.log

# OKX specific logs
tail -f logs/okx_trading.log

# Error logs
tail -f logs/error.log

# Monitor all logs
tail -f logs/*.log
```

### Health Checks
```bash
# Check system status
python scripts/health_check.sh

# Check positions
sqlite3 data/trading_system.db "SELECT * FROM trades WHERE status='pending';"

# Monitor performance
python tools/analyze_recent_trades.py
```

### Key Metrics to Monitor
- Account balance changes
- Open positions
- Daily P&L
- API rate limit usage
- Connection stability
- Order execution latency

## Troubleshooting

### Common Issues

#### 1. API Authentication Failed
```
Error: OKX API authentication failed
Solution:
- Verify API credentials in .env file
- Check API key permissions
- Ensure passphrase is correct
- Verify IP whitelist if enabled
```

#### 2. Insufficient Balance
```
Error: Insufficient balance for order
Solution:
- Check account balance: await okx.get_balance()
- Reduce position size in config
- Ensure correct margin mode (isolated/cross)
```

#### 3. Rate Limit Exceeded
```
Error: API rate limit exceeded
Solution:
- Reduce API call frequency
- Implement request queuing
- Use WebSocket for real-time data
```

#### 4. Connection Issues
```
Error: Connection timeout
Solution:
- Check internet connection
- Verify API endpoints are accessible
- Try using proxy if needed
- Check OKX system status
```

### Debug Mode
```bash
# Run with debug logging
LOG_LEVEL=debug python main.py config/config.okx_production.json

# Enable verbose API logging
export DEBUG_API_CALLS=true
python main.py
```

## Security Best Practices

### 1. API Key Security
- ✅ Use environment variables for API keys
- ✅ Never commit credentials to git
- ✅ Enable IP whitelist on OKX
- ✅ Use read-only keys for monitoring
- ✅ Rotate API keys regularly

### 2. System Security
```bash
# Secure the .env file
chmod 600 .env

# Restrict access to config files
chmod 600 config/*.json

# Use encrypted storage for sensitive data
# Consider using tools like ansible-vault or sops
```

### 3. Risk Management
- Set conservative position sizes initially
- Use stop-loss orders
- Monitor positions regularly
- Set daily loss limits
- Keep emergency shutdown ready

### 4. Monitoring & Alerts
```python
# Set up alerts for:
- Large losses
- Unusual trading volume
- API errors
- Connection failures
- Position limit breaches
```

### 5. Backup & Recovery
```bash
# Regular database backups
./scripts/backup_database.sh

# Configuration backup
cp -r config/ backups/config_$(date +%Y%m%d)/

# Keep trading logs
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs/
```

## Production Checklist

Before going live:
- [ ] API keys configured and tested
- [ ] Risk parameters set conservatively
- [ ] Stop-loss and position limits configured
- [ ] Monitoring and alerts set up
- [ ] Database backup scheduled
- [ ] Emergency shutdown procedure ready
- [ ] Dry run completed successfully
- [ ] Small position test executed
- [ ] Log rotation configured
- [ ] System monitoring active

## Support & Resources

### OKX Documentation
- [OKX API Documentation](https://www.okx.com/docs-v5/en/)
- [WebSocket Guide](https://www.okx.com/docs-v5/en/#websocket-api)
- [Trading Rules](https://www.okx.com/trade-market/info/swap)

### Project Resources
- GitHub Issues: Report bugs and issues
- Logs: Check `logs/` directory for detailed information
- Database: `data/trading_system.db` for trade history

### Emergency Contacts
- OKX Support: [support.okx.com](https://www.okx.com/support)
- System Admin: [Your contact info]

## Quick Commands Reference

```bash
# Start trading
python main.py config/config.okx_production.json

# Stop trading
./scripts/stop_system.sh

# Check status
./scripts/health_check.sh

# View recent trades
python tools/analyze_recent_trades.py

# Emergency stop
./scripts/emergency_stop.sh

# Database query
sqlite3 data/trading_system.db

# View logs
tail -f logs/trading_system.log
```

---

## Important Notes

⚠️ **WARNING**: Live trading involves real money and risk. Always:
1. Start with small amounts
2. Test thoroughly in dry-run mode
3. Monitor closely during initial deployment
4. Have emergency procedures ready
5. Never invest more than you can afford to lose

📝 **Remember**: This is an automated trading system. While it includes risk management features, no trading system is perfect. Markets can be unpredictable, and losses can occur.

🔒 **Security**: Keep your API keys secure and never share them. Regularly review your API permissions and access logs on OKX.

---

Last Updated: 2024-12-20
Version: 1.0.0
