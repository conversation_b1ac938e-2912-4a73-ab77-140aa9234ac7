# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## System Overview

This is a **Binance cryptocurrency automated trading system** that analyzes market data every 30 minutes and executes trades automatically. The system features dual architecture - an original monolithic version and a refactored service-based version with dependency injection.

**Current Trading Symbol**: BTCUSDC (configurable via config files)
**Analysis Schedule**: Every 30 minutes at :09 and :39
**Position Holding**: 10-30 minutes per trade
**Risk Management**: Kelly Criterion with daily loss limits

## Essential Commands

### Quick Start
```bash
# First-time setup (creates virtual environment and installs dependencies)
./scripts/deploy_refactored.sh development

# Start system with development config
./scripts/start_system.sh config.development.json

# Stop system gracefully
./scripts/stop_system.sh

# Restart system
./scripts/restart_system.sh config.development.json

# Check system health
./scripts/health_check.sh
```

### Development Commands
```bash
# Activate virtual environment
source trading_system_venv/bin/activate

# Install/update dependencies
pip install -r config/requirements.txt

# Run refactored system (recommended)
python main_refactored.py config/config.development.json

# Run original system (fallback)
python main.py config/config.json

# Background execution
nohup python main_refactored.py config/config.development.json > logs/nohup.out 2>&1 &
```

### Testing Commands
```bash
# Run integration tests
python tests/integration/test_behavior_verification.py
python tests/integration/test_critical_workflows.py
python tests/integration/performance_comparison.py

# Test Binance connection
python scripts/test_binance_connection.py

# Validate migration
python scripts/validate_pre_migration.py
python scripts/validate_post_migration.py
```

### Database Operations
```bash
# View all tables
sqlite3 data/trading_system.db ".tables"

# Check pending trades
sqlite3 data/trading_system.db "SELECT * FROM trades WHERE status='pending' ORDER BY created_at DESC LIMIT 5;"

# View recent trades
sqlite3 data/trading_system.db "SELECT id, symbol, signal_type, confidence, created_at FROM trades ORDER BY created_at DESC LIMIT 10;"

# Check settlement status
sqlite3 data/trading_system.db "SELECT COUNT(*) as pending_count FROM trades WHERE status='pending' AND created_at < datetime('now', '-10 minutes');"
```

### Log Monitoring
```bash
# Watch main log
tail -f logs/trading_system.log

# Monitor errors only
tail -f logs/error.log | grep ERROR

# View trade execution logs
tail -f logs/trade_*.log

# Clean old logs
./scripts/clean_logs.sh
```

## Architecture Overview

### Dual Architecture System

The codebase maintains **two parallel architectures** for stability and migration flexibility:

#### 1. Original Monolithic (`main.py`)
- **Entry**: `main.py` → `TradingSystem` class
- **Structure**: Single class orchestrates all operations
- **Pros**: Battle-tested in production, simpler debugging
- **Cons**: Tightly coupled, harder to test individual components

#### 2. Refactored Service-Based (`main_refactored.py`) [RECOMMENDED]
- **Entry**: `main_refactored.py` → `TradingSystemOrchestrator`
- **Structure**: Service-oriented with dependency injection
- **ServiceContainer**: Manages service lifecycle and dependencies
- **Pros**: Better testability, fault isolation, cleaner code
- **Cons**: More complex, requires understanding of service patterns

### Core Services (Refactored Architecture)

```python
# Service initialization order (important!)
1. MarketAnalysisService     # Signal generation & trade execution
2. SettlementService         # Trade settlement (30-second intervals)
3. RiskManagementService     # Position sizing & risk control
4. HealthMonitoringService   # System health checks
5. SystemMetricsService      # Performance monitoring
```

### Key Components (Both Architectures)

| Component | File | Purpose |
|-----------|------|---------|  
| Analysis Engine | `quant/simple_analysis_engine.py` | Generate trading signals using MA, RSI, MACD, Bollinger Bands |
| Binance Client | `quant/binance_client.py` | Exchange API wrapper for orders and market data |
| Database Manager | `quant/database_manager.py` | SQLite persistence with connection pooling |
| Auto Trader | `quant/strategies/auto_trader.py` | Execute trades with limit/market order logic |
| Exit Manager | `quant/strategies/simple_exit_manager.py` | Auto-close positions before candle end |
| Risk Manager | `quant/risk_manager.py` | Kelly Criterion position sizing, daily loss limits |
| Recovery Manager | `quant/recovery_manager.py` | Retry failed operations with exponential backoff |

### Trading Signal Flow

```mermaid
graph LR
    A[Market Data] --> B[Analysis Engine]
    B --> C{Signal Generation}
    C -->|LONG/SHORT| D[Risk Manager]
    D --> E[Position Sizing]
    E --> F[Auto Trader]
    F --> G[Binance API]
    G --> H[Database]
    H --> I[Exit Manager]
    I --> J[Settlement]
```

#### 1. Market Analysis (Cron: :09 and :39)
```python
# Triggered by: market_analysis_and_trade_task()
- Fetch 100 x 30-minute candles from Binance
- Calculate indicators:
  • MA(20, 50) - Moving averages
  • RSI(14) - Momentum
  • MACD(12, 26, 9) - Trend
  • Bollinger Bands(20, 2) - Volatility
- Generate signal with confidence score (0.0-1.0)
```

#### 2. Risk Assessment
```python
# Handled by: RiskManager.calculate_position_size()
- Base position: 150 USDT (configurable)
- Kelly factor: 0.25 (conservative)
- Confidence adjustment: size * confidence_score
- Daily loss limit: 1000 USDT
- Max consecutive losses: 3
- Final range: 10-1000 USDT
```

#### 3. Trade Execution
```python
# Handled by: AutoTrader.execute_trade()
- Order type: LIMIT (maker) or MARKET (taker)
- Limit order timeout: 30 seconds
- Retry logic: 3 attempts with backoff
- Database: Store with 'pending' status
- Exit manager: Schedule auto-close
```

#### 4. Settlement Processing (Every 30 seconds)
```python
# Handled by: SettlementService.run_settlement_check()
- Window: Trades older than 10 minutes
- Batch size: 20 concurrent settlements
- Calculate P&L from actual exit price
- Update status: 'pending' → 'settled'
- Clean up exit manager entries
```

### Configuration System

#### Configuration Hierarchy
```
config/
├── config.json              # Base configuration (defaults)
├── config.development.json  # Development overrides
└── config.production.json   # Production settings
```

#### Key Configuration Sections

```json
// BINANCE - Exchange settings
{
  "BINANCE": {
    "API_KEY": "configured_via_env_vars",  // Set in .env file
    "SECRET_KEY": "configured_via_env_vars"
  }
}

// SCHEDULING - Analysis timing
{
  "SCHEDULING": {
    "analysis_minutes": [9, 39],  // Run at :09 and :39
    "timezone": "Asia/Shanghai"
  }
}

// RISK_MANAGEMENT - Position control
{
  "RISK_MANAGEMENT": {
    "base_position_size_usdt": 150.0,
    "min_position_size_usdt": 10.0,
    "max_position_size_usdt": 1000.0,
    "MAX_DAILY_LOSS": 1000.0,
    "max_consecutive_losses": 3
  }
}

// AUTO_TRADER - Execution settings
{
  "AUTO_TRADER": {
    "symbol": "BTCUSDC",  // Trading pair
    "order_mode": "SMART",  // LIMIT first, then MARKET
    "max_position_minutes": 30
  }
}

// SERVICES - Enable/disable services (refactored only)
{
  "SERVICES": {
    "market_analysis": {"enabled": true},
    "settlement": {"enabled": true, "max_concurrent": 20},
    "risk_management": {"enabled": true}
  }
}
```

#### Environment Variables (.env file)
```bash
# Required for Binance API
BINANCE_ACCESS_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here

# Optional
ENVIRONMENT=development  # or production
```

### Database Schema

#### SQLite Databases
- **Main**: `data/trading_system.db` - Trading data
- **Time Series**: `data/timeseries.db` - Market data cache

#### Core Tables

```sql
-- trades table (main trading records)
CREATE TABLE trades (
    id INTEGER PRIMARY KEY,
    symbol TEXT,
    signal_type TEXT,  -- 'LONG' or 'SHORT'
    confidence REAL,    -- 0.0 to 1.0
    entry_price REAL,
    exit_price REAL,
    quantity REAL,
    status TEXT,        -- 'pending', 'settled', 'failed'
    created_at TIMESTAMP,
    settled_at TIMESTAMP
);

-- orders table (exchange orders)
CREATE TABLE orders (
    id INTEGER PRIMARY KEY,
    trade_id INTEGER,
    order_id TEXT,      -- Binance order ID
    side TEXT,          -- 'BUY' or 'SELL'
    price REAL,
    quantity REAL,
    status TEXT,
    created_at TIMESTAMP
);

-- risk_metrics table (daily tracking)
CREATE TABLE risk_metrics (
    date TEXT PRIMARY KEY,
    total_trades INTEGER,
    winning_trades INTEGER,
    total_pnl REAL,
    max_drawdown REAL,
    consecutive_losses INTEGER
);
```

### Error Handling & Recovery

#### Recovery Hierarchy
```python
# Priority levels in RecoveryManager
class RecoveryPriority:
    CRITICAL = 0  # System-critical operations
    HIGH = 1      # Trade execution
    MEDIUM = 2    # Data fetching
    LOW = 3       # Logging, metrics
```

#### Error Handling Components

1. **RecoveryManager** (`quant/recovery_manager.py`)
   - Exponential backoff: 1s → 2s → 4s → 8s
   - Max retries: 3 (configurable)
   - Priority queue for operations

2. **CircuitBreaker Pattern**
   - Threshold: 5 failures in 60 seconds
   - Cooldown: 5 minutes
   - Auto-recovery with health checks

3. **Global Exception Handler**
   - Catches unhandled exceptions
   - Logs to `error.log` with full traceback
   - Triggers graceful shutdown if critical

4. **Notification System**
   - DingTalk webhook for critical alerts
   - Configurable in `DINGTALK` config section
   - Alert types: Trade failures, risk breaches, system errors

## Code Architecture Patterns

### Service Development (Refactored Architecture)

```python
# All services inherit from BaseService
from quant.services.base_service import BaseService

class MyService(BaseService):
    async def initialize(self):
        """Setup resources, called once at startup"""
        self.client = await create_client()
        
    async def cleanup(self):
        """Clean up resources, called at shutdown"""
        await self.client.close()
        
    def get_health_status(self):
        """Return health check dict"""
        return {
            "status": "healthy",
            "details": {"connections": 1}
        }
        
    async def execute_safe(self, *args, **kwargs):
        """Main execution with error handling"""
        try:
            return await self._execute(*args, **kwargs)
        except Exception as e:
            self.logger.error(f"Execution failed: {e}")
            raise
```

### Dependency Injection Pattern

```python
# ServiceContainer manages dependencies
from quant.services.service_container import ServiceContainer

container = ServiceContainer(config_manager)
container.register_service('market_analysis', MarketAnalysisService)
container.register_service('risk_management', RiskManagementService)

# Services can access other services
market_service = container.get_service('market_analysis')
risk_service = container.get_service('risk_management')
```

### Logging Best Practices

```python
from quant.utils.logger import get_logger
logger = get_logger(__name__)

# Log levels and when to use them
logger.debug("Detailed info for debugging")      # Development only
logger.info("Normal operation events")           # Important events
logger.warning("Warning but recoverable")        # Potential issues  
logger.error("Error that needs attention")       # Failures
logger.critical("System-critical failure")       # Fatal errors

# Trade-specific logging
from quant.utils.structured_logger import StructuredLogger
trade_logger = StructuredLogger("trades")
trade_logger.log_trade({
    "action": "OPEN",
    "symbol": "BTCUSDC",
    "side": "BUY",
    "price": 65000.0,
    "quantity": 0.001
})
```

### Async/Await Patterns

```python
# All I/O operations should be async
import asyncio
from typing import List, Dict

async def fetch_market_data() -> List[Dict]:
    """Fetch data concurrently"""
    tasks = [
        binance_client.get_klines("30m", 100),
        binance_client.get_orderbook(),
        binance_client.get_ticker()
    ]
    results = await asyncio.gather(*tasks)
    return results

# Use asyncio.create_task for fire-and-forget
async def process_signal(signal):
    task = asyncio.create_task(auto_trader.execute_trade(signal))
    # Don't await if you don't need the result immediately
```

### Testing Patterns

```python
# Use dependency injection for testing
import pytest
from unittest.mock import AsyncMock

@pytest.fixture
def mock_binance():
    mock = AsyncMock()
    mock.get_klines.return_value = [...]
    return mock

async def test_signal_generation(mock_binance):
    service = MarketAnalysisService()
    service.binance_client = mock_binance  # Inject mock
    
    signal = await service.generate_signal()
    assert signal.confidence > 0.5
```

## Troubleshooting Guide

### Common Issues

#### 1. WebSocket Connection Issues
```python
# Check connection status
await real_time_data_manager.get_stream_status()

# Restart streams
await real_time_data_manager.stop_all_streams()
await real_time_data_manager.initialize()
```

#### 2. Settlement Backlog
```bash
# Check pending settlements
sqlite3 data/trading_system.db "
  SELECT COUNT(*) as pending, 
         MIN(created_at) as oldest 
  FROM trades 
  WHERE status='pending';"

# Force settlement (if needed)
python scripts/force_settlement.py --older-than-minutes=30
```

#### 3. Risk Manager Suspended Trading
```python
# Check risk status
risk_report = risk_manager.get_risk_report()
print(f"Suspended: {risk_report['is_suspended']}")
print(f"Daily loss: {risk_report['daily_loss']}")
print(f"Consecutive losses: {risk_report['consecutive_losses']}")

# Reset risk metrics (use with caution!)
sqlite3 data/trading_system.db "
  UPDATE risk_metrics 
  SET consecutive_losses = 0 
  WHERE date = date('now');"
```

#### 4. API Rate Limits
```python
# Binance rate limits
# - Weight: 1200 requests/minute
# - Order: 10 orders/second
# - Raw: 5000 requests/5 minutes

# Check current usage
headers = binance_client.get_exchange_info()
print(f"Used weight: {headers.get('X-MBX-USED-WEIGHT-1M')}")
```

#### 5. Database Lock Issues
```bash
# Check for locks
fuser data/trading_system.db

# Force unlock (last resort)
rm data/trading_system.db-journal
```

### Emergency Procedures

#### Stop All Trading
```bash
# 1. Stop the system
./scripts/stop_system.sh

# 2. Cancel all open orders
python scripts/emergency_cancel_all.py

# 3. Switch to safe mode
echo '{"AUTO_TRADER": {"emergency_stop": true}}' > config/emergency.json
```

#### Switch Trading Symbol
```bash
# Use the symbol manager
python scripts/emergency_switch_market.py --symbol=ETHUSDT

# Or edit config directly
jq '.AUTO_TRADER.symbol = "ETHUSDT"' config/config.json > temp.json && mv temp.json config/config.json
```

## Performance & Optimization

### Resource Usage

| Component | CPU | Memory | Network |
|-----------|-----|--------|---------|  
| Idle | <10% | 200-300MB | 10 req/min |
| Analysis | 20-40% | 400-500MB | 50 req/min |
| Trading | 30-50% | 500-600MB | 100 req/min |

### Database Optimization

```sql
-- Key indexes for performance
CREATE INDEX idx_trades_status_created ON trades(status, created_at);
CREATE INDEX idx_trades_symbol ON trades(symbol);
CREATE INDEX idx_orders_trade_id ON orders(trade_id);
CREATE INDEX idx_settlements_date ON settlements(settled_date);
```

### Concurrent Processing Limits

- **Settlement batch**: 20 concurrent settlements
- **API requests**: 5 concurrent operations (configurable)
- **Database connections**: 10 connection pool (SQLAlchemy)
- **WebSocket streams**: 3 concurrent (price, depth, trades)

### Memory Management

```python
# Automatic cleanup of old data
- Trade history: Keep 90 days (configurable)
- Logs rotation: 7 days or 20MB per file
- Market data cache: 24 hours TTL
```

## Deployment & Operations

### Production Deployment Checklist

```bash
# 1. Pre-deployment validation
python scripts/validate_pre_migration.py

# 2. Backup database
cp data/trading_system.db data/trading_system.db.backup

# 3. Deploy with production config
./scripts/deploy_refactored.sh production

# 4. Verify deployment
./scripts/health_check.sh

# 5. Monitor for 24 hours
tail -f logs/trading_system.log
```

### Environment Configuration

| Environment | Config File | API Keys | Log Level | Services |
|------------|-------------|----------|-----------|----------|
| Development | config.development.json | Testnet | DEBUG | All enabled |
| Staging | config.staging.json | Testnet | INFO | All enabled |
| Production | config.production.json | Mainnet | INFO | Selective |

### Service Management

#### Linux (systemd)
```bash
sudo systemctl start trading-system-refactored
sudo systemctl status trading-system-refactored
sudo systemctl enable trading-system-refactored  # Auto-start on boot
journalctl -u trading-system-refactored -f        # View logs
```

#### macOS (launchd)
```bash
launchctl load ~/Library/LaunchAgents/com.trading.system.refactored.plist
launchctl list | grep trading
launchctl unload ~/Library/LaunchAgents/com.trading.system.refactored.plist
```

### Migration Between Architectures

```bash
# Switch from original to refactored
./scripts/stop_system.sh
python scripts/validate_pre_migration.py
./scripts/start_system.sh config.development.json

# Rollback to original if needed
./scripts/stop_system.sh
python main.py config/config.json
```

### Monitoring & Alerting

- **Logs**: Check `logs/` directory, rotated daily
- **Metrics**: System metrics logged every 5 minutes
- **Alerts**: DingTalk webhook for critical events
- **Health**: Run `./scripts/health_check.sh` periodically

## Critical Files Reference

### Core System Files
```
quant/
├── binance_client.py              # Exchange API integration
├── database_manager.py            # SQLite persistence layer
├── risk_manager.py                # Position sizing & risk control
├── config_manager.py              # Configuration management
├── trading_system_orchestrator.py # Refactored architecture orchestrator
└── simple_analysis_engine.py      # Signal generation engine
```

### Strategy Files
```
quant/strategies/
├── auto_trader.py                 # Automatic trade execution
├── simple_exit_manager.py         # Position exit management
├── limit_order_executor.py        # Limit order handling
└── position_exit_manager.py       # Advanced exit strategies
```

### Service Files (Refactored)
```
quant/services/
├── base_service.py                # Base service class
├── service_container.py           # Dependency injection container
├── market_analysis_service.py     # Market analysis service
├── settlement_service.py          # Trade settlement service
├── risk_management_service.py     # Risk management service
└── health_monitoring_service.py   # Health monitoring service
```

### Configuration Files
```
config/
├── config.json                    # Base configuration
├── config.development.json        # Development overrides
├── config.production.json         # Production settings
├── pyproject.toml                 # Python project settings
└── requirements.txt               # Python dependencies
```

### When Making Changes

| If you modify... | Also check... |
|-----------------|---------------|
| Signal generation | `simple_analysis_engine.py`, `refactored_analysis_engine.py` |
| Trade execution | `auto_trader.py`, `binance_client.py`, risk limits |
| Risk parameters | `risk_manager.py`, position sizing in config |
| Database schema | `database_manager.py`, migration scripts |
| Service behavior | `service_container.py`, service dependencies |
| Configuration | Both `config.json` and environment-specific configs |
