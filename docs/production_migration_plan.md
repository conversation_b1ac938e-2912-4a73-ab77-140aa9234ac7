# 生产环境迁移计划

## 📋 概述

本文档描述了从现有生产架构（main.py）迁移到新重构架构（main_refactored.py）的详细计划。

### 🎯 迁移目标
- 零宕机时间部署
- 保证数据一致性
- 降低迁移风险
- 快速回滚能力

### 🏗️ 架构变化
- **旧架构**: 单体式 TradingSystem 类
- **新架构**: 服务化 TradingSystemOrchestrator + ServiceContainer
- **优势**: 更好的可测试性、可维护性、扩展性

## 🔄 迁移策略：蓝绿部署

### Phase 1: 准备阶段
1. **备份系统**
2. **部署监控系统**
3. **验证新架构**
4. **准备回滚方案**

### Phase 2: 蓝绿切换
1. **启动绿色环境**（新架构）
2. **流量切换**
3. **验证功能正常**
4. **停用蓝色环境**（旧架构）

### Phase 3: 验证和清理
1. **性能对比**
2. **功能验证**
3. **清理旧代码**

---

## 📅 详细执行计划

### 阶段1：系统备份和准备 (30分钟)

#### 1.1 数据库备份
```bash
# 备份SQLite数据库
cp trading_system.db trading_system.db.backup.$(date +%Y%m%d_%H%M%S)
cp data/timeseries.db data/timeseries.db.backup.$(date +%Y%m%d_%H%M%S)
```

#### 1.2 配置文件备份
```bash
# 备份配置文件
cp config.json config.json.backup.$(date +%Y%m%d_%H%M%S)
cp -r logs logs.backup.$(date +%Y%m%d_%H%M%S)
```

#### 1.3 进程状态记录
```bash
# 记录当前运行状态
ps aux | grep main.py > current_processes.log
netstat -tlnp | grep :888 > current_ports.log
```

### 阶段2：监控系统部署 (15分钟)

#### 2.1 部署新监控系统
```bash
# 启动监控服务（在后台运行）
python test_monitoring_system.py &
MONITORING_PID=$!
echo $MONITORING_PID > monitoring.pid
```

#### 2.2 验证监控功能
- 访问 http://localhost:8888/dashboard
- 检查指标收集是否正常
- 验证告警系统工作状态

### 阶段3：新架构验证 (20分钟)

#### 3.1 集成测试验证
```bash
# 运行关键工作流测试
python tests/integration/test_critical_workflows.py
```

#### 3.2 服务健康检查
```bash
# 检查所有服务是否就绪
python -c "
from quant.trading_system_orchestrator import TradingSystemOrchestrator
from quant.config_manager import ConfigManager
import asyncio

async def health_check():
    config = ConfigManager('config.json')
    orchestrator = TradingSystemOrchestrator(config)
    status = orchestrator.service_container.get_container_status()
    print(f'Services ready: {status}')

asyncio.run(health_check())
"
```

### 阶段4：蓝绿切换执行 (10分钟)

#### 4.1 停止旧系统
```bash
# 优雅停止旧系统
OLD_PID=$(ps aux | grep "python.*main.py" | grep -v grep | awk '{print $2}')
if [ ! -z "$OLD_PID" ]; then
    echo "Stopping old system (PID: $OLD_PID)"
    kill -TERM $OLD_PID
    sleep 30  # 等待优雅关闭
    
    # 如果仍在运行，强制停止
    if ps -p $OLD_PID > /dev/null; then
        kill -KILL $OLD_PID
    fi
fi
```

#### 4.2 启动新系统
```bash
# 启动新架构系统
nohup python main_refactored.py > logs/main_refactored.log 2>&1 &
NEW_PID=$!
echo $NEW_PID > main_refactored.pid
echo "New system started (PID: $NEW_PID)"
```

#### 4.3 验证新系统启动
```bash
# 等待系统启动
sleep 15

# 检查进程状态
if ps -p $NEW_PID > /dev/null; then
    echo "✅ New system is running"
else
    echo "❌ New system failed to start"
    exit 1
fi

# 检查日志是否有严重错误
if grep -i "fatal\|error.*critical" logs/main_refactored.log; then
    echo "⚠️  Critical errors detected in logs"
fi
```

### 阶段5：功能验证 (15分钟)

#### 5.1 数据库连接验证
```bash
python -c "
from quant.database_manager import DatabaseManager
db = DatabaseManager()
status = db.get_database_status()
print(f'Database status: {status}')
assert status['status'] == 'healthy', f'Database unhealthy: {status}'
print('✅ Database connection verified')
"
```

#### 5.2 API连接验证
```bash
python -c "
from quant.binance_client import BinanceClient
import asyncio

async def test_api():
    client = BinanceClient()
    await client.initialize()
    price = await client.get_current_price()
    print(f'Current BTC price: \${price:,.2f}')
    await client.close()
    print('✅ Binance API connection verified')

asyncio.run(test_api())
"
```

#### 5.3 实时数据流验证
```bash
python -c "
from quant.real_time_data_manager import RealTimeDataManager
import asyncio

async def test_streams():
    manager = RealTimeDataManager()
    await manager.initialize()
    await manager.start_kline_stream('BTCUSDT', '1m')
    
    # 等待一些数据
    await asyncio.sleep(10)
    
    status = manager.get_stream_status()
    print(f'Stream status: {status}')
    
    await manager.stop()
    print('✅ Real-time data streams verified')

asyncio.run(test_streams())
"
```

#### 5.4 交易信号生成验证
```bash
python -c "
from quant.simple_analysis_engine import SimpleAnalysisEngine
import asyncio

async def test_analysis():
    engine = SimpleAnalysisEngine()
    signal = await engine.analyze_market()
    
    if signal:
        print(f'Signal generated: {signal[\"direction\"]} at \${signal[\"entry_price\"]:,.2f}')
        print('✅ Market analysis engine verified')
    else:
        print('ℹ️  No signal generated (normal behavior)')
        print('✅ Market analysis engine verified')

asyncio.run(test_analysis())
"
```

### 阶段6：性能监控 (10分钟)

#### 6.1 系统性能对比
```bash
# 记录新系统性能指标
python -c "
from quant.system_monitor import SystemMonitor
import json

monitor = SystemMonitor()
metrics = monitor.collect_system_metrics()
performance_data = {
    'timestamp': '$(date -Iseconds)',
    'cpu_percent': metrics.cpu_percent,
    'memory_percent': metrics.memory_percent,
    'disk_percent': metrics.disk_percent,
    'memory_mb': metrics.memory_mb
}

with open('migration_performance.json', 'w') as f:
    json.dump(performance_data, f, indent=2)

print('📊 Performance metrics recorded')
"
```

#### 6.2 监控告警检查
```bash
# 检查是否有新告警
curl -s http://localhost:8888/api/alerts | python -m json.tool
```

---

## 🔧 迁移脚本

创建自动化迁移脚本: