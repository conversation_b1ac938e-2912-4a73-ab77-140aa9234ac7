#!/usr/bin/env python3
"""
快速验证OKX集成状态
"""

import sys
import os
sys.path.insert(0, '.')

def test_okx_integration():
    print("🔧 OKX集成状态验证")
    print("="*50)
    
    try:
        # 1. 检查配置
        from quant.config_manager import config
        
        exchanges = config.get('EXCHANGES', {})
        default_exchange = exchanges.get('default', 'not_configured')
        auto_trader = config.get('AUTO_TRADER', {})
        trader_exchange = auto_trader.get('exchange', 'not_configured')
        
        print(f"📋 配置检查:")
        print(f"   默认交易所: {default_exchange}")
        print(f"   自动交易交易所: {trader_exchange}")
        
        # 2. 检查exchange_client
        from quant.exchange_client import exchange_client
        current_exchange = exchange_client.get_exchange_name()
        
        print(f"🔄 Exchange Client:")
        print(f"   当前交易所: {current_exchange}")
        
        # 3. 验证OKX配置
        okx_config = exchanges.get('okx', {})
        okx_symbols = okx_config.get('symbols', {})
        btc_config = okx_symbols.get('BTCUSDT', {})
        
        print(f"🪙 OKX BTCUSDT配置:")
        print(f"   启用状态: {btc_config.get('enabled', False)}")
        print(f"   合约ID: {btc_config.get('inst_id', 'not_set')}")
        print(f"   交易模式: {btc_config.get('trade_mode', 'not_set')}")
        print(f"   杠杆: {btc_config.get('leverage', 'not_set')}")
        
        # 4. 检查环境变量
        okx_key = os.getenv('OKX_ACCESS_KEY')
        okx_secret = os.getenv('OKX_SECRET_KEY') 
        okx_passphrase = os.getenv('OKX_PASSPHRASE')
        
        print(f"🔐 OKX API凭证:")
        print(f"   Access Key: {'已设置' if okx_key else '未设置'}")
        print(f"   Secret Key: {'已设置' if okx_secret else '未设置'}")
        print(f"   Passphrase: {'已设置' if okx_passphrase else '未设置'}")
        
        # 5. 综合判断
        all_good = (
            default_exchange == 'okx' and
            trader_exchange == 'okx' and 
            current_exchange == 'okx' and
            btc_config.get('enabled', False) and
            okx_key and okx_secret and okx_passphrase
        )
        
        if all_good:
            print("\n✅ OKX集成配置完整，系统准备就绪！")
        else:
            print("\n⚠️ OKX集成存在问题：")
            issues = []
            if default_exchange != 'okx':
                issues.append(f"默认交易所不是OKX: {default_exchange}")
            if trader_exchange != 'okx':
                issues.append(f"自动交易交易所不是OKX: {trader_exchange}")
            if current_exchange != 'okx':
                issues.append(f"当前客户端不是OKX: {current_exchange}")
            if not btc_config.get('enabled', False):
                issues.append("BTCUSDT交易对未启用")
            if not (okx_key and okx_secret and okx_passphrase):
                issues.append("OKX API凭证不完整")
            
            for issue in issues:
                print(f"   - {issue}")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_okx_integration()
    if success:
        print("\n🚀 可以开始运行OKX交易系统！")
    else:
        print("\n🔧 请先完成OKX配置后再运行系统")