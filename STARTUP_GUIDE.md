# OKX交易系统启动脚本使用指南

## 🚀 可用的启动脚本

### 1. `./start.sh` - 完整安全启动脚本（推荐）
**功能最全面，安全性最高的启动方式**

特点：
- ✅ 完整的安全检查（.env文件权限、API密钥验证等）
- ✅ 系统完整性验证（依赖检查、配置验证、API连接测试）
- ✅ 详细的启动日志和状态报告
- ✅ 错误处理和故障排除提示
- ✅ 符合OKX API集成规范

使用方法：
```bash
# 使用默认配置启动
./start.sh

# 指定配置文件启动
./start.sh config/config.development.json
```

### 2. `./quick_start.sh` - 快速启动脚本
**适合日常快速启动使用**

特点：
- ⚡ 快速检查和启动
- ✅ 基本的安全验证
- 🎯 适合熟悉系统的用户

使用方法：
```bash
./quick_start.sh
```

### 3. `python start_okx_trading_system.py` - Python安全启动
**纯Python实现的安全启动方式**

特点：
- 🔒 完整的安全验证
- 📊 系统状态检查
- 🐍 纯Python实现，跨平台兼容

使用方法：
```bash
python start_okx_trading_system.py
```

## 📋 启动前的准备工作

### 1. 环境配置
确保`.env`文件包含正确的OKX API配置：
```env
OKX_ACCESS_KEY=your_access_key
OKX_SECRET_KEY=your_secret_key
OKX_PASSPHRASE=your_passphrase
ENVIRONMENT=development
```

### 2. 文件权限
```bash
chmod 600 .env          # 设置.env文件安全权限
chmod +x start.sh       # 启动脚本执行权限
chmod +x quick_start.sh # 快速启动脚本执行权限
```

### 3. 虚拟环境（可选但推荐）
```bash
# 如果存在虚拟环境，脚本会自动激活
ls trading_system_venv/bin/activate
```

## 🔧 启动脚本功能对比

| 功能 | start.sh | quick_start.sh | start_okx_trading_system.py |
|------|----------|----------------|------------------------------|
| API密钥验证 | ✅ | ✅ | ✅ |
| 文件权限检查 | ✅ | ❌ | ✅ |
| 依赖检查 | ✅ | ❌ | ❌ |
| 系统验证 | ✅ | ❌ | ✅ |
| 配置验证 | ✅ | ❌ | ✅ |
| API连接测试 | ✅ | ❌ | ✅ |
| 详细日志 | ✅ | ❌ | ✅ |
| 启动速度 | 慢 | 快 | 中等 |
| 适用场景 | 生产环境 | 开发调试 | 验证测试 |

## 📊 启动流程说明

### start.sh 完整流程：
1. 🔐 环境变量和文件权限检查
2. 🔑 OKX API密钥验证
3. 🐍 Python环境和依赖检查
4. ⚙️ 配置文件验证
5. 🔍 系统完整性验证
6. 📡 OKX API连接测试
7. 🚀 启动交易系统

### quick_start.sh 快速流程：
1. 🔐 基本环境检查
2. 🔑 OKX API密钥验证
3. 🚀 直接启动系统

## 🛠️ 故障排除

### 常见问题及解决方案：

#### 1. 权限错误
```bash
# 问题：Permission denied
# 解决：
chmod +x start.sh
chmod 600 .env
```

#### 2. API密钥错误
```bash
# 问题：OKX API密钥配置不完整
# 解决：检查.env文件配置
cat .env | grep OKX
```

#### 3. 依赖缺失
```bash
# 问题：缺少Python依赖
# 解决：
pip install -r requirements.txt
```

#### 4. 配置文件错误
```bash
# 问题：配置文件JSON格式错误
# 解决：验证JSON格式
python -c "import json; json.load(open('config/config.json'))"
```

## 🔍 验证系统状态

运行系统验证脚本：
```bash
python verify_okx_system.py
```

检查系统日志：
```bash
tail -f logs/error.log
```

查看实时状态：
```bash
# 系统运行后，查看进程
ps aux | grep python
```

## 🎯 最佳实践

### 生产环境启动：
```bash
# 1. 完整验证启动
./start.sh

# 2. 后台运行（可选）
nohup ./start.sh > system.log 2>&1 &
```

### 开发环境启动：
```bash
# 快速启动进行测试
./quick_start.sh
```

### 系统监控：
```bash
# 定期运行验证
python verify_okx_system.py

# 检查系统性能
python -c "
from quant.system_monitor import system_monitor
print(system_monitor.get_system_status())
"
```

## 🔒 安全提醒

1. **绝不在脚本中硬编码API密钥**
2. **确保.env文件权限为600**
3. **定期更新API密钥**
4. **监控系统运行状态**
5. **备份重要配置文件**

---

**选择合适的启动方式，确保OKX交易系统安全稳定运行！**