#!/bin/bash
# 修复部署常见问题脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

echo ""
echo -e "${BLUE}=====================================================${NC}"
echo -e "${BLUE}🔧 修复部署常见问题${NC}"
echo -e "${BLUE}=====================================================${NC}"
echo ""

# 1. 检查和修复虚拟环境
info "1. 检查虚拟环境状态..."
if [ ! -d "trading_system_venv" ]; then
    warn "虚拟环境不存在，正在创建..."
    python3 -m venv trading_system_venv
    log "虚拟环境创建成功"
else
    log "虚拟环境存在"
fi

# 激活虚拟环境
source trading_system_venv/bin/activate

# 2. 更新pip和安装/更新依赖
info "2. 更新依赖包..."
pip install --upgrade pip

if [ -f "requirements.txt" ]; then
    log "安装requirements.txt中的依赖..."
    pip install -r requirements.txt
else
    warn "requirements.txt不存在，安装基本依赖..."
    pip install pandas numpy scipy scikit-learn python-binance APScheduler SQLAlchemy requests python-dotenv psutil
fi

# 安装测试和开发工具依赖
pip install pytest-asyncio psutil black ruff

log "依赖包安装完成"

# 3. 检查和修复Python路径问题
info "3. 修复Python模块路径问题..."

# 验证quant模块可以导入
python -c "
import sys
import os
sys.path.insert(0, os.getcwd())
try:
    import quant
    print('✅ quant模块导入成功')
except ImportError as e:
    print(f'❌ quant模块导入失败: {e}')
    sys.exit(1)
"

# 4. 检查和修复配置文件
info "4. 检查配置文件..."

CONFIG_FILES=("config/config.json" "config/config.development.json" "config/config.production.json")
for config in "${CONFIG_FILES[@]}"; do
    if [ -f "$config" ]; then
        log "检查配置文件: $config"
        if python -m json.tool "$config" > /dev/null 2>&1; then
            log "  JSON语法有效"
        else
            warn "  JSON语法错误，尝试修复..."
            # 备份原文件
            cp "$config" "$config.backup.$(date +%Y%m%d_%H%M%S)"
            warn "  原文件已备份为: $config.backup.$(date +%Y%m%d_%H%M%S)"
            warn "  请手动检查和修复配置文件: $config"
        fi
    else
        if [ "$config" == "config/config.json" ]; then
            error "主配置文件 $config 不存在"
            exit 1
        else
            info "创建默认配置文件: $config"
            cp config/config.json "$config" 2>/dev/null || warn "无法创建 $config"
        fi
    fi
done

# 5. 检查和创建必要目录
info "5. 创建必要的目录结构..."
mkdir -p logs data exports backups

# 6. 检查和修复数据库
info "6. 检查数据库状态..."
if [ -f "scripts/init_database.py" ]; then
    log "测试数据库初始化脚本..."
    python scripts/init_database.py --verbose || warn "数据库初始化失败"
else
    warn "数据库初始化脚本不存在"
fi

# 7. 检查权限
info "7. 设置脚本执行权限..."
chmod +x scripts/*.sh
log "脚本权限设置完成"

# 8. 验证核心功能
info "8. 验证系统核心功能..."
python -c "
import sys
import os
sys.path.insert(0, os.getcwd())

try:
    # 测试配置管理器
    from quant.config_manager import ConfigManager
    config = ConfigManager('config/config.json')
    print('✅ 配置管理器正常')
    
    # 测试服务编排器  
    from quant.trading_system_orchestrator import TradingSystemOrchestrator
    orchestrator = TradingSystemOrchestrator(config)
    print('✅ 服务编排器正常')
    
    # 测试服务容器
    status = orchestrator.service_container.get_container_status()
    print(f'✅ 服务容器正常 - {status[\"enabled_services\"]} 个服务已配置')
    
    print('🎉 所有核心组件验证成功')
    
except Exception as e:
    print(f'❌ 核心功能验证失败: {e}')
    import traceback
    traceback.print_exc()
    sys.exit(1)
"

# 9. 清理临时文件
info "9. 清理临时文件..."
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
log "临时文件清理完成"

# 10. 运行基础测试
info "10. 运行基础功能测试..."
timeout 10s python main_refactored.py config/config.development.json &
SYSTEM_PID=$!

sleep 5

if kill -0 $SYSTEM_PID 2>/dev/null; then
    log "✅ 系统启动测试成功"
    kill $SYSTEM_PID 2>/dev/null || true
    wait $SYSTEM_PID 2>/dev/null || true
else
    warn "⚠️  系统启动测试超时或失败"
fi

echo ""
echo -e "${BLUE}=====================================================${NC}"
echo -e "${BLUE}📊 修复结果总结${NC}"
echo -e "${BLUE}=====================================================${NC}"

log "✅ 虚拟环境: 正常"
log "✅ 依赖包: 已安装"  
log "✅ Python路径: 已修复"
log "✅ 配置文件: 已检查"
log "✅ 目录结构: 已创建"
log "✅ 脚本权限: 已设置"
log "✅ 核心功能: 已验证"

echo ""
log "🎉 部署问题修复完成！"
echo ""
log "现在您可以尝试以下操作："
echo "  1. 启动系统: ./scripts/start_system.sh config/config.development.json"
echo "  2. 健康检查: ./scripts/health_check.sh"
echo "  3. 运行部署: ./scripts/deploy_refactored.sh development"
echo ""