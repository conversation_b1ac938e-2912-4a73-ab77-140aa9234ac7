#!/bin/bash
# 系统健康检查脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

echo ""
echo -e "${BLUE}=====================================================${NC}"
echo -e "${BLUE}🏥 重构交易系统健康检查${NC}"
echo -e "${BLUE}=====================================================${NC}"
echo ""

# 1. 检查系统进程
info "1. 检查系统进程状态..."
PIDS=$(pgrep -f "main_refactored.py" || true)
if [ ! -z "$PIDS" ]; then
    log "✅ 发现运行中的进程: $PIDS"
    ps aux | grep main_refactored | grep -v grep | while read line; do
        echo "   $line"
    done
else
    warn "⚠️  未发现直接运行的进程"
fi

# 2. 检查系统服务 (macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    info "2. 检查macOS后台服务..."
    if launchctl list | grep -q "com.trading.system.refactored"; then
        SERVICE_INFO=$(launchctl list | grep "com.trading.system.refactored")
        log "✅ 发现后台服务: $SERVICE_INFO"
    else
        warn "⚠️  未发现macOS后台服务"
    fi
fi

# 3. 检查虚拟环境
info "3. 检查虚拟环境..."
if [ -d "trading_system_venv" ]; then
    log "✅ 虚拟环境存在"
    PYTHON_VERSION=$(trading_system_venv/bin/python --version 2>&1)
    echo "   Python版本: $PYTHON_VERSION"
else
    error "❌ 虚拟环境不存在"
fi

# 4. 检查配置文件
info "4. 检查配置文件..."
CONFIG_FILES=("config/config.json" "config/config.development.json" "config/config.production.json")
for config in "${CONFIG_FILES[@]}"; do
    if [ -f "$config" ]; then
        log "✅ 配置文件存在: $config"
        # 验证JSON语法
        if python -m json.tool "$config" > /dev/null 2>&1; then
            echo "   JSON语法: ✅ 有效"
        else
            warn "   JSON语法: ⚠️  可能有错误"
        fi
    else
        warn "⚠️  配置文件不存在: $config"
    fi
done

# 5. 检查环境变量
info "5. 检查环境变量..."
if [ -f ".env" ]; then
    log "✅ .env文件存在"
    source .env 2>/dev/null || true
    
    if [ ! -z "$BINANCE_ACCESS_KEY" ]; then
        echo "   Binance API Key: ✅ 已配置"
    else
        warn "   Binance API Key: ⚠️  未配置"
    fi
    
    if [ ! -z "$ENVIRONMENT" ]; then
        echo "   环境变量 ENVIRONMENT: $ENVIRONMENT"
    else
        info "   环境变量 ENVIRONMENT: 未设置（将使用默认值）"
    fi
else
    warn "⚠️  .env文件不存在"
fi

# 6. 检查数据库文件
info "6. 检查数据库文件..."
DB_FILES=("data/trading_system.db" "data/timeseries.db")
for db in "${DB_FILES[@]}"; do
    if [ -f "$db" ]; then
        log "✅ 数据库文件存在: $db"
        DB_SIZE=$(du -h "$db" | cut -f1)
        echo "   文件大小: $DB_SIZE"
    else
        warn "⚠️  数据库文件不存在: $db"
    fi
done

# 7. 检查日志目录
info "7. 检查日志目录..."
if [ -d "logs" ]; then
    log "✅ 日志目录存在"
    LOG_COUNT=$(find logs -name "*.log" | wc -l)
    echo "   日志文件数量: $LOG_COUNT"
    
    # 检查最新日志
    LATEST_LOG=$(find logs -name "*.log" -type f -exec ls -t {} + | head -1 2>/dev/null || true)
    if [ ! -z "$LATEST_LOG" ]; then
        LATEST_TIME=$(stat -f "%m" "$LATEST_LOG" 2>/dev/null || stat -c "%Y" "$LATEST_LOG" 2>/dev/null || echo "0")
        CURRENT_TIME=$(date +%s)
        TIME_DIFF=$((CURRENT_TIME - LATEST_TIME))
        
        if [ $TIME_DIFF -lt 3600 ]; then  # 1小时内
            log "   最新日志更新: ✅ 1小时内"
        else
            warn "   最新日志更新: ⚠️  超过1小时前"
        fi
    fi
else
    warn "⚠️  日志目录不存在"
fi

# 8. 检查系统依赖
info "8. 检查Python依赖..."
if [ -d "trading_system_venv" ]; then
    source trading_system_venv/bin/activate
    
    REQUIRED_MODULES=("pandas" "numpy" "scipy" "sklearn" "binance" "sqlalchemy" "apscheduler")
    MISSING_MODULES=()
    
    for module in "${REQUIRED_MODULES[@]}"; do
        if python -c "import $module" 2>/dev/null; then
            echo "   ✅ $module"
        else
            echo "   ❌ $module (缺失)"
            MISSING_MODULES+=("$module")
        fi
    done
    
    if [ ${#MISSING_MODULES[@]} -eq 0 ]; then
        log "✅ 所有必需依赖都已安装"
    else
        error "❌ 缺少依赖: ${MISSING_MODULES[*]}"
        echo "   请运行: pip install -r requirements.txt"
    fi
else
    warn "⚠️  无法检查依赖（虚拟环境不存在）"
fi

# 9. 系统资源检查
info "9. 检查系统资源..."
if command -v python3 &> /dev/null; then
    python3 -c "
import psutil
import os

# CPU使用率
cpu_percent = psutil.cpu_percent(interval=1)
print(f'   CPU使用率: {cpu_percent:.1f}%')

# 内存使用情况
memory = psutil.virtual_memory()
print(f'   内存使用率: {memory.percent:.1f}% ({memory.used//1024//1024}MB / {memory.total//1024//1024}MB)')

# 磁盘空间
disk = psutil.disk_usage('.')
print(f'   磁盘使用率: {disk.percent:.1f}% ({disk.free//1024//1024//1024}GB 可用)')

# 负载状态评估
if cpu_percent > 80:
    print('   ⚠️  CPU使用率过高')
elif cpu_percent > 50:
    print('   ⚡ CPU使用率中等')
else:
    print('   ✅ CPU使用率正常')
    
if memory.percent > 80:
    print('   ⚠️  内存使用率过高')
elif memory.percent > 50:
    print('   ⚡ 内存使用率中等')  
else:
    print('   ✅ 内存使用率正常')
"
fi

# 10. 网络连接检查
info "10. 检查网络连接..."
if curl -s --max-time 5 https://api.binance.com/api/v3/ping > /dev/null 2>&1; then
    log "✅ Binance API连接正常"
else
    warn "⚠️  Binance API连接异常"
fi

# 汇总健康状态
echo ""
echo -e "${BLUE}=====================================================${NC}"
echo -e "${BLUE}📊 健康检查汇总${NC}"  
echo -e "${BLUE}=====================================================${NC}"

# 计算健康分数
TOTAL_CHECKS=10
PASSED_CHECKS=0

# 简单的状态评估逻辑
if [ ! -z "$PIDS" ] || launchctl list | grep -q "com.trading.system.refactored" 2>/dev/null; then
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    echo "✅ 进程状态: 正常"
else
    echo "❌ 进程状态: 未运行"
fi

if [ -d "trading_system_venv" ]; then
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    echo "✅ 环境状态: 正常"
else
    echo "❌ 环境状态: 异常"
fi

if [ -f "config/config.json" ]; then
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    echo "✅ 配置状态: 正常"
else
    echo "❌ 配置状态: 异常"
fi

HEALTH_SCORE=$((PASSED_CHECKS * 100 / 3))  # 基于关键检查项

echo ""
if [ $HEALTH_SCORE -ge 80 ]; then
    log "🎉 系统健康状态: 良好 ($HEALTH_SCORE%)"
elif [ $HEALTH_SCORE -ge 60 ]; then
    warn "⚠️  系统健康状态: 一般 ($HEALTH_SCORE%)"
else
    error "❌ 系统健康状态: 异常 ($HEALTH_SCORE%)"
fi

echo ""
log "健康检查完成"