#!/bin/bash
# 重启重构交易系统脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

CONFIG_FILE=${1:-"config/config.development.json"}

echo ""
echo -e "${BLUE}=====================================================${NC}"
echo -e "${BLUE}🔄 重启重构交易系统${NC}"
echo -e "${BLUE}=====================================================${NC}"
echo ""

log "开始重启序列..."
log "使用配置文件: $CONFIG_FILE"

# 步骤1: 停止系统
info "步骤1: 停止当前运行的系统..."
./scripts/stop_system.sh

# 步骤2: 等待完全停止
info "步骤2: 等待系统完全停止..."
sleep 5

# 步骤3: 验证系统状态
info "步骤3: 验证系统状态..."
if pgrep -f "main_refactored.py" > /dev/null; then
    warn "系统可能未完全停止，强制清理..."
    pkill -9 -f "main_refactored.py" || true
    sleep 2
fi

# 步骤4: 可选的配置验证
if [[ "$2" == "--verify-config" ]]; then
    info "步骤4: 验证配置文件..."
    source trading_system_venv/bin/activate
    python -c "
from quant.config_manager import ConfigManager
try:
    config = ConfigManager('$CONFIG_FILE')
    print('✅ 配置文件验证成功')
except Exception as e:
    print(f'❌ 配置文件验证失败: {e}')
    import sys
    sys.exit(1)
    "
fi

# 步骤5: 启动系统
info "步骤5: 启动系统..."
echo ""

# 如果是后台模式
if [[ "$2" == "--background" || "$3" == "--background" ]]; then
    log "后台模式启动..."
    nohup ./scripts/start_system.sh "$CONFIG_FILE" > logs/nohup.out 2>&1 &
    
    # 等待启动
    sleep 5
    
    # 检查是否成功启动
    if pgrep -f "main_refactored.py" > /dev/null; then
        log "✅ 系统已成功在后台启动"
        log "查看日志: tail -f logs/nohup.out"
        log "查看进程: ps aux | grep main_refactored"
    else
        warn "❌ 后台启动可能失败，请检查 logs/nohup.out"
    fi
else
    log "前台模式启动..."
    ./scripts/start_system.sh "$CONFIG_FILE"
fi