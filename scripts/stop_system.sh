#!/bin/bash
# 停止重构交易系统脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# 检测操作系统
if [[ "$OSTYPE" == "darwin"* ]]; then
    ENVIRONMENT_OS="macos"
elif [[ -f /etc/debian_version ]] || [[ -f /etc/lsb-release ]]; then
    ENVIRONMENT_OS="linux"
else
    ENVIRONMENT_OS="unknown"
fi

log "停止重构交易系统..."

# 方法1: 停止直接运行的进程
log "查找运行中的交易系统进程..."
PIDS=$(pgrep -f "main_refactored.py" || true)

if [ ! -z "$PIDS" ]; then
    log "发现运行中的进程: $PIDS"
    
    # 优雅停止 (SIGTERM)
    log "发送优雅停止信号..."
    kill -TERM $PIDS 2>/dev/null || true
    
    # 等待进程停止
    sleep 3
    
    # 检查进程是否还在运行
    REMAINING_PIDS=$(pgrep -f "main_refactored.py" || true)
    if [ ! -z "$REMAINING_PIDS" ]; then
        warn "进程仍在运行，强制停止..."
        kill -KILL $REMAINING_PIDS 2>/dev/null || true
        sleep 1
    fi
    
    log "进程已停止"
else
    log "未找到直接运行的进程"
fi

# 方法2: 停止系统服务
if [[ "$ENVIRONMENT_OS" == "macos" ]]; then
    log "检查macOS后台服务..."
    
    if launchctl list | grep -q "com.trading.system.refactored"; then
        log "停止macOS后台服务..."
        launchctl stop com.trading.system.refactored 2>/dev/null || true
        log "macOS服务已停止"
    else
        log "未发现macOS后台服务"
    fi
    
elif [[ "$ENVIRONMENT_OS" == "linux" ]]; then
    log "检查Linux系统服务..."
    
    if systemctl is-active --quiet trading-system-refactored 2>/dev/null; then
        log "停止Linux系统服务..."
        sudo systemctl stop trading-system-refactored
        log "Linux服务已停止"
    else
        log "未发现活跃的Linux系统服务"
    fi
fi

# 验证停止状态
log "验证系统停止状态..."
sleep 2

FINAL_CHECK=$(pgrep -f "main_refactored.py" || true)
if [ -z "$FINAL_CHECK" ]; then
    log "✅ 系统已完全停止"
else
    warn "⚠️  仍有进程在运行: $FINAL_CHECK"
    warn "如需强制停止，请运行: kill -9 $FINAL_CHECK"
fi

# 显示系统状态
echo ""
log "📊 最终状态报告:"
echo "  进程状态: $(if [ -z "$FINAL_CHECK" ]; then echo "✅ 已停止"; else echo "⚠️ 仍在运行"; fi)"

if [[ "$ENVIRONMENT_OS" == "macos" ]]; then
    SERVICE_STATUS=$(launchctl list | grep "com.trading.system.refactored" || echo "未运行")
    echo "  macOS服务: $SERVICE_STATUS"
elif [[ "$ENVIRONMENT_OS" == "linux" ]]; then
    SERVICE_STATUS=$(systemctl is-active trading-system-refactored 2>/dev/null || echo "inactive")
    echo "  Linux服务: $SERVICE_STATUS"
fi

echo ""
log "系统停止完成"