#!/usr/bin/env python3
"""
Database Initialization Script

Creates and initializes the SQLite database with required tables and indexes.
"""

import sys
import argparse
from pathlib import Path

# Add the project root to Python path  
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from quant.database_manager import db
from quant.utils.logger import get_logger

logger = get_logger(__name__)


def main():
    """Main database initialization function."""
    parser = argparse.ArgumentParser(description='Initialize trading system database')
    parser.add_argument('--force', action='store_true', help='Force recreate database')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    try:
        logger.info("Starting database initialization...")
        
        # Check if database already exists
        db_path = Path("data/trading_system.db")
        if db_path.exists() and not args.force:
            logger.warning("Database already exists. Use --force to recreate.")
            return 1
        
        # Remove existing database if force flag is set
        if db_path.exists() and args.force:
            logger.info("Removing existing database...")
            db_path.unlink()
        
        # Initialize database
        db.init_database()
        
        logger.info("Database initialized successfully!")
        logger.info(f"Database file: {db_path.absolute()}")
        
        # Print schema information
        if args.verbose:
            print("\nDatabase Schema:")
            print("-" * 50)
            print("Table: trade_history")
            print("Columns:")
            print("  - id: Integer (Primary Key)")
            print("  - signal_timestamp: DateTime")
            print("  - symbol: String(20)")
            print("  - direction: String(10)")
            print("  - entry_price: Float")
            print("  - confidence_score: Float")
            print("  - market_state: String(50)")
            print("  - trigger_pattern: String(100)")
            print("  - confirmed_indicators: Text")
            print("  - suggested_bet: Float")
            print("  - status: String(20)")
            print("  - exit_price: Float (nullable)")
            print("  - exit_timestamp: DateTime (nullable)")
            print("  - pnl: Float (nullable)")
            print("  - decision_details: Text")
            print("  - created_at: DateTime")
            print("\nIndexes:")
            print("  - idx_status_timestamp: Composite index on (status, signal_timestamp)")
            print("  - ix_trade_history_signal_timestamp: Index on signal_timestamp")
            print("  - ix_trade_history_symbol: Index on symbol")
            print("  - ix_trade_history_status: Index on status")
        
        return 0
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())