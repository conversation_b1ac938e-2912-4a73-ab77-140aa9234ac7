"""
Database Migration Script for Confidence Scoring System

This script adds the necessary database schema extensions to support
the intelligent confidence scoring system.
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quant.utils.logger import get_logger

logger = get_logger(__name__)


class ConfidenceScoringMigration:
    """Handles database migration for confidence scoring system."""
    
    def __init__(self, db_path: str = "data/trading_system.db"):
        self.db_path = Path(db_path)
        self.logger = logger
    
    def run_migration(self) -> bool:
        """
        Run the complete migration for confidence scoring system.
        
        Returns:
            bool: True if migration successful, False otherwise
        """
        try:
            self.logger.info("Starting confidence scoring database migration...")
            
            # Check if database exists
            if not self.db_path.exists():
                self.logger.warning(f"Database file {self.db_path} does not exist. Creating new database.")
                return self._create_new_database()
            
            # Connect to existing database
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Check if migration already applied
            if self._is_migration_applied(cursor):
                self.logger.info("Confidence scoring migration already applied.")
                conn.close()
                return True
            
            # Begin transaction
            cursor.execute("BEGIN TRANSACTION")
            
            try:
                # Apply migration steps
                self._add_confidence_breakdown_columns(cursor)
                self._create_confidence_scoring_history_table(cursor)
                self._create_performance_indexes(cursor)
                self._mark_migration_as_applied(cursor)
                
                # Commit transaction
                conn.commit()
                self.logger.info("Confidence scoring migration completed successfully.")
                return True
                
            except Exception as e:
                # Rollback on error
                conn.rollback()
                self.logger.error(f"Migration failed: {e}")
                return False
                
            finally:
                conn.close()
                
        except Exception as e:
            self.logger.error(f"Error during migration: {e}")
            return False
    
    def _is_migration_applied(self, cursor: sqlite3.Cursor) -> bool:
        """Check if the migration has already been applied."""
        try:
            # Check if confidence_breakdown column exists
            cursor.execute("PRAGMA table_info(trade_history)")
            columns = [column[1] for column in cursor.fetchall()]
            
            required_columns = [
                'confidence_breakdown',
                'indicator_scores',
                'market_regime_score',
                'trend_strength_score',
                'momentum_score',
                'volatility_score',
                'volume_score'
            ]
            
            return all(col in columns for col in required_columns)
            
        except Exception as e:
            self.logger.error(f"Error checking migration status: {e}")
            return False
    
    def _add_confidence_breakdown_columns(self, cursor: sqlite3.Cursor):
        """Add confidence breakdown columns to trade_history table."""
        self.logger.info("Adding confidence breakdown columns to trade_history table...")
        
        columns_to_add = [
            ("confidence_breakdown", "TEXT"),
            ("indicator_scores", "TEXT"),
            ("market_regime_score", "FLOAT"),
            ("trend_strength_score", "FLOAT"),
            ("momentum_score", "FLOAT"),
            ("volatility_score", "FLOAT"),
            ("volume_score", "FLOAT"),
            ("calculation_time_ms", "INTEGER"),
            ("signal_strength", "INTEGER")
        ]
        
        for column_name, column_type in columns_to_add:
            try:
                cursor.execute(f"ALTER TABLE trade_history ADD COLUMN {column_name} {column_type}")
                self.logger.debug(f"Added column: {column_name}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    self.logger.debug(f"Column {column_name} already exists")
                else:
                    raise e
    
    def _create_confidence_scoring_history_table(self, cursor: sqlite3.Cursor):
        """Create confidence scoring history table."""
        self.logger.info("Creating confidence_scoring_history table...")
        
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS confidence_scoring_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            trade_id INTEGER,
            timestamp DATETIME NOT NULL,
            overall_confidence FLOAT NOT NULL,
            trend_score FLOAT NOT NULL,
            momentum_score FLOAT NOT NULL,
            volatility_score FLOAT NOT NULL,
            volume_score FLOAT NOT NULL,
            market_regime_score FLOAT NOT NULL,
            indicator_weights TEXT,
            calculation_details TEXT,
            calculation_time_ms INTEGER,
            market_regime TEXT,
            signal_strength INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (trade_id) REFERENCES trade_history(id)
        )
        """
        
        cursor.execute(create_table_sql)
        
        # Create indexes
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_confidence_history_trade_id ON confidence_scoring_history(trade_id)",
            "CREATE INDEX IF NOT EXISTS idx_confidence_history_timestamp ON confidence_scoring_history(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_confidence_history_overall ON confidence_scoring_history(overall_confidence)",
            "CREATE INDEX IF NOT EXISTS idx_confidence_history_regime ON confidence_scoring_history(market_regime)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _create_performance_indexes(self, cursor: sqlite3.Cursor):
        """Create performance optimization indexes."""
        self.logger.info("Creating performance indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_trade_history_confidence ON trade_history(confidence_score)",
            "CREATE INDEX IF NOT EXISTS idx_trade_history_signal_strength ON trade_history(signal_strength)",
            "CREATE INDEX IF NOT EXISTS idx_trade_history_market_regime ON trade_history(market_regime_score)",
            "CREATE INDEX IF NOT EXISTS idx_trade_history_trend_score ON trade_history(trend_strength_score)",
            "CREATE INDEX IF NOT EXISTS idx_trade_history_momentum_score ON trade_history(momentum_score)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _mark_migration_as_applied(self, cursor: sqlite3.Cursor):
        """Mark the migration as applied in the database."""
        self.logger.info("Marking migration as applied...")
        
        # Create migration tracking table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS schema_migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            migration_name TEXT NOT NULL,
            applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
        """)
        
        # Insert migration record
        cursor.execute("""
        INSERT INTO schema_migrations (migration_name, description)
        VALUES (?, ?)
        """, (
            "confidence_scoring_system",
            "Added confidence scoring breakdown columns and history table"
        ))
    
    def _create_new_database(self) -> bool:
        """Create a new database with the confidence scoring schema."""
        try:
            self.logger.info(f"Creating new database at {self.db_path}")
            
            # Connect to create the database
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Create tables with confidence scoring support
            self._create_trade_history_table(cursor)
            self._create_confidence_scoring_history_table(cursor)
            self._mark_migration_as_applied(cursor)
            
            conn.commit()
            conn.close()
            
            self.logger.info("New database created successfully with confidence scoring support.")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating new database: {e}")
            return False
    
    def _create_trade_history_table(self, cursor: sqlite3.Cursor):
        """Create trade_history table with confidence scoring columns."""
        self.logger.info("Creating trade_history table with confidence scoring support...")
        
        create_table_sql = """
        CREATE TABLE trade_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            signal_timestamp DATETIME NOT NULL,
            symbol TEXT(20) NOT NULL,
            direction TEXT(10) NOT NULL,
            entry_price FLOAT NOT NULL,
            confidence_score FLOAT NOT NULL,
            market_state TEXT(50) NOT NULL,
            trigger_pattern TEXT(100) NOT NULL,
            confirmed_indicators TEXT NOT NULL,
            suggested_bet FLOAT NOT NULL,
            status TEXT(20) NOT NULL,
            exit_price FLOAT,
            exit_timestamp DATETIME,
            pnl FLOAT,
            decision_details TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            
            -- Confidence scoring breakdown columns
            confidence_breakdown TEXT,
            indicator_scores TEXT,
            market_regime_score FLOAT,
            trend_strength_score FLOAT,
            momentum_score FLOAT,
            volatility_score FLOAT,
            volume_score FLOAT,
            calculation_time_ms INTEGER,
            signal_strength INTEGER
        )
        """
        
        cursor.execute(create_table_sql)
        
        # Create indexes
        indexes = [
            "CREATE INDEX idx_trade_history_symbol ON trade_history(symbol)",
            "CREATE INDEX idx_trade_history_status ON trade_history(status)",
            "CREATE INDEX idx_trade_history_timestamp ON trade_history(signal_timestamp)",
            "CREATE INDEX idx_trade_history_confidence ON trade_history(confidence_score)",
            "CREATE INDEX idx_trade_history_signal_strength ON trade_history(signal_strength)",
            "CREATE INDEX idx_status_timestamp ON trade_history(status, signal_timestamp)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get the current migration status."""
        try:
            if not self.db_path.exists():
                return {"status": "no_database", "message": "Database file does not exist"}
            
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Check if migration table exists
            cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='schema_migrations'
            """)
            migration_table_exists = cursor.fetchone() is not None
            
            if migration_table_exists:
                # Check for confidence scoring migration
                cursor.execute("""
                SELECT migration_name, applied_at, description 
                FROM schema_migrations 
                WHERE migration_name = 'confidence_scoring_system'
                """)
                migration_record = cursor.fetchone()
                
                if migration_record:
                    return {
                        "status": "migrated",
                        "migration_name": migration_record[0],
                        "applied_at": migration_record[1],
                        "description": migration_record[2]
                    }
                else:
                    return {"status": "pending", "message": "Confidence scoring migration not applied"}
            else:
                # Check for confidence columns in trade_history
                cursor.execute("PRAGMA table_info(trade_history)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'confidence_breakdown' in columns:
                    return {"status": "partial", "message": "Confidence columns exist but migration not tracked"}
                else:
                    return {"status": "pending", "message": "Database exists but confidence scoring not implemented"}
            
        except Exception as e:
            return {"status": "error", "message": f"Error checking migration status: {e}"}
        finally:
            if 'conn' in locals():
                conn.close()


def main():
    """Main function to run the migration."""
    migration = ConfidenceScoringMigration()
    
    print("Confidence Scoring Database Migration")
    print("=" * 50)
    
    # Check current status
    status = migration.get_migration_status()
    print(f"Current status: {status['status']}")
    if 'message' in status:
        print(f"Details: {status['message']}")
    print()
    
    if status['status'] in ['pending', 'partial', 'no_database']:
        # Run migration
        print("Running migration...")
        success = migration.run_migration()
        
        if success:
            print("✅ Migration completed successfully!")
        else:
            print("❌ Migration failed!")
            return 1
    else:
        print("✅ Database is already up to date!")
    
    return 0


if __name__ == "__main__":
    exit(main())