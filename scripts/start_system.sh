#!/bin/bash
# 启动重构交易系统脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# 检查虚拟环境
if [ ! -d "trading_system_venv" ]; then
    error "虚拟环境不存在，请先运行部署脚本: ./scripts/deploy_refactored.sh development"
fi

# 检查配置文件
CONFIG_FILE=${1:-"config/config.development.json"}
if [ ! -f "$CONFIG_FILE" ]; then
    warn "配置文件 $CONFIG_FILE 不存在，使用默认配置 config/config.json"
    CONFIG_FILE="config/config.json"
fi

if [ ! -f "$CONFIG_FILE" ]; then
    error "配置文件 $CONFIG_FILE 不存在"
fi

# 显示启动信息
log "启动重构交易系统..."
log "配置文件: $CONFIG_FILE"
log "项目路径: $PROJECT_ROOT"

# 激活虚拟环境并启动
source trading_system_venv/bin/activate

# 检查依赖
log "检查系统依赖..."
python -c "
import sys
required_modules = ['pandas', 'numpy', 'scipy', 'sklearn', 'quant']
missing_modules = []

for module in required_modules:
    try:
        __import__(module)
    except ImportError:
        missing_modules.append(module)

if missing_modules:
    print(f'❌ 缺少依赖: {missing_modules}')
    print('请运行: pip install -r requirements.txt')
    sys.exit(1)
else:
    print('✅ 所有依赖正常')
"

# 检查系统配置
log "验证系统配置..."
python -c "
from quant.config_manager import ConfigManager
from quant.trading_system_orchestrator import TradingSystemOrchestrator

try:
    config = ConfigManager('$CONFIG_FILE')
    orchestrator = TradingSystemOrchestrator(config)
    status = orchestrator.service_container.get_container_status()
    print(f'✅ 配置验证成功 - {status[\"enabled_services\"]} 个服务已配置')
except Exception as e:
    print(f'❌ 配置验证失败: {e}')
    import sys
    sys.exit(1)
"

# 创建日志目录
mkdir -p logs

# 显示启动提示
echo ""
log "🚀 正在启动重构交易系统..."
echo -e "${YELLOW}按 Ctrl+C 可优雅停止系统${NC}"
echo ""

# 启动系统
python main_refactored.py "$CONFIG_FILE"