#!/usr/bin/env python3
"""
紧急切换到市价单模式
"""

import json
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))


def switch_to_market_mode():
    """修改配置文件，切换到市价单模式"""
    config_path = "config/config.json"
    
    try:
        # 读取配置文件
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # 修改订单模式
        if "AUTO_TRADER" in config:
            config["AUTO_TRADER"]["order_mode"] = "MARKET"
            print(f"成功切换到市价单模式 (MARKET)")
        else:
            print("错误: 未找到 AUTO_TRADER 配置")
            return False
        
        # 写回配置文件
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"配置文件 {config_path} 已更新")
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        return False


if __name__ == "__main__":
    print("正在切换到市价单模式...")
    success = switch_to_market_mode()
    
    if success:
        print("\n请重启交易系统以使更改生效")
        print("  pkill -f 'python.*auto_trader'")
        print("  nohup python3 -m quant.strategies.auto_trader > logs/auto_trader.log 2>&1 &")
    else:
        print("\n切换失败，请手动检查配置文件")
