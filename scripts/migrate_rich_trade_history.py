"""
Database Migration Script

Adds new columns for rich text trade history and daily performance tracking.
Run this script once to update the database schema.
"""

import json
import sqlite3
from pathlib import Path


def migrate_database():
    """Migrate database to add new columns for Story 3.1."""

    db_path = Path("data/trading_system.db")
    if not db_path.exists():
        print("Database file not found. Creating new database...")
        return

    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # Check if columns already exist
        cursor.execute("PRAGMA table_info(trade_history)")
        columns = [row[1] for row in cursor.fetchall()]

        # Add new columns if they don't exist
        new_columns = [
            ("decision_context", "TEXT"),
            ("entry_minutes", "INTEGER"),
            ("confirmed_indicators_json", "TEXT"),
        ]

        for column_name, column_type in new_columns:
            if column_name not in columns:
                print(f"Adding column: {column_name}")
                cursor.execute(
                    f"ALTER TABLE trade_history ADD COLUMN {column_name} {column_type}"
                )
            else:
                print(f"Column {column_name} already exists")

        # Create daily_performance table if it doesn't exist
        cursor.execute(
            """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='daily_performance'
        """
        )

        if not cursor.fetchone():
            print("Creating daily_performance table")
            cursor.execute(
                """
                CREATE TABLE daily_performance (
                    id INTEGER PRIMARY KEY,
                    date DATE UNIQUE NOT NULL,
                    total_trades INTEGER NOT NULL DEFAULT 0,
                    winning_trades INTEGER NOT NULL DEFAULT 0,
                    losing_trades INTEGER NOT NULL DEFAULT 0,
                    win_rate REAL NOT NULL DEFAULT 0.0,
                    total_pnl REAL NOT NULL DEFAULT 0.0,
                    generated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            # Create indexes
            cursor.execute(
                "CREATE INDEX idx_daily_performance_date ON daily_performance(date)"
            )
            cursor.execute(
                "CREATE INDEX idx_daily_performance_win_rate ON daily_performance(win_rate)"
            )
        else:
            print("daily_performance table already exists")

        # Commit changes
        conn.commit()
        print("Database migration completed successfully!")

    except Exception as e:
        print(f"Error during migration: {e}")
        conn.rollback()
    finally:
        conn.close()


def update_existing_trades():
    """Update existing trades with new rich text data."""

    db_path = Path("data/trading_system.db")
    if not db_path.exists():
        print("Database file not found.")
        return

    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # Get existing trades
        cursor.execute(
            "SELECT id, decision_details, confirmed_indicators FROM trade_history"
        )
        trades = cursor.fetchall()

        print(f"Updating {len(trades)} existing trades...")

        for trade_id, decision_details, confirmed_indicators in trades:
            try:
                # Parse existing data
                decision_context = {}
                if decision_details:
                    try:
                        decision_context = json.loads(decision_details)
                    except:
                        decision_context = {}

                # Update entry_minutes (default to 0 for existing trades)
                entry_minutes = 0

                # Update confirmed_indicators_json
                confirmed_indicators_data = {
                    "indicators": (
                        json.loads(confirmed_indicators) if confirmed_indicators else []
                    ),
                    "details": {},
                }

                # Update the trade
                cursor.execute(
                    """
                    UPDATE trade_history 
                    SET decision_context = ?,
                        entry_minutes = ?,
                        confirmed_indicators_json = ?
                    WHERE id = ?
                """,
                    (
                        json.dumps(decision_context),
                        entry_minutes,
                        json.dumps(confirmed_indicators_data),
                        trade_id,
                    ),
                )

            except Exception as e:
                print(f"Error updating trade {trade_id}: {e}")

        conn.commit()
        print("Existing trades updated successfully!")

    except Exception as e:
        print(f"Error updating existing trades: {e}")
        conn.rollback()
    finally:
        conn.close()


def verify_migration():
    """Verify that the migration was successful."""

    db_path = Path("data/trading_system.db")
    if not db_path.exists():
        print("Database file not found.")
        return

    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        # Check trade_history columns
        cursor.execute("PRAGMA table_info(trade_history)")
        columns = [row[1] for row in cursor.fetchall()]

        required_columns = [
            "decision_context",
            "entry_minutes",
            "confirmed_indicators_json",
        ]

        print("Trade History Schema Check:")
        for col in required_columns:
            status = "✓" if col in columns else "✗"
            print(f"  {status} {col}")

        # Check daily_performance table
        cursor.execute(
            """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='daily_performance'
        """
        )

        daily_perf_exists = cursor.fetchone() is not None
        print(f"\nDaily Performance Table: {'✓' if daily_perf_exists else '✗'}")

        # Check trade count
        cursor.execute("SELECT COUNT(*) FROM trade_history")
        trade_count = cursor.fetchone()[0]
        print(f"Total trades in database: {trade_count}")

        # Check daily performance count
        if daily_perf_exists:
            cursor.execute("SELECT COUNT(*) FROM daily_performance")
            perf_count = cursor.fetchone()[0]
            print(f"Daily performance records: {perf_count}")

        conn.close()

    except Exception as e:
        print(f"Error verifying migration: {e}")


if __name__ == "__main__":
    print("Starting database migration for Story 3.1...")

    # Step 1: Migrate database schema
    migrate_database()

    # Step 2: Update existing trades
    update_existing_trades()

    # Step 3: Verify migration
    verify_migration()

    print("\nMigration completed!")
