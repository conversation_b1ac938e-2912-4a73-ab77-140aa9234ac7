#!/usr/bin/env python3
"""
迁移前验证脚本

验证系统是否准备好进行生产迁移：
1. 数据完整性检查
2. 配置文件验证
3. 依赖项检查
4. 服务可用性测试
5. 新架构功能测试
"""

import asyncio
import sys
import json
import os
import subprocess
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple

from quant.utils.logger import get_logger
from quant.config_manager import ConfigManager

logger = get_logger(__name__)


class PreMigrationValidator:
    """迁移前验证器"""
    
    def __init__(self):
        self.validation_results: List[Dict[str, Any]] = []
        self.critical_failures = 0
        self.warnings = 0
        
    def add_result(self, category: str, test_name: str, passed: bool, 
                   message: str, details: Dict = None, critical: bool = False):
        """添加验证结果"""
        result = {
            "category": category,
            "test_name": test_name,
            "passed": passed,
            "message": message,
            "details": details or {},
            "critical": critical,
            "timestamp": datetime.now().isoformat()
        }
        
        self.validation_results.append(result)
        
        if not passed:
            if critical:
                self.critical_failures += 1
            else:
                self.warnings += 1
        
        # 实时输出结果
        status = "✅ PASS" if passed else ("❌ CRITICAL" if critical else "⚠️  WARNING")
        logger.info(f"{status} | {category} | {test_name}: {message}")
    
    async def validate_database_integrity(self):
        """验证数据库完整性"""
        try:
            from quant.database_manager import DatabaseManager
            
            db = DatabaseManager()
            
            # 检查数据库状态
            status = db.get_database_status()
            self.add_result(
                "Database", "Database Status",
                status['status'] == 'healthy',
                f"Database status: {status['status']}",
                status,
                critical=True
            )
            
            # 检查关键表是否存在
            tables = ['trade_signals', 'trade_results', 'system_logs']
            for table in tables:
                try:
                    result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                    count = result[0][0] if result else 0
                    self.add_result(
                        "Database", f"Table {table}",
                        True,
                        f"Table exists with {count} records",
                        {"table": table, "count": count}
                    )
                except Exception as e:
                    self.add_result(
                        "Database", f"Table {table}",
                        False,
                        f"Table check failed: {str(e)}",
                        {"table": table, "error": str(e)},
                        critical=True
                    )
            
            # 检查最近的交易记录
            try:
                recent_trades = db.get_recent_trades(limit=10)
                self.add_result(
                    "Database", "Recent Trades",
                    len(recent_trades) >= 0,
                    f"Found {len(recent_trades)} recent trades",
                    {"recent_trades_count": len(recent_trades)}
                )
            except Exception as e:
                self.add_result(
                    "Database", "Recent Trades",
                    False,
                    f"Failed to fetch recent trades: {str(e)}",
                    {"error": str(e)}
                )
            
        except Exception as e:
            self.add_result(
                "Database", "Database Connection",
                False,
                f"Database connection failed: {str(e)}",
                {"error": str(e)},
                critical=True
            )
    
    async def validate_configuration(self):
        """验证配置文件"""
        try:
            # 检查配置文件存在性
            config_files = ['config.json', 'config.development.json']
            for config_file in config_files:
                exists = Path(config_file).exists()
                self.add_result(
                    "Configuration", f"Config File {config_file}",
                    exists,
                    f"File {'exists' if exists else 'missing'}",
                    {"file": config_file, "exists": exists},
                    critical=(config_file == 'config.json')
                )
            
            # 验证主配置文件内容
            if Path('config.json').exists():
                try:
                    config = ConfigManager('config.json')
                    
                    # 检查必需的配置项
                    required_configs = [
                        ('BINANCE', 'API_KEY'),
                        ('BINANCE', 'SECRET_KEY'),
                        ('BINANCE', 'BASE_URL'),
                        ('DATABASE', 'PATH'),
                        ('RISK_MANAGEMENT', 'MAX_DAILY_LOSS'),
                        ('RISK_MANAGEMENT', 'POSITION_SIZE_PERCENT')
                    ]
                    
                    for section, key in required_configs:
                        try:
                            value = config.get(section, {}).get(key)
                            has_value = value is not None and value != ""
                            
                            self.add_result(
                                "Configuration", f"{section}.{key}",
                                has_value,
                                f"{'Configured' if has_value else 'Missing or empty'}",
                                {"section": section, "key": key, "has_value": has_value},
                                critical=(key in ['API_KEY', 'SECRET_KEY'])
                            )
                        except Exception as e:
                            self.add_result(
                                "Configuration", f"{section}.{key}",
                                False,
                                f"Configuration check failed: {str(e)}",
                                {"section": section, "key": key, "error": str(e)}
                            )
                
                except Exception as e:
                    self.add_result(
                        "Configuration", "Config Parsing",
                        False,
                        f"Failed to parse config.json: {str(e)}",
                        {"error": str(e)},
                        critical=True
                    )
        
        except Exception as e:
            self.add_result(
                "Configuration", "Configuration Validation",
                False,
                f"Configuration validation failed: {str(e)}",
                {"error": str(e)},
                critical=True
            )
    
    async def validate_dependencies(self):
        """验证Python依赖项"""
        try:
            # 检查requirements.txt
            req_file = Path('requirements.txt')
            if req_file.exists():
                self.add_result(
                    "Dependencies", "Requirements File",
                    True,
                    "requirements.txt exists",
                    {"file": "requirements.txt"}
                )
                
                # 检查关键依赖项
                critical_deps = [
                    'aiohttp',
                    'websockets', 
                    'numpy',
                    'pandas',
                    'python-dotenv',
                    'apscheduler'
                ]
                
                try:
                    with open(req_file, 'r') as f:
                        requirements = f.read()
                    
                    for dep in critical_deps:
                        if dep in requirements:
                            self.add_result(
                                "Dependencies", f"Package {dep}",
                                True,
                                f"Package {dep} listed in requirements",
                                {"package": dep}
                            )
                        else:
                            self.add_result(
                                "Dependencies", f"Package {dep}",
                                False,
                                f"Package {dep} missing from requirements",
                                {"package": dep},
                                critical=True
                            )
                            
                except Exception as e:
                    self.add_result(
                        "Dependencies", "Requirements Reading",
                        False,
                        f"Failed to read requirements.txt: {str(e)}",
                        {"error": str(e)}
                    )
            else:
                self.add_result(
                    "Dependencies", "Requirements File",
                    False,
                    "requirements.txt missing",
                    {"file": "requirements.txt"},
                    critical=True
                )
            
            # 检查已安装的包
            try:
                import aiohttp
                import websockets
                import numpy
                import pandas
                
                self.add_result(
                    "Dependencies", "Critical Packages",
                    True,
                    "All critical packages importable",
                    {}
                )
            except ImportError as e:
                self.add_result(
                    "Dependencies", "Critical Packages",
                    False,
                    f"Missing critical package: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
        
        except Exception as e:
            self.add_result(
                "Dependencies", "Dependency Validation",
                False,
                f"Dependency validation failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def validate_external_services(self):
        """验证外部服务连接"""
        try:
            from quant.binance_client import BinanceClient
            
            # 测试Binance API连接
            try:
                client = BinanceClient()
                await client.initialize()
                
                # 测试获取当前价格
                price = await client.get_current_price()
                if price and price > 0:
                    self.add_result(
                        "External Services", "Binance API",
                        True,
                        f"API connection successful, BTC price: ${price:,.2f}",
                        {"current_price": price}
                    )
                else:
                    self.add_result(
                        "External Services", "Binance API",
                        False,
                        "API connected but invalid price data",
                        {"price": price},
                        critical=True
                    )
                
                await client.close()
                
            except Exception as e:
                self.add_result(
                    "External Services", "Binance API",
                    False,
                    f"Binance API connection failed: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
            
            # 测试网络连接
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.get('https://api.binance.com/api/v3/ping', timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status == 200:
                            self.add_result(
                                "External Services", "Network Connectivity",
                                True,
                                "Network connection to Binance successful",
                                {"status_code": response.status}
                            )
                        else:
                            self.add_result(
                                "External Services", "Network Connectivity",
                                False,
                                f"Network connection returned status {response.status}",
                                {"status_code": response.status}
                            )
            except Exception as e:
                self.add_result(
                    "External Services", "Network Connectivity",
                    False,
                    f"Network connectivity test failed: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
        
        except Exception as e:
            self.add_result(
                "External Services", "Service Validation",
                False,
                f"External service validation failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def validate_new_architecture(self):
        """验证新架构组件"""
        try:
            from quant.trading_system_orchestrator import TradingSystemOrchestrator
            
            # 测试新架构初始化
            try:
                config = ConfigManager('config.json')
                orchestrator = TradingSystemOrchestrator(config)
                
                self.add_result(
                    "New Architecture", "Orchestrator Creation",
                    True,
                    "TradingSystemOrchestrator created successfully",
                    {}
                )
                
                # 测试服务容器
                container_status = orchestrator.service_container.get_container_status()
                self.add_result(
                    "New Architecture", "Service Container",
                    True,
                    f"Service container initialized with {container_status.get('total_services', 0)} services",
                    container_status
                )
                
                # 测试服务健康状态
                health_status = orchestrator.service_container.get_service_health_status()
                healthy_services = sum(1 for status in health_status.values() if status.get('running', False))
                total_services = len(health_status)
                
                self.add_result(
                    "New Architecture", "Service Health",
                    healthy_services == total_services,
                    f"Services health: {healthy_services}/{total_services} services ready",
                    health_status,
                    critical=(healthy_services < total_services)
                )
                
            except Exception as e:
                self.add_result(
                    "New Architecture", "Architecture Validation",
                    False,
                    f"New architecture validation failed: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
        
        except ImportError as e:
            self.add_result(
                "New Architecture", "Import Test",
                False,
                f"Cannot import new architecture components: {str(e)}",
                {"error": str(e)},
                critical=True
            )
        except Exception as e:
            self.add_result(
                "New Architecture", "Architecture Test",
                False,
                f"Architecture test failed: {str(e)}",
                {"error": str(e)},
                critical=True
            )
    
    async def validate_file_permissions(self):
        """验证文件权限"""
        try:
            critical_files = [
                'main.py',
                'main_refactored.py',
                'config.json',
                'data/trading_system.db'
            ]
            
            for file_path in critical_files:
                path = Path(file_path)
                if path.exists():
                    readable = os.access(path, os.R_OK)
                    writable = os.access(path, os.W_OK) if file_path.endswith('.db') else True
                    
                    self.add_result(
                        "File Permissions", f"File {file_path}",
                        readable and writable,
                        f"Permissions OK: read={readable}, write={writable}",
                        {"file": file_path, "readable": readable, "writable": writable},
                        critical=(not readable)
                    )
                else:
                    self.add_result(
                        "File Permissions", f"File {file_path}",
                        file_path.endswith('.db'),  # DB files may not exist yet
                        f"File {'missing' if not file_path.endswith('.db') else 'will be created'}",
                        {"file": file_path, "exists": False},
                        critical=(not file_path.endswith('.db'))
                    )
            
            # 检查日志目录
            logs_dir = Path('logs')
            if logs_dir.exists():
                writable = os.access(logs_dir, os.W_OK)
                self.add_result(
                    "File Permissions", "Logs Directory",
                    writable,
                    f"Logs directory writable: {writable}",
                    {"directory": "logs", "writable": writable}
                )
            else:
                try:
                    logs_dir.mkdir(exist_ok=True)
                    self.add_result(
                        "File Permissions", "Logs Directory",
                        True,
                        "Created logs directory",
                        {"directory": "logs", "created": True}
                    )
                except Exception as e:
                    self.add_result(
                        "File Permissions", "Logs Directory",
                        False,
                        f"Cannot create logs directory: {str(e)}",
                        {"directory": "logs", "error": str(e)},
                        critical=True
                    )
        
        except Exception as e:
            self.add_result(
                "File Permissions", "Permission Check",
                False,
                f"Permission validation failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def validate_system_resources(self):
        """验证系统资源"""
        try:
            import psutil
            
            # CPU检查
            cpu_percent = psutil.cpu_percent(interval=1)
            self.add_result(
                "System Resources", "CPU Usage",
                cpu_percent < 80,
                f"CPU usage: {cpu_percent:.1f}%",
                {"cpu_percent": cpu_percent},
                critical=(cpu_percent > 90)
            )
            
            # 内存检查
            memory = psutil.virtual_memory()
            self.add_result(
                "System Resources", "Memory Usage",
                memory.percent < 85,
                f"Memory usage: {memory.percent:.1f}%",
                {"memory_percent": memory.percent, "available_gb": memory.available / 1024 / 1024 / 1024},
                critical=(memory.percent > 95)
            )
            
            # 磁盘检查
            disk = psutil.disk_usage('.')
            self.add_result(
                "System Resources", "Disk Space",
                disk.percent < 90,
                f"Disk usage: {disk.percent:.1f}%",
                {"disk_percent": disk.percent, "free_gb": disk.free / 1024 / 1024 / 1024},
                critical=(disk.percent > 95)
            )
        
        except Exception as e:
            self.add_result(
                "System Resources", "Resource Check",
                False,
                f"System resource check failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def run_validation(self):
        """运行所有验证测试"""
        logger.info("🔍 开始迁移前验证...")
        logger.info("=" * 60)
        
        # 执行所有验证测试
        await self.validate_configuration()
        await self.validate_dependencies() 
        await self.validate_file_permissions()
        await self.validate_database_integrity()
        await self.validate_external_services()
        await self.validate_new_architecture()
        await self.validate_system_resources()
        
        # 生成验证报告
        self.generate_report()
        
        # 返回验证结果
        return self.critical_failures == 0
    
    def generate_report(self):
        """生成验证报告"""
        logger.info("=" * 60)
        logger.info("📊 验证结果汇总")
        logger.info("=" * 60)
        
        # 按类别统计
        categories = {}
        for result in self.validation_results:
            category = result['category']
            if category not in categories:
                categories[category] = {'total': 0, 'passed': 0, 'failed': 0, 'critical_failed': 0}
            
            categories[category]['total'] += 1
            if result['passed']:
                categories[category]['passed'] += 1
            else:
                categories[category]['failed'] += 1
                if result['critical']:
                    categories[category]['critical_failed'] += 1
        
        # 输出类别统计
        for category, stats in categories.items():
            status_icon = "✅" if stats['failed'] == 0 else ("❌" if stats['critical_failed'] > 0 else "⚠️")
            logger.info(f"{status_icon} {category}: {stats['passed']}/{stats['total']} passed, {stats['critical_failed']} critical failures")
        
        logger.info("=" * 60)
        
        # 总体结果
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for r in self.validation_results if r['passed'])
        
        logger.info(f"📈 总体结果: {passed_tests}/{total_tests} 测试通过")
        logger.info(f"⚠️  警告数量: {self.warnings}")
        logger.info(f"❌ 严重失败: {self.critical_failures}")
        
        # 迁移建议
        if self.critical_failures == 0:
            logger.info("✅ 系统已准备好进行迁移")
        else:
            logger.error("❌ 系统尚未准备好迁移，需要解决严重问题")
            logger.error("请修复以下严重问题后再进行迁移:")
            
            for result in self.validation_results:
                if not result['passed'] and result['critical']:
                    logger.error(f"  - {result['category']}: {result['test_name']} - {result['message']}")
        
        # 保存详细报告
        report_data = {
            "validation_time": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "warnings": self.warnings,
                "critical_failures": self.critical_failures,
                "ready_for_migration": self.critical_failures == 0
            },
            "categories": categories,
            "detailed_results": self.validation_results
        }
        
        report_file = f"pre_migration_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"📄 详细报告已保存: {report_file}")


async def main():
    """主函数"""
    logger.info("🔧 生产环境迁移前验证")
    logger.info("检查系统是否准备好从 main.py 迁移到 main_refactored.py")
    logger.info("=" * 60)
    
    validator = PreMigrationValidator()
    
    try:
        success = await validator.run_validation()
        
        if success:
            logger.info("🎉 验证成功！系统已准备好进行迁移")
            logger.info("运行以下命令开始迁移:")
            logger.info("  ./migrate_to_production.sh")
            return 0
        else:
            logger.error("❌ 验证失败！请修复严重问题后重新验证")
            logger.error("重新运行验证:")
            logger.error("  python validate_pre_migration.py")
            return 1
    
    except Exception as e:
        logger.error(f"验证过程出现错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))