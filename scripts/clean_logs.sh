#!/bin/bash
# 安全清理日志文件脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

echo ""
echo -e "${BLUE}=====================================================${NC}"
echo -e "${BLUE}🧹 日志文件清理工具${NC}"
echo -e "${BLUE}=====================================================${NC}"
echo ""

# 检查logs目录
if [ ! -d "logs" ]; then
    warn "logs目录不存在，无需清理"
    exit 0
fi

# 显示当前日志文件状态
info "当前日志文件状态:"
echo ""
du -sh logs/* 2>/dev/null | sort -hr || echo "  无日志文件"
echo ""

TOTAL_SIZE=$(du -sh logs/ | cut -f1)
info "总日志大小: $TOTAL_SIZE"
echo ""

# 询问用户确认
read -p "是否要清理旧日志文件？[y/N]: " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log "用户取消操作"
    exit 0
fi

# 创建备份目录（可选）
read -p "是否要先备份重要日志？[y/N]: " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    BACKUP_DIR="logs_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份最新的错误日志
    if [ -f "logs/error.log" ]; then
        cp "logs/error.log" "$BACKUP_DIR/"
        log "已备份 error.log 到 $BACKUP_DIR/"
    fi
    
    # 备份最新的服务日志  
    if [ -f "logs/service.log" ]; then
        cp "logs/service.log" "$BACKUP_DIR/"
        log "已备份 service.log 到 $BACKUP_DIR/"
    fi
    
    # 备份最新的交易系统日志
    if [ -f "logs/trading_system.log" ]; then
        cp "logs/trading_system.log" "$BACKUP_DIR/"
        log "已备份 trading_system.log 到 $BACKUP_DIR/"
    fi
    
    log "重要日志已备份到: $BACKUP_DIR"
fi

# 开始清理
log "开始清理日志文件..."

# 清理轮转的错误日志
DELETED_COUNT=0
FREED_SPACE=0

for file in logs/error.log.*; do
    if [ -f "$file" ]; then
        FILE_SIZE=$(du -k "$file" | cut -f1)
        rm "$file"
        DELETED_COUNT=$((DELETED_COUNT + 1))
        FREED_SPACE=$((FREED_SPACE + FILE_SIZE))
        log "已删除: $(basename "$file")"
    fi
done

# 清理测试日志
for file in logs/test_rotation.log*; do
    if [ -f "$file" ]; then
        FILE_SIZE=$(du -k "$file" | cut -f1)
        rm "$file"  
        DELETED_COUNT=$((DELETED_COUNT + 1))
        FREED_SPACE=$((FREED_SPACE + FILE_SIZE))
        log "已删除: $(basename "$file")"
    fi
done

# 清理空的主error.log（如果选择的话）
read -p "是否也要清理主error.log文件？[y/N]: " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "logs/error.log" ]; then
        FILE_SIZE=$(du -k "logs/error.log" | cut -f1)
        rm "logs/error.log"
        DELETED_COUNT=$((DELETED_COUNT + 1))
        FREED_SPACE=$((FREED_SPACE + FILE_SIZE))
        log "已删除: error.log"
    fi
fi

# 清理其他旧日志文件
read -p "是否要清理超过7天的其他日志文件？[y/N]: " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    OLD_LOGS=$(find logs/ -name "*.log" -type f -mtime +7 2>/dev/null || true)
    if [ ! -z "$OLD_LOGS" ]; then
        echo "$OLD_LOGS" | while read -r file; do
            if [ -f "$file" ]; then
                FILE_SIZE=$(du -k "$file" | cut -f1)
                rm "$file"
                DELETED_COUNT=$((DELETED_COUNT + 1))
                FREED_SPACE=$((FREED_SPACE + FILE_SIZE))
                log "已删除: $(basename "$file") (超过7天)"
            fi
        done
    else
        info "没有找到超过7天的旧日志文件"
    fi
fi

# 转换KB为人类可读格式
if [ $FREED_SPACE -gt 1048576 ]; then
    FREED_DISPLAY=$(echo "scale=1; $FREED_SPACE / 1048576" | bc -l 2>/dev/null || echo "$((FREED_SPACE / 1048576))")GB
elif [ $FREED_SPACE -gt 1024 ]; then
    FREED_DISPLAY=$(echo "scale=1; $FREED_SPACE / 1024" | bc -l 2>/dev/null || echo "$((FREED_SPACE / 1024))")MB
else
    FREED_DISPLAY="${FREED_SPACE}KB"
fi

echo ""
echo -e "${BLUE}=====================================================${NC}"
echo -e "${BLUE}📊 清理结果总结${NC}"
echo -e "${BLUE}=====================================================${NC}"

log "清理完成！"
log "删除文件数: $DELETED_COUNT"
log "释放空间: $FREED_DISPLAY"

# 显示清理后的状态
if [ -d "logs" ]; then
    echo ""
    info "清理后的日志目录:"
    ls -la logs/ || echo "  目录为空"
    echo ""
    REMAINING_SIZE=$(du -sh logs/ 2>/dev/null | cut -f1 || echo "0B")
    info "剩余日志大小: $REMAINING_SIZE"
fi

echo ""
log "🎉 日志清理完成！"
echo ""
info "建议:"
echo "  1. 定期运行此脚本清理日志"
echo "  2. 重要日志已备份，可随时查看"
echo "  3. 新系统会自动管理日志文件大小"