#!/usr/bin/env python3
"""
测试Binance API连接
验证API密钥和权限是否正确配置
"""

import asyncio
import sys
from pathlib import Path
import os

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from quant.binance_client import binance_client
from quant.utils.logger import get_logger

logger = get_logger(__name__)


async def test_connection():
    """测试API连接和权限"""
    
    print("=" * 60)
    print("Binance API 连接测试")
    print("=" * 60)
    
    # 检查环境变量
    env = os.getenv("ENVIRONMENT", "unknown")
    print(f"\n1. 环境设置: {env}")
    
    if env == "development":
        print("   ⚠️ 警告: 当前为开发环境，将使用模拟数据")
        print("   请设置 ENVIRONMENT=production 以测试真实API")
    
    # 检查API密钥
    api_key = os.getenv("BINANCE_ACCESS_KEY")
    if api_key:
        print(f"2. API密钥: {api_key[:10]}...{api_key[-4:]}")
    else:
        print("2. API密钥: ❌ 未找到")
        return False
    
    try:
        # 初始化客户端
        print("\n3. 初始化Binance客户端...")
        await binance_client.initialize()
        print("   ✅ 客户端初始化成功")
        
        # 测试获取服务器时间
        print("\n4. 测试服务器连接...")
        server_time = await binance_client.get_server_time()
        print(f"   ✅ 服务器时间: {server_time}")
        
        # 测试获取账户信息
        print("\n5. 测试账户权限...")
        account_info = await binance_client.get_account_info()
        
        if account_info:
            print(f"   ✅ 账户类型: {account_info.get('accountType', 'SPOT')}")
            print(f"   ✅ 是否可交易: {account_info.get('canTrade', False)}")
            
            # 检查权限
            permissions = account_info.get('permissions', [])
            print(f"   ✅ 账户权限: {', '.join(permissions)}")
            
            if 'SPOT' not in permissions and 'FUTURES' not in permissions:
                print("   ⚠️ 警告: 账户缺少交易权限")
        
        # 测试获取余额
        print("\n6. 测试获取余额...")
        usdt_balance = await binance_client.get_asset_balance("USDT")
        if usdt_balance:
            free = float(usdt_balance.get('free', 0))
            locked = float(usdt_balance.get('locked', 0))
            print(f"   ✅ USDT余额: {free:.2f} (可用) + {locked:.2f} (冻结)")
        
        # 测试获取价格
        print("\n7. 测试获取市场数据...")
        btc_price = await binance_client.get_current_price("BTCUSDT")
        print(f"   ✅ BTC当前价格: ${btc_price:,.2f}")
        
        # 测试获取订单簿
        print("\n8. 测试获取订单簿...")
        orderbook = await binance_client.get_orderbook("BTCUSDT", limit=5)
        if orderbook:
            bids = orderbook.get("bids", [])
            asks = orderbook.get("asks", [])
            if bids and asks:
                print(f"   ✅ 买一价: ${float(bids[0][0]):,.2f}")
                print(f"   ✅ 卖一价: ${float(asks[0][0]):,.2f}")
                spread = float(asks[0][0]) - float(bids[0][0])
                spread_bps = (spread / float(bids[0][0])) * 10000
                print(f"   ✅ 价差: ${spread:.2f} ({spread_bps:.1f} bps)")
        
        print("\n" + "=" * 60)
        print("✅ 所有测试通过！API连接正常")
        print("=" * 60)
        
        # 关闭连接
        await binance_client.close()
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print("\n故障排查建议:")
        print("1. 检查网络连接")
        print("2. 确认API密钥正确")
        print("3. 检查IP白名单设置")
        print("4. 确认API权限已启用")
        return False


async def main():
    """主函数"""
    success = await test_connection()
    
    if success:
        print("\n下一步:")
        print("1. 运行小额交易测试: python3 scripts/test_small_trade.py")
        print("2. 检查配置文件: cat config/config.json | grep MAKER_ORDER -A 10")
        print("3. 启动交易系统: python3 -m quant.strategies.auto_trader")
    else:
        print("\n请修复问题后重新运行测试")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
