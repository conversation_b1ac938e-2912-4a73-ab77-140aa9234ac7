#!/usr/bin/env python3
"""
迁移后验证脚本

验证新架构在生产环境中的运行状态：
1. 服务健康状态检查
2. 功能完整性验证
3. 性能指标对比
4. 数据一致性检查
5. 监控系统验证
"""

import asyncio
import sys
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import subprocess
import requests

from quant.utils.logger import get_logger
from quant.config_manager import ConfigManager

logger = get_logger(__name__)


class PostMigrationValidator:
    """迁移后验证器"""
    
    def __init__(self):
        self.validation_results: List[Dict[str, Any]] = []
        self.critical_failures = 0
        self.warnings = 0
        self.start_time = datetime.now()
        
    def add_result(self, category: str, test_name: str, passed: bool, 
                   message: str, details: Dict = None, critical: bool = False):
        """添加验证结果"""
        result = {
            "category": category,
            "test_name": test_name,
            "passed": passed,
            "message": message,
            "details": details or {},
            "critical": critical,
            "timestamp": datetime.now().isoformat()
        }
        
        self.validation_results.append(result)
        
        if not passed:
            if critical:
                self.critical_failures += 1
            else:
                self.warnings += 1
        
        # 实时输出结果
        status = "✅ PASS" if passed else ("❌ CRITICAL" if critical else "⚠️  WARNING")
        logger.info(f"{status} | {category} | {test_name}: {message}")
    
    async def validate_system_startup(self):
        """验证系统启动状态"""
        try:
            # 检查新系统进程
            try:
                with open('main_refactored.pid', 'r') as f:
                    pid = int(f.read().strip())
                
                # 检查进程是否存在
                try:
                    subprocess.check_call(['ps', '-p', str(pid)], 
                                        stdout=subprocess.DEVNULL, 
                                        stderr=subprocess.DEVNULL)
                    
                    self.add_result(
                        "System Startup", "Main Process",
                        True,
                        f"New system running (PID: {pid})",
                        {"pid": pid}
                    )
                except subprocess.CalledProcessError:
                    self.add_result(
                        "System Startup", "Main Process",
                        False,
                        f"Process {pid} not found",
                        {"pid": pid},
                        critical=True
                    )
                    
            except FileNotFoundError:
                self.add_result(
                    "System Startup", "PID File",
                    False,
                    "main_refactored.pid file not found",
                    {},
                    critical=True
                )
            
            # 检查监控系统进程
            try:
                with open('monitoring.pid', 'r') as f:
                    monitor_pid = int(f.read().strip())
                
                try:
                    subprocess.check_call(['ps', '-p', str(monitor_pid)], 
                                        stdout=subprocess.DEVNULL, 
                                        stderr=subprocess.DEVNULL)
                    
                    self.add_result(
                        "System Startup", "Monitoring Process",
                        True,
                        f"Monitoring system running (PID: {monitor_pid})",
                        {"monitor_pid": monitor_pid}
                    )
                except subprocess.CalledProcessError:
                    self.add_result(
                        "System Startup", "Monitoring Process",
                        False,
                        f"Monitoring process {monitor_pid} not found",
                        {"monitor_pid": monitor_pid}
                    )
                    
            except FileNotFoundError:
                self.add_result(
                    "System Startup", "Monitoring PID",
                    False,
                    "monitoring.pid file not found",
                    {}
                )
            
            # 检查日志文件
            log_file = "logs/main_refactored.log"
            try:
                with open(log_file, 'r') as f:
                    recent_logs = f.readlines()[-20:]  # 最近20行
                
                # 检查是否有严重错误
                error_lines = [line for line in recent_logs if any(
                    keyword in line.lower() 
                    for keyword in ['critical', 'fatal', 'error', 'exception']
                )]
                
                has_critical_errors = any(
                    keyword in line.lower() 
                    for line in error_lines 
                    for keyword in ['critical', 'fatal']
                )
                
                self.add_result(
                    "System Startup", "Log Analysis",
                    not has_critical_errors,
                    f"Log analysis: {len(error_lines)} error lines, critical errors: {has_critical_errors}",
                    {
                        "log_file": log_file,
                        "error_lines_count": len(error_lines),
                        "has_critical_errors": has_critical_errors
                    },
                    critical=has_critical_errors
                )
                
            except FileNotFoundError:
                self.add_result(
                    "System Startup", "Log File",
                    False,
                    f"Log file {log_file} not found",
                    {"log_file": log_file}
                )
        
        except Exception as e:
            self.add_result(
                "System Startup", "Startup Validation",
                False,
                f"Startup validation failed: {str(e)}",
                {"error": str(e)},
                critical=True
            )
    
    async def validate_service_health(self):
        """验证服务健康状态"""
        try:
            from quant.trading_system_orchestrator import TradingSystemOrchestrator
            
            # 测试服务容器状态
            try:
                config = ConfigManager('config.json')
                orchestrator = TradingSystemOrchestrator(config)
                
                # 获取容器状态
                container_status = orchestrator.service_container.get_container_status()
                enabled_services = container_status.get('enabled_services', 0)
                total_services = container_status.get('total_services', 0)
                
                self.add_result(
                    "Service Health", "Service Container",
                    enabled_services > 0,
                    f"Services: {enabled_services}/{total_services} enabled",
                    container_status,
                    critical=(enabled_services == 0)
                )
                
                # 获取服务健康状态
                health_status = orchestrator.service_container.get_service_health_status()
                healthy_count = sum(1 for status in health_status.values() 
                                  if status.get('running', False))
                total_count = len(health_status)
                
                self.add_result(
                    "Service Health", "Individual Services",
                    healthy_count == total_count and total_count > 0,
                    f"Services health: {healthy_count}/{total_count} running",
                    health_status,
                    critical=(healthy_count < total_count * 0.8)  # 至少80%服务运行
                )
                
                # 详细检查每个服务
                for service_name, status in health_status.items():
                    is_running = status.get('running', False)
                    self.add_result(
                        "Service Health", f"Service {service_name}",
                        is_running,
                        f"Service {service_name}: {'running' if is_running else 'not running'}",
                        status
                    )
                
            except Exception as e:
                self.add_result(
                    "Service Health", "Health Check",
                    False,
                    f"Service health check failed: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
        
        except Exception as e:
            self.add_result(
                "Service Health", "Service Validation",
                False,
                f"Service validation failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def validate_database_operations(self):
        """验证数据库操作"""
        try:
            from quant.database_manager import DatabaseManager
            
            db = DatabaseManager()
            
            # 基本连接测试
            status = db.get_database_status()
            self.add_result(
                "Database", "Connection Status",
                status['status'] == 'healthy',
                f"Database status: {status['status']}",
                status,
                critical=(status['status'] != 'healthy')
            )
            
            # 读取操作测试
            try:
                recent_trades = db.get_recent_trades(limit=5)
                self.add_result(
                    "Database", "Read Operations",
                    True,
                    f"Successfully read {len(recent_trades)} recent trades",
                    {"trades_count": len(recent_trades)}
                )
            except Exception as e:
                self.add_result(
                    "Database", "Read Operations",
                    False,
                    f"Read operation failed: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
            
            # 写入操作测试
            try:
                test_signal = {
                    'symbol': 'BTCUSDT',
                    'direction': 'test',
                    'entry_price': 50000.0,
                    'confidence': 0.5,
                    'signal_timestamp': datetime.now().isoformat(),
                    'analysis_only': True  # 测试信号
                }
                
                trade_id = db.save_trade_signal(test_signal)
                if trade_id:
                    self.add_result(
                        "Database", "Write Operations",
                        True,
                        f"Test signal saved with ID: {trade_id}",
                        {"test_trade_id": trade_id}
                    )
                    
                    # 清理测试数据
                    try:
                        db.execute_query("DELETE FROM trade_signals WHERE id = ?", (trade_id,))
                    except:
                        pass  # 清理失败不算严重错误
                else:
                    self.add_result(
                        "Database", "Write Operations",
                        False,
                        "Failed to save test signal",
                        {},
                        critical=True
                    )
                    
            except Exception as e:
                self.add_result(
                    "Database", "Write Operations",
                    False,
                    f"Write operation failed: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
        
        except Exception as e:
            self.add_result(
                "Database", "Database Validation",
                False,
                f"Database validation failed: {str(e)}",
                {"error": str(e)},
                critical=True
            )
    
    async def validate_api_connectivity(self):
        """验证API连接"""
        try:
            from quant.binance_client import BinanceClient
            
            # 测试Binance API连接
            try:
                client = BinanceClient()
                await client.initialize()
                
                # 测试价格获取
                start_time = time.time()
                price = await client.get_current_price()
                response_time = (time.time() - start_time) * 1000
                
                if price and price > 0:
                    self.add_result(
                        "API Connectivity", "Binance Price API",
                        True,
                        f"Price API working, BTC: ${price:,.2f} (response: {response_time:.0f}ms)",
                        {"price": price, "response_time_ms": response_time}
                    )
                else:
                    self.add_result(
                        "API Connectivity", "Binance Price API",
                        False,
                        "Price API returned invalid data",
                        {"price": price},
                        critical=True
                    )
                
                # 测试K线数据获取
                try:
                    start_time = time.time()
                    klines = await client.get_klines(interval="1m", limit=5)
                    response_time = (time.time() - start_time) * 1000
                    
                    if klines and len(klines) > 0:
                        self.add_result(
                            "API Connectivity", "Binance Kline API",
                            True,
                            f"Kline API working, got {len(klines)} klines (response: {response_time:.0f}ms)",
                            {"klines_count": len(klines), "response_time_ms": response_time}
                        )
                    else:
                        self.add_result(
                            "API Connectivity", "Binance Kline API",
                            False,
                            "Kline API returned no data",
                            {"klines": klines}
                        )
                
                except Exception as e:
                    self.add_result(
                        "API Connectivity", "Binance Kline API",
                        False,
                        f"Kline API failed: {str(e)}",
                        {"error": str(e)}
                    )
                
                await client.close()
                
            except Exception as e:
                self.add_result(
                    "API Connectivity", "Binance Connection",
                    False,
                    f"Binance API connection failed: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
        
        except Exception as e:
            self.add_result(
                "API Connectivity", "API Validation",
                False,
                f"API validation failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def validate_monitoring_system(self):
        """验证监控系统"""
        try:
            # 测试监控面板可访问性
            try:
                response = requests.get('http://localhost:8888/api/health', timeout=10)
                if response.status_code == 200:
                    health_data = response.json()
                    self.add_result(
                        "Monitoring", "Health API",
                        True,
                        f"Health API accessible, status: {health_data.get('status', 'unknown')}",
                        {"health_data": health_data}
                    )
                else:
                    self.add_result(
                        "Monitoring", "Health API",
                        False,
                        f"Health API returned status {response.status_code}",
                        {"status_code": response.status_code}
                    )
            except requests.exceptions.RequestException as e:
                self.add_result(
                    "Monitoring", "Health API",
                    False,
                    f"Cannot access health API: {str(e)}",
                    {"error": str(e)}
                )
            
            # 测试指标API
            try:
                response = requests.get('http://localhost:8888/api/metrics', timeout=10)
                if response.status_code == 200:
                    metrics_data = response.json()
                    self.add_result(
                        "Monitoring", "Metrics API",
                        True,
                        f"Metrics API accessible, got {len(metrics_data.get('recent_metrics', {}))} metrics",
                        {"metrics_count": len(metrics_data.get('recent_metrics', {}))}
                    )
                else:
                    self.add_result(
                        "Monitoring", "Metrics API",
                        False,
                        f"Metrics API returned status {response.status_code}",
                        {"status_code": response.status_code}
                    )
            except requests.exceptions.RequestException as e:
                self.add_result(
                    "Monitoring", "Metrics API",
                    False,
                    f"Cannot access metrics API: {str(e)}",
                    {"error": str(e)}
                )
            
            # 测试告警API
            try:
                response = requests.get('http://localhost:8888/api/alerts', timeout=10)
                if response.status_code == 200:
                    alerts_data = response.json()
                    alert_count = len(alerts_data.get('alerts', []))
                    self.add_result(
                        "Monitoring", "Alerts API",
                        True,
                        f"Alerts API accessible, {alert_count} active alerts",
                        {"active_alerts": alert_count}
                    )
                else:
                    self.add_result(
                        "Monitoring", "Alerts API",
                        False,
                        f"Alerts API returned status {response.status_code}",
                        {"status_code": response.status_code}
                    )
            except requests.exceptions.RequestException as e:
                self.add_result(
                    "Monitoring", "Alerts API",
                    False,
                    f"Cannot access alerts API: {str(e)}",
                    {"error": str(e)}
                )
            
            # 测试仪表板页面
            try:
                response = requests.get('http://localhost:8888/dashboard', timeout=10)
                if response.status_code == 200:
                    self.add_result(
                        "Monitoring", "Dashboard Page",
                        True,
                        "Dashboard page accessible",
                        {"dashboard_url": "http://localhost:8888/dashboard"}
                    )
                else:
                    self.add_result(
                        "Monitoring", "Dashboard Page",
                        False,
                        f"Dashboard returned status {response.status_code}",
                        {"status_code": response.status_code}
                    )
            except requests.exceptions.RequestException as e:
                self.add_result(
                    "Monitoring", "Dashboard Page",
                    False,
                    f"Cannot access dashboard: {str(e)}",
                    {"error": str(e)}
                )
        
        except Exception as e:
            self.add_result(
                "Monitoring", "Monitoring Validation",
                False,
                f"Monitoring validation failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def validate_functional_operations(self):
        """验证功能操作"""
        try:
            # 测试市场分析引擎
            try:
                from quant.simple_analysis_engine import SimpleAnalysisEngine
                
                engine = SimpleAnalysisEngine()
                start_time = time.time()
                signal = await engine.analyze_market()
                analysis_time = (time.time() - start_time) * 1000
                
                if signal is not None:
                    self.add_result(
                        "Functional", "Market Analysis",
                        True,
                        f"Analysis engine generated signal in {analysis_time:.0f}ms: {signal.get('direction', 'unknown')}",
                        {"signal": signal, "analysis_time_ms": analysis_time}
                    )
                else:
                    self.add_result(
                        "Functional", "Market Analysis",
                        True,  # 不生成信号也是正常的
                        f"Analysis engine ran successfully in {analysis_time:.0f}ms (no signal)",
                        {"analysis_time_ms": analysis_time}
                    )
                    
            except Exception as e:
                self.add_result(
                    "Functional", "Market Analysis",
                    False,
                    f"Market analysis failed: {str(e)}",
                    {"error": str(e)},
                    critical=True
                )
            
            # 测试实时数据管理器
            try:
                from quant.real_time_data_manager import RealTimeDataManager
                
                data_manager = RealTimeDataManager()
                await data_manager.initialize()
                
                # 测试数据流状态
                stream_status = data_manager.get_stream_status()
                self.add_result(
                    "Functional", "Real-time Data",
                    True,
                    f"Data manager initialized, streams: {stream_status.get('active_streams', 0)}",
                    stream_status
                )
                
                await data_manager.stop()
                
            except Exception as e:
                self.add_result(
                    "Functional", "Real-time Data",
                    False,
                    f"Real-time data manager failed: {str(e)}",
                    {"error": str(e)}
                )
            
            # 测试风险管理器
            try:
                from quant.risk_manager import RiskManager
                
                risk_manager = RiskManager()
                risk_report = risk_manager.get_risk_report()
                
                self.add_result(
                    "Functional", "Risk Management",
                    True,
                    f"Risk manager working, status: {risk_report.get('risk_status', 'unknown')}",
                    {"risk_status": risk_report.get('risk_status')}
                )
                
            except Exception as e:
                self.add_result(
                    "Functional", "Risk Management",
                    False,
                    f"Risk manager failed: {str(e)}",
                    {"error": str(e)}
                )
        
        except Exception as e:
            self.add_result(
                "Functional", "Functional Validation",
                False,
                f"Functional validation failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def validate_performance(self):
        """验证系统性能"""
        try:
            import psutil
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.add_result(
                "Performance", "CPU Usage",
                cpu_percent < 80,
                f"CPU usage: {cpu_percent:.1f}%",
                {"cpu_percent": cpu_percent},
                critical=(cpu_percent > 95)
            )
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.add_result(
                "Performance", "Memory Usage",
                memory.percent < 85,
                f"Memory usage: {memory.percent:.1f}%",
                {"memory_percent": memory.percent},
                critical=(memory.percent > 95)
            )
            
            # 系统负载
            try:
                load_avg = os.getloadavg()
                cpu_count = psutil.cpu_count()
                load_ratio = load_avg[0] / cpu_count if cpu_count > 0 else 0
                
                self.add_result(
                    "Performance", "System Load",
                    load_ratio < 2.0,
                    f"Load average: {load_avg[0]:.2f} (ratio: {load_ratio:.2f})",
                    {"load_avg": load_avg, "load_ratio": load_ratio}
                )
            except:
                pass  # 某些系统不支持load average
            
            # 检查进程内存使用
            try:
                with open('main_refactored.pid', 'r') as f:
                    pid = int(f.read().strip())
                
                process = psutil.Process(pid)
                memory_mb = process.memory_info().rss / 1024 / 1024
                cpu_percent = process.cpu_percent()
                
                self.add_result(
                    "Performance", "Process Resources",
                    memory_mb < 1000,  # 小于1GB
                    f"Process using {memory_mb:.1f}MB memory, {cpu_percent:.1f}% CPU",
                    {"memory_mb": memory_mb, "cpu_percent": cpu_percent},
                    critical=(memory_mb > 2000)  # 大于2GB算严重
                )
                
            except Exception as e:
                self.add_result(
                    "Performance", "Process Resources",
                    False,
                    f"Cannot check process resources: {str(e)}",
                    {"error": str(e)}
                )
        
        except Exception as e:
            self.add_result(
                "Performance", "Performance Validation",
                False,
                f"Performance validation failed: {str(e)}",
                {"error": str(e)}
            )
    
    async def run_validation(self, duration_minutes: int = 10):
        """运行所有验证测试"""
        logger.info("🔍 开始迁移后验证...")
        logger.info(f"验证时长: {duration_minutes} 分钟")
        logger.info("=" * 60)
        
        # 立即执行的验证
        await self.validate_system_startup()
        await self.validate_service_health()
        await self.validate_database_operations()
        await self.validate_api_connectivity()
        await self.validate_monitoring_system()
        await self.validate_functional_operations()
        await self.validate_performance()
        
        # 持续监控一段时间
        logger.info(f"⏱️  开始 {duration_minutes} 分钟持续监控...")
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        check_interval = 30  # 30秒检查一次
        check_count = 0
        
        while datetime.now() < end_time:
            check_count += 1
            await asyncio.sleep(check_interval)
            
            # 定期检查关键指标
            try:
                # 检查进程仍在运行
                with open('main_refactored.pid', 'r') as f:
                    pid = int(f.read().strip())
                
                try:
                    subprocess.check_call(['ps', '-p', str(pid)], 
                                        stdout=subprocess.DEVNULL, 
                                        stderr=subprocess.DEVNULL)
                    
                    if check_count % 4 == 0:  # 每2分钟记录一次
                        self.add_result(
                            "Continuous Monitor", f"Process Check {check_count}",
                            True,
                            f"System still running after {check_count * check_interval / 60:.1f} minutes",
                            {"check_count": check_count, "pid": pid}
                        )
                        
                except subprocess.CalledProcessError:
                    self.add_result(
                        "Continuous Monitor", f"Process Check {check_count}",
                        False,
                        f"Process {pid} died during monitoring",
                        {"check_count": check_count, "pid": pid},
                        critical=True
                    )
                    break
                    
            except Exception as e:
                self.add_result(
                    "Continuous Monitor", f"Monitor Check {check_count}",
                    False,
                    f"Monitoring check failed: {str(e)}",
                    {"error": str(e)}
                )
        
        # 生成最终报告
        self.generate_report()
        
        return self.critical_failures == 0
    
    def generate_report(self):
        """生成验证报告"""
        total_runtime = datetime.now() - self.start_time
        
        logger.info("=" * 60)
        logger.info("📊 迁移后验证结果汇总")
        logger.info("=" * 60)
        
        # 按类别统计
        categories = {}
        for result in self.validation_results:
            category = result['category']
            if category not in categories:
                categories[category] = {'total': 0, 'passed': 0, 'failed': 0, 'critical_failed': 0}
            
            categories[category]['total'] += 1
            if result['passed']:
                categories[category]['passed'] += 1
            else:
                categories[category]['failed'] += 1
                if result['critical']:
                    categories[category]['critical_failed'] += 1
        
        # 输出类别统计
        for category, stats in categories.items():
            status_icon = "✅" if stats['failed'] == 0 else ("❌" if stats['critical_failed'] > 0 else "⚠️")
            logger.info(f"{status_icon} {category}: {stats['passed']}/{stats['total']} passed, {stats['critical_failed']} critical failures")
        
        logger.info("=" * 60)
        
        # 总体结果
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for r in self.validation_results if r['passed'])
        
        logger.info(f"📈 总体结果: {passed_tests}/{total_tests} 测试通过")
        logger.info(f"⏱️  验证时长: {total_runtime.total_seconds():.1f} 秒")
        logger.info(f"⚠️  警告数量: {self.warnings}")
        logger.info(f"❌ 严重失败: {self.critical_failures}")
        
        # 迁移结果评估
        if self.critical_failures == 0:
            logger.info("✅ 迁移成功！新系统运行正常")
            logger.info("🌐 监控面板: http://localhost:8888/dashboard")
        else:
            logger.error("❌ 迁移存在问题，需要检查以下严重错误:")
            
            for result in self.validation_results:
                if not result['passed'] and result['critical']:
                    logger.error(f"  - {result['category']}: {result['test_name']} - {result['message']}")
        
        # 保存详细报告
        report_data = {
            "validation_time": datetime.now().isoformat(),
            "validation_duration_seconds": total_runtime.total_seconds(),
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "warnings": self.warnings,
                "critical_failures": self.critical_failures,
                "migration_successful": self.critical_failures == 0
            },
            "categories": categories,
            "detailed_results": self.validation_results
        }
        
        report_file = f"post_migration_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"📄 详细报告已保存: {report_file}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Production migration post-validation')
    parser.add_argument('--duration', type=int, default=10, 
                       help='Duration for continuous monitoring in minutes (default: 10)')
    args = parser.parse_args()
    
    logger.info("🔧 生产环境迁移后验证")
    logger.info("验证新架构在生产环境中的运行状态")
    logger.info("=" * 60)
    
    validator = PostMigrationValidator()
    
    try:
        success = await validator.run_validation(duration_minutes=args.duration)
        
        if success:
            logger.info("🎉 验证成功！新系统运行正常")
            logger.info("建议继续观察24小时以确保系统稳定")
            return 0
        else:
            logger.error("❌ 验证发现问题！可能需要回滚")
            logger.error("运行以下命令进行回滚:")
            logger.error("  ./migrate_to_production.sh --rollback")
            return 1
    
    except KeyboardInterrupt:
        logger.info("验证被用户中断")
        return 0
    except Exception as e:
        logger.error(f"验证过程出现错误: {e}")
        return 1


if __name__ == "__main__":
    import os
    sys.exit(asyncio.run(main()))