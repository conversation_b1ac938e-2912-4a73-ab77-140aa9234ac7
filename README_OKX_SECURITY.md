# OKX交易系统安全使用指南

## 🔒 API密钥安全管理

### ⚠️ 重要警告
**绝对不要在代码中硬编码API密钥！** 这是严重的安全风险。

### ✅ 正确的API密钥配置方法

#### 1. 使用.env文件（推荐）
在项目根目录创建`.env`文件：
```bash
# OKX API Credentials  
OKX_ACCESS_KEY=your_actual_access_key
OKX_SECRET_KEY=your_actual_secret_key
OKX_PASSPHRASE=your_actual_passphrase

# Environment Configuration
ENVIRONMENT=development
```

#### 2. 设置文件权限
```bash
chmod 600 .env  # 只有文件所有者可读写
```

#### 3. 确保.env文件不被提交到版本控制
检查`.gitignore`文件包含：
```
.env
*.env
```

### 🚀 安全启动系统

#### 方法1：使用安全启动脚本（推荐）
```bash
python start_okx_trading_system.py
```

#### 方法2：验证系统配置
```bash
python verify_okx_system.py
```

#### 方法3：运行测试
```bash
python test_complete_okx_system.py
```

## 📋 系统验证检查单

运行`verify_okx_system.py`会检查：

- ✅ API密钥是否从环境变量正确加载
- ✅ .env文件权限是否安全（600）
- ✅ 代码中是否存在硬编码的API密钥
- ✅ OKX配置是否一致
- ✅ API连接是否正常
- ✅ 系统状态是否健康

## ⚙️ 系统配置

### config.json中的安全配置
```json
{
  "OKX": {
    "API_KEY": "configured_via_env_vars",
    "SECRET_KEY": "configured_via_env_vars", 
    "PASSPHRASE": "configured_via_env_vars",
    "comment": "API credentials loaded from OKX_ACCESS_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE"
  },
  "EXCHANGES": {
    "default": "okx"
  },
  "AUTO_TRADER": {
    "exchange": "okx"
  }
}
```

### ConfigManager自动加载环境变量
系统使用`config_manager.py`的`get_platform_config()`方法：
```python
def get_platform_config(self, platform: str) -> dict[str, Any]:
    if platform.lower() == "okx":
        env_access_key = os.getenv("OKX_ACCESS_KEY")
        env_secret_key = os.getenv("OKX_SECRET_KEY")
        env_passphrase = os.getenv("OKX_PASSPHRASE")
        
        if env_access_key and env_secret_key and env_passphrase:
            platform_config["access_key"] = env_access_key
            platform_config["secret_key"] = env_secret_key
            platform_config["passphrase"] = env_passphrase
```

## 🛡️ 安全最佳实践

### 1. 环境变量优先级
1. 环境变量（最高优先级）
2. .env文件
3. 配置文件（不包含敏感信息）

### 2. 开发与生产环境隔离
- 开发环境：使用.env文件
- 生产环境：使用系统环境变量或密钥管理服务

### 3. 定期安全检查
```bash
# 每天运行一次安全验证
python verify_okx_system.py
```

### 4. 文件权限管理
```bash
# 设置正确的文件权限
chmod 600 .env
chmod 755 *.py
```

## 🚫 避免的错误做法

### ❌ 错误：硬编码API密钥
```python
# 错误！不要这样做
os.environ['OKX_ACCESS_KEY'] = '2cc32344-c0dc-42b0-a36c-f4df2e9b43fa'
os.environ['OKX_SECRET_KEY'] = 'E06C64E3874AC284CC0CB3162BB13C1E'
```

### ✅ 正确：从环境变量加载
```python
# 正确的做法
from dotenv import load_dotenv
load_dotenv()

# 系统自动从环境变量加载
okx_key = os.getenv('OKX_ACCESS_KEY')
```

## 📊 系统功能

### 核心功能
- ✅ OKX数据源集成
- ✅ 实时指标分析
- ✅ 交易信号生成
- ✅ 自动下单执行
- ✅ 价格一致性保障
- ✅ 风险管理
- ✅ 实时结算

### 价格一致性机制
系统确保：
- 入场信号价格 = 实时OKX价格
- 出场信号价格 = 实时OKX价格  
- 交易执行价格 = OKX API价格

### 监控和日志
- 实时系统监控
- 详细交易日志
- API调用跟踪
- 错误异常处理

## 🔧 故障排除

### 1. API密钥问题
```bash
# 检查环境变量
python -c "import os; print('OKX_ACCESS_KEY:', 'SET' if os.getenv('OKX_ACCESS_KEY') else 'NOT SET')"
```

### 2. 配置问题
```bash
# 验证配置
python test_okx_config.py
```

### 3. 连接问题
```bash
# 测试API连接
python verify_okx_system.py
```

## 📞 支持

如果遇到问题：
1. 运行`verify_okx_system.py`进行诊断
2. 检查`.env`文件配置
3. 确认网络连接正常
4. 查看日志文件：`logs/error.log`

---

**记住：安全是第一优先级！始终使用环境变量管理API密钥，永远不要硬编码敏感信息。**